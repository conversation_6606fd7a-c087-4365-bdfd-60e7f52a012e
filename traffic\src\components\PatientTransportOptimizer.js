import React, { useState, useEffect, useRef } from 'react';
import './PatientTransportOptimizer.css';

const PatientTransportOptimizer = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [optimizationRequests, setOptimizationRequests] = useState([
    {
      id: 'REQ001',
      patientName: '<PERSON><PERSON>',
      age: 45,
      condition: 'Cardiac Emergency',
      priority: 'critical',
      pickupLocation: { name: 'T Nagar Residence', coords: { lat: 13.0418, lng: 80.2341 } },
      destination: { name: 'Apollo Hospital', coords: { lat: 13.0358, lng: 80.2297 } },
      requestTime: new Date(Date.now() - 180000), // 3 minutes ago
      status: 'optimizing',
      requestMethod: 'mobile_app',
      contactNumber: '+91 98765 43210',
      optimizedRoute: null,
      assignedAmbulance: null
    },
    {
      id: 'REQ002',
      patientName: '<PERSON><PERSON>',
      age: 28,
      condition: 'Pregnancy Emergency',
      priority: 'high',
      pickupLocation: { name: 'Anna Nagar Apartment', coords: { lat: 13.0850, lng: 80.2101 } },
      destination: { name: 'Fortis Malar Hospital', coords: { lat: 13.0067, lng: 80.2206 } },
      requestTime: new Date(Date.now() - 300000), // 5 minutes ago
      status: 'dispatched',
      requestMethod: 'phone_call',
      contactNumber: '+91 87654 32109',
      optimizedRoute: { distance: '4.2 km', eta: '8 min', trafficScore: 0.7 },
      assignedAmbulance: 'AMB003'
    },
    {
      id: 'REQ003',
      patientName: 'Venkat Reddy',
      age: 67,
      condition: 'Stroke Symptoms',
      priority: 'critical',
      pickupLocation: { name: 'Velachery IT Park', coords: { lat: 12.9756, lng: 80.2207 } },
      destination: { name: 'MIOT International', coords: { lat: 12.9756, lng: 80.2207 } },
      requestTime: new Date(Date.now() - 120000), // 2 minutes ago
      status: 'en_route',
      requestMethod: 'family_app',
      contactNumber: '+91 76543 21098',
      optimizedRoute: { distance: '2.8 km', eta: '5 min', trafficScore: 0.9 },
      assignedAmbulance: 'AMB001'
    }
  ]);

  const [availableAmbulances, setAvailableAmbulances] = useState([
    { id: 'AMB001', location: { lat: 13.0827, lng: 80.2707 }, status: 'en_route', fuel: 85, equipment: ['AED', 'Ventilator'] },
    { id: 'AMB002', location: { lat: 13.0500, lng: 80.2824 }, status: 'available', fuel: 92, equipment: ['Trauma Kit', 'IV'] },
    { id: 'AMB003', location: { lat: 13.0418, lng: 80.2341 }, status: 'dispatched', fuel: 67, equipment: ['Pediatric Kit'] },
    { id: 'AMB004', location: { lat: 12.9756, lng: 80.2207 }, status: 'available', fuel: 78, equipment: ['Basic Life Support'] }
  ]);

  const [hospitalCapacity, setHospitalCapacity] = useState([
    { name: 'Apollo Hospital', capacity: 85, emergencyBeds: 12, waitTime: '5 min', specialties: ['Cardiac', 'Neuro'] },
    { name: 'Fortis Malar Hospital', capacity: 92, emergencyBeds: 8, waitTime: '3 min', specialties: ['Maternity', 'Pediatric'] },
    { name: 'MIOT International', capacity: 78, emergencyBeds: 15, waitTime: '7 min', specialties: ['Orthopedic', 'Cardiac'] },
    { name: 'Stanley Medical College', capacity: 65, emergencyBeds: 20, waitTime: '10 min', specialties: ['General', 'Trauma'] }
  ]);

  const [trafficData, setTrafficData] = useState({
    currentCongestion: 68,
    averageSpeed: 25,
    incidents: 4,
    optimalRoutes: 12
  });

  const [optimizationMetrics, setOptimizationMetrics] = useState({
    totalRequests: 247,
    averageResponseTime: 4.2,
    routeOptimizationSavings: 23,
    patientSatisfaction: 94.5,
    vehicleUtilization: 87.3
  });

  const mapRef = useRef(null);

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update traffic data
      setTrafficData(prev => ({
        ...prev,
        currentCongestion: Math.max(20, Math.min(95, prev.currentCongestion + (Math.random() - 0.5) * 5)),
        averageSpeed: Math.max(15, Math.min(45, prev.averageSpeed + (Math.random() - 0.5) * 3)),
        incidents: Math.max(0, prev.incidents + (Math.random() > 0.8 ? 1 : Math.random() < 0.2 ? -1 : 0))
      }));

      // Update hospital capacity
      setHospitalCapacity(prev => prev.map(hospital => ({
        ...hospital,
        capacity: Math.max(60, Math.min(100, hospital.capacity + (Math.random() - 0.5) * 5)),
        emergencyBeds: Math.max(5, Math.min(25, hospital.emergencyBeds + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)))
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Initialize Google Maps
  useEffect(() => {
    const initMap = () => {
      const newMap = new window.google.maps.Map(document.getElementById("transport-map"), {
        zoom: 12,
        center: { lat: 13.0827, lng: 80.2707 },
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
        styles: [
          { elementType: "geometry", stylers: [{ color: "#f5f5f5" }] },
          { elementType: "labels.text.fill", stylers: [{ color: "#616161" }] },
          { elementType: "labels.text.stroke", stylers: [{ color: "#f5f5f5" }] },
          { featureType: "road", elementType: "geometry", stylers: [{ color: "#ffffff" }] },
          { featureType: "road", elementType: "geometry.stroke", stylers: [{ color: "#e0e0e0" }] },
          { featureType: "road.highway", elementType: "geometry", stylers: [{ color: "#ffd54f" }] },
          { featureType: "water", elementType: "geometry", stylers: [{ color: "#c9c9c9" }] },
          { featureType: "poi", elementType: "geometry", stylers: [{ color: "#eeeeee" }] }
        ]
      });

      const trafficLayer = new window.google.maps.TrafficLayer();
      trafficLayer.setMap(newMap);

      mapRef.current = newMap;
    };

    const loadGoogleMapsScript = () => {
      if (!window.google || !window.google.maps) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);
        script.onload = initMap;
      } else {
        initMap();
      }
    };

    loadGoogleMapsScript();
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#dc2626';
      case 'high': return '#ea580c';
      case 'medium': return '#d97706';
      case 'low': return '#059669';
      default: return '#64748b';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'optimizing': return '#3b82f6';
      case 'dispatched': return '#f59e0b';
      case 'en_route': return '#ef4444';
      case 'completed': return '#10b981';
      default: return '#64748b';
    }
  };

  const simulateNewRequest = () => {
    const patients = ['Arun Kumar', 'Meera Devi', 'Suresh Babu', 'Lakshmi Priya'];
    const conditions = ['Heart Attack', 'Accident Injury', 'Breathing Difficulty', 'Severe Pain'];
    const priorities = ['critical', 'high', 'medium'];
    const methods = ['mobile_app', 'phone_call', 'family_app', 'web_portal'];
    
    const newRequest = {
      id: `REQ${String(Date.now()).slice(-3)}`,
      patientName: patients[Math.floor(Math.random() * patients.length)],
      age: 25 + Math.floor(Math.random() * 50),
      condition: conditions[Math.floor(Math.random() * conditions.length)],
      priority: priorities[Math.floor(Math.random() * priorities.length)],
      pickupLocation: { 
        name: 'Random Location', 
        coords: { 
          lat: 13.0827 + (Math.random() - 0.5) * 0.1, 
          lng: 80.2707 + (Math.random() - 0.5) * 0.1 
        } 
      },
      destination: hospitalCapacity[Math.floor(Math.random() * hospitalCapacity.length)],
      requestTime: new Date(),
      status: 'optimizing',
      requestMethod: methods[Math.floor(Math.random() * methods.length)],
      contactNumber: `+91 ${Math.floor(Math.random() * 90000) + 10000} ${Math.floor(Math.random() * 90000) + 10000}`,
      optimizedRoute: null,
      assignedAmbulance: null
    };

    setOptimizationRequests(prev => [newRequest, ...prev]);
    
    // Simulate optimization process
    setTimeout(() => {
      setOptimizationRequests(prev => prev.map(req => 
        req.id === newRequest.id 
          ? { 
              ...req, 
              status: 'dispatched',
              optimizedRoute: {
                distance: `${(Math.random() * 8 + 2).toFixed(1)} km`,
                eta: `${Math.floor(Math.random() * 15 + 3)} min`,
                trafficScore: Math.random()
              },
              assignedAmbulance: availableAmbulances.find(amb => amb.status === 'available')?.id || 'AMB002'
            }
          : req
      ));
    }, 3000);
  };

  return (
    <div className="transport-optimizer">
      <div className="optimizer-header">
        <div className="header-info">
          <h2>🚑 Patient Transport Optimization Agent</h2>
          <p>AI-powered ambulance dispatch with real-time route optimization</p>
        </div>
        <div className="header-stats">
          <div className="stat-item">
            <span className="stat-value">{optimizationMetrics.averageResponseTime}min</span>
            <span className="stat-label">Avg Response Time</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{optimizationMetrics.routeOptimizationSavings}%</span>
            <span className="stat-label">Time Savings</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{optimizationMetrics.vehicleUtilization}%</span>
            <span className="stat-label">Vehicle Utilization</span>
          </div>
        </div>
      </div>

      <div className="optimizer-content">
        {/* Left Panel - Requests and Controls */}
        <div className="left-panel">
          {/* Transport Requests */}
          <div className="panel transport-requests-panel">
            <div className="panel-header">
              <h3>📋 Transport Requests</h3>
              <button className="new-request-btn" onClick={simulateNewRequest}>
                + New Request
              </button>
            </div>
            <div className="requests-list">
              {optimizationRequests.map(request => (
                <div key={request.id} className={`request-card priority-${request.priority}`}>
                  <div className="request-header">
                    <span className="request-id">{request.id}</span>
                    <span className={`priority-badge ${request.priority}`}>
                      {request.priority.toUpperCase()}
                    </span>
                  </div>
                  <div className="request-details">
                    <div className="patient-info">
                      <strong>{request.patientName}</strong> ({request.age}y)
                    </div>
                    <div className="condition">{request.condition}</div>
                    <div className="locations">
                      <div>📍 From: {request.pickupLocation.name}</div>
                      <div>🏥 To: {request.destination.name}</div>
                    </div>
                    <div className="request-meta">
                      <div>📱 Via: {request.requestMethod.replace('_', ' ')}</div>
                      <div>⏰ {Math.floor((currentTime - request.requestTime) / 60000)} min ago</div>
                    </div>
                    <div className={`request-status status-${request.status}`}>
                      Status: {request.status.replace('_', ' ').toUpperCase()}
                    </div>
                    {request.optimizedRoute && (
                      <div className="optimized-route">
                        <div>🛣️ Route: {request.optimizedRoute.distance} • {request.optimizedRoute.eta}</div>
                        <div>🚑 Ambulance: {request.assignedAmbulance}</div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Optimization Metrics */}
          <div className="panel metrics-panel">
            <div className="panel-header">
              <h3>📊 Optimization Metrics</h3>
            </div>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-value">{optimizationMetrics.totalRequests}</div>
                <div className="metric-label">Total Requests</div>
                <div className="metric-trend positive">↗ +12% today</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{optimizationMetrics.patientSatisfaction}%</div>
                <div className="metric-label">Patient Satisfaction</div>
                <div className="metric-trend positive">↗ +2.3%</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{optimizationMetrics.routeOptimizationSavings}%</div>
                <div className="metric-label">Time Savings</div>
                <div className="metric-trend positive">↗ +5.1%</div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{optimizationMetrics.vehicleUtilization}%</div>
                <div className="metric-label">Vehicle Utilization</div>
                <div className="metric-trend neutral">→ Stable</div>
              </div>
            </div>
          </div>
        </div>

        {/* Center Panel - Map */}
        <div className="center-panel">
          <div className="panel map-panel">
            <div className="panel-header">
              <h3>🗺️ Real-Time Transport Map</h3>
              <div className="map-controls">
                <button className="map-btn active">Live Tracking</button>
                <button className="map-btn">Optimal Routes</button>
                <button className="map-btn">Traffic Layer</button>
              </div>
            </div>
            <div id="transport-map" className="transport-map"></div>
            <div className="map-legend">
              <div className="legend-item">
                <span className="legend-icon pickup">📍</span>
                <span>Pickup Location</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon hospital">🏥</span>
                <span>Hospital</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon ambulance-available">🚑</span>
                <span>Available Ambulance</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon ambulance-busy">🚑</span>
                <span>Busy Ambulance</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon route">━</span>
                <span>Optimized Route</span>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Analytics */}
        <div className="right-panel">
          {/* Hospital Capacity */}
          <div className="panel hospital-capacity-panel">
            <div className="panel-header">
              <h3>🏥 Hospital Capacity</h3>
            </div>
            <div className="hospital-list">
              {hospitalCapacity.map((hospital, index) => (
                <div key={index} className="hospital-card">
                  <div className="hospital-header">
                    <span className="hospital-name">{hospital.name}</span>
                    <span className={`capacity-indicator ${hospital.capacity > 90 ? 'high' : hospital.capacity > 75 ? 'medium' : 'low'}`}>
                      {hospital.capacity}%
                    </span>
                  </div>
                  <div className="hospital-details">
                    <div className="hospital-stat">
                      <span>Emergency Beds:</span>
                      <span>{hospital.emergencyBeds}</span>
                    </div>
                    <div className="hospital-stat">
                      <span>Wait Time:</span>
                      <span>{hospital.waitTime}</span>
                    </div>
                    <div className="hospital-specialties">
                      {hospital.specialties.map(specialty => (
                        <span key={specialty} className="specialty-tag">{specialty}</span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Traffic Analytics */}
          <div className="panel traffic-analytics-panel">
            <div className="panel-header">
              <h3>🚦 Traffic Analytics</h3>
            </div>
            <div className="traffic-stats">
              <div className="traffic-stat">
                <div className="stat-value">{trafficData.currentCongestion}%</div>
                <div className="stat-label">Current Congestion</div>
                <div className={`stat-trend ${trafficData.currentCongestion > 70 ? 'high' : 'normal'}`}>
                  {trafficData.currentCongestion > 70 ? '⚠️ High' : '✅ Normal'}
                </div>
              </div>
              <div className="traffic-stat">
                <div className="stat-value">{trafficData.averageSpeed} km/h</div>
                <div className="stat-label">Average Speed</div>
                <div className={`stat-trend ${trafficData.averageSpeed < 25 ? 'low' : 'normal'}`}>
                  {trafficData.averageSpeed < 25 ? '🐌 Slow' : '🚗 Normal'}
                </div>
              </div>
              <div className="traffic-stat">
                <div className="stat-value">{trafficData.incidents}</div>
                <div className="stat-label">Active Incidents</div>
                <div className={`stat-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`}>
                  {trafficData.incidents > 5 ? '⚠️ High' : '✅ Normal'}
                </div>
              </div>
              <div className="traffic-stat">
                <div className="stat-value">{trafficData.optimalRoutes}</div>
                <div className="stat-label">Optimal Routes</div>
                <div className="stat-trend positive">🎯 Active</div>
              </div>
            </div>
          </div>

          {/* Available Ambulances */}
          <div className="panel ambulances-panel">
            <div className="panel-header">
              <h3>🚑 Available Fleet</h3>
            </div>
            <div className="ambulances-list">
              {availableAmbulances.map(ambulance => (
                <div key={ambulance.id} className={`ambulance-card status-${ambulance.status}`}>
                  <div className="ambulance-header">
                    <span className="ambulance-id">{ambulance.id}</span>
                    <span className={`status-badge ${ambulance.status}`}>
                      {ambulance.status.toUpperCase()}
                    </span>
                  </div>
                  <div className="ambulance-details">
                    <div className="fuel-level">
                      <span>Fuel: {ambulance.fuel}%</span>
                      <div className="fuel-bar">
                        <div
                          className="fuel-fill"
                          style={{
                            width: `${ambulance.fuel}%`,
                            backgroundColor: ambulance.fuel < 30 ? '#ef4444' : '#10b981'
                          }}
                        ></div>
                      </div>
                    </div>
                    <div className="equipment-list">
                      {ambulance.equipment.map(item => (
                        <span key={item} className="equipment-tag">{item}</span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientTransportOptimizer;
