import pygame
import sys
import requests

# ESP8266 IP Address
ESP_IP = "http://***********"  # Replace with your ESP8266 IP on your network

def trigger_relay(state):
    try:
        if state == "on":
            requests.get(f"{ESP_IP}/on")
        elif state == "off":
            requests.get(f"{ESP_IP}/off")
    except Exception as e:
        print("Failed to send request:", e)

# Initialize pygame
pygame.init()

# Screen settings
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Vehicle Following a given path")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)

# Line storage
path = []
drawing = False
follower_index = 0

clock = pygame.time.Clock()
running = True
relay_triggered = False

while running:
    screen.fill(WHITE)

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            trigger_relay("off")  # Turn off relay on quit
            running = False

        elif event.type == pygame.MOUSEBUTTONDOWN:
            drawing = True
            path = []

        elif event.type == pygame.MOUSEBUTTONUP:
            drawing = False
            follower_index = 0
            relay_triggered = False

        elif event.type == pygame.MOUSEMOTION and drawing:
            path.append(event.pos)

    if len(path) > 1:
        pygame.draw.lines(screen, BLACK, False, path, 3)

    if path and follower_index < len(path):
        pygame.draw.circle(screen, RED, path[follower_index], 8)
        follower_index += 1

        if not relay_triggered:
            trigger_relay("on")  # Turn relay ON
            relay_triggered = True

    pygame.display.flip()
    clock.tick(60)

pygame.quit()
sys.exit()
