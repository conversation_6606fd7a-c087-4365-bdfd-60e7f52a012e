import React, { useState, useEffect, useRef } from 'react';
import './TrafficDashboard.css';
import { trafficRoutes, trafficZones } from '../routes';

const TrafficDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [trafficData, setTrafficData] = useState({
    totalVehicles: 45230,
    avgSpeed: 28,
    congestionLevel: 65,
    activeIncidents: 3,
    activeRoutes: 6
  });
  const [mapView, setMapView] = useState('traffic'); // 'traffic', 'routes', 'incidents'
  const mapRef = useRef(null);

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate real-time traffic data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setTrafficData(prev => ({
        ...prev,
        totalVehicles: prev.totalVehicles + Math.floor(Math.random() * 100 - 50),
        avgSpeed: Math.max(15, Math.min(50, prev.avgSpeed + Math.floor(Math.random() * 6 - 3))),
        congestionLevel: Math.max(30, Math.min(95, prev.congestionLevel + Math.floor(Math.random() * 10 - 5)))
      }));
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getDensityColor = (density) => {
    switch (density) {
      case 'very_high': return '#dc2626';
      case 'high': return '#f59e0b';
      case 'medium': return '#10b981';
      case 'low': return '#059669';
      default: return '#6b7280';
    }
  };

  const getCongestionStatus = (level) => {
    if (level >= 80) return { status: 'Critical', color: '#dc2626' };
    if (level >= 60) return { status: 'High', color: '#f59e0b' };
    if (level >= 40) return { status: 'Moderate', color: '#10b981' };
    return { status: 'Low', color: '#059669' };
  };

  const congestionStatus = getCongestionStatus(trafficData.congestionLevel);

  return (
    <div className="traffic-dashboard">
      {/* Header */}
      <div className="dashboard-header">
        <div className="header-left">
          <h1>🚦 Chennai Traffic Management System</h1>
          <div className="live-indicator">
            <span className="live-dot"></span>
            <span>LIVE - {formatTime(currentTime)}</span>
          </div>
        </div>
        <div className="header-right">
          <div className="view-controls">
            <button 
              className={`view-btn ${mapView === 'traffic' ? 'active' : ''}`}
              onClick={() => setMapView('traffic')}
            >
              🗺️ Traffic Density
            </button>
            <button 
              className={`view-btn ${mapView === 'routes' ? 'active' : ''}`}
              onClick={() => setMapView('routes')}
            >
              🛣️ Routes
            </button>
            <button 
              className={`view-btn ${mapView === 'incidents' ? 'active' : ''}`}
              onClick={() => setMapView('incidents')}
            >
              ⚠️ Incidents
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="dashboard-content">
        {/* Left Panel - Statistics */}
        <div className="stats-panel">
          <h3>📊 Real-time Statistics</h3>
          
          <div className="stat-card">
            <div className="stat-icon">🚗</div>
            <div className="stat-content">
              <div className="stat-value">{trafficData.totalVehicles.toLocaleString()}</div>
              <div className="stat-label">Total Vehicles</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">⚡</div>
            <div className="stat-content">
              <div className="stat-value">{trafficData.avgSpeed} km/h</div>
              <div className="stat-label">Average Speed</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon" style={{ color: congestionStatus.color }}>🚦</div>
            <div className="stat-content">
              <div className="stat-value" style={{ color: congestionStatus.color }}>
                {trafficData.congestionLevel}%
              </div>
              <div className="stat-label">Congestion Level</div>
              <div className="stat-status" style={{ color: congestionStatus.color }}>
                {congestionStatus.status}
              </div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">⚠️</div>
            <div className="stat-content">
              <div className="stat-value">{trafficData.activeIncidents}</div>
              <div className="stat-label">Active Incidents</div>
            </div>
          </div>

          <div className="stat-card">
            <div className="stat-icon">🛣️</div>
            <div className="stat-content">
              <div className="stat-value">{trafficData.activeRoutes}</div>
              <div className="stat-label">Monitored Routes</div>
            </div>
          </div>

          {/* Traffic Zones Legend */}
          <div className="legend-section">
            <h4>Traffic Density Legend</h4>
            <div className="legend-items">
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#dc2626' }}></div>
                <span>Very High (0-15 km/h)</span>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#f59e0b' }}></div>
                <span>High (15-30 km/h)</span>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#10b981' }}></div>
                <span>Medium (30-45 km/h)</span>
              </div>
              <div className="legend-item">
                <div className="legend-color" style={{ backgroundColor: '#059669' }}></div>
                <span>Low (45+ km/h)</span>
              </div>
            </div>
          </div>
        </div>

        {/* Center Panel - Map */}
        <div className="map-panel">
          <div className="map-container" ref={mapRef}>
            <div className="map-placeholder">
              <div className="map-overlay">
                <h2>🗺️ Chennai Traffic Map</h2>
                <p>Interactive traffic density visualization</p>
                <div className="map-info">
                  <span>Current View: {mapView.charAt(0).toUpperCase() + mapView.slice(1)}</span>
                </div>
              </div>
              
              {/* Simulated Traffic Zones */}
              <div className="traffic-zones">
                {trafficZones.map(zone => (
                  <div 
                    key={zone.id}
                    className="traffic-zone"
                    style={{
                      backgroundColor: zone.color + '40',
                      border: `2px solid ${zone.color}`,
                      position: 'absolute',
                      left: `${(zone.id - 1) * 30 + 10}%`,
                      top: `${(zone.id - 1) * 20 + 20}%`,
                      width: '25%',
                      height: '15%',
                      borderRadius: '8px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: zone.color
                    }}
                  >
                    {zone.name}
                  </div>
                ))}
              </div>

              {/* Route Overlays */}
              {mapView === 'routes' && (
                <div className="route-overlays">
                  {trafficRoutes.map(route => (
                    <div 
                      key={route.id}
                      className="route-line"
                      style={{
                        position: 'absolute',
                        left: `${route.id * 15}%`,
                        top: `${route.id * 12 + 10}%`,
                        width: '60%',
                        height: '4px',
                        backgroundColor: route.color,
                        borderRadius: '2px',
                        cursor: 'pointer'
                      }}
                      onClick={() => setSelectedRoute(route)}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Right Panel - Route Details */}
        <div className="details-panel">
          <h3>🛣️ Route Information</h3>
          
          {selectedRoute ? (
            <div className="route-details">
              <h4>{selectedRoute.name}</h4>
              <p>{selectedRoute.description}</p>
              
              <div className="route-stats">
                <div className="route-stat">
                  <span className="label">Distance:</span>
                  <span className="value">{selectedRoute.distance}</span>
                </div>
                <div className="route-stat">
                  <span className="label">Avg Speed:</span>
                  <span className="value">{selectedRoute.avgSpeed} km/h</span>
                </div>
                <div className="route-stat">
                  <span className="label">Congestion:</span>
                  <span className="value" style={{ color: selectedRoute.color }}>
                    {selectedRoute.congestionLevel}%
                  </span>
                </div>
                <div className="route-stat">
                  <span className="label">Est. Time:</span>
                  <span className="value">{selectedRoute.estimatedTime}</span>
                </div>
              </div>

              <div className="route-path">
                <h5>Route Path:</h5>
                <ul>
                  {selectedRoute.path.map((point, index) => (
                    <li key={index}>{point.name}</li>
                  ))}
                </ul>
              </div>

              <div className="peak-hours">
                <h5>Peak Hours:</h5>
                <div className="peak-times">
                  {selectedRoute.peakHours.map((time, index) => (
                    <span key={index} className="peak-time">{time}</span>
                  ))}
                </div>
              </div>
            </div>
          ) : (
            <div className="no-selection">
              <p>Click on a route to view details</p>
              
              <div className="route-list">
                <h4>Available Routes:</h4>
                {trafficRoutes.map(route => (
                  <div 
                    key={route.id}
                    className="route-item"
                    onClick={() => setSelectedRoute(route)}
                  >
                    <div 
                      className="route-color" 
                      style={{ backgroundColor: route.color }}
                    ></div>
                    <div className="route-info">
                      <div className="route-name">{route.name}</div>
                      <div className="route-summary">
                        {route.distance} • {route.estimatedTime}
                      </div>
                    </div>
                    <div 
                      className="congestion-badge"
                      style={{ backgroundColor: route.color + '20', color: route.color }}
                    >
                      {route.congestionLevel}%
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TrafficDashboard;
