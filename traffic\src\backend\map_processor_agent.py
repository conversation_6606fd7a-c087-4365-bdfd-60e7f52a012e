import sys
import cv2
import numpy as np
from PIL import ImageGrab, Image
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QPushButton, 
                           QVBoxLayout, QLabel, QFrame, QRubberBand, QScrollArea, QGridLayout, QHBoxLayout)
from PyQt5.QtCore import Qt, QRect, QPoint
from PyQt5.QtGui import QFont, QIcon, QPixmap, QImage
import pygame
import threading
import subprocess
import os

class ImageViewer(QMainWindow):
    def __init__(self, images_dict):
        super().__init__()
        self.images_dict = images_dict  # Store the images dictionary
        self.setWindowTitle("Map Analysis Gallery")
        self.setGeometry(100, 100, 1200, 800)
        self.current_lines = None  # Store detected lines

        # Create a central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)

        # Create a scroll area
        self.scroll = QScrollArea()
        self.main_layout.addWidget(self.scroll)
        self.scroll.setWidgetResizable(True)

        # Create a widget to hold the grid
        self.content_widget = QWidget()
        self.grid = QGridLayout(self.content_widget)
        self.scroll.setWidget(self.content_widget)

        self.show_gallery_view()
        self.show()

    def detect_lines_in_image(self, img):
        # Extract blue lines from the image
        if len(img.shape) == 3:
            # Create mask for blue color
            blue = img[:, :, 0]  # Blue channel
            green = img[:, :, 1]  # Green channel
            red = img[:, :, 2]    # Red channel
            
            # Create mask where blue is dominant
            blue_mask = (blue > 200) & (green < 100) & (red < 100)
            
            # Find contours of blue lines
            contours, _ = cv2.findContours(blue_mask.astype(np.uint8), cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
            
            # Convert contours to line segments
            lines = []
            for contour in contours:
                if len(contour) >= 2:
                    # Get start and end points of the contour
                    x1, y1 = contour[0][0]
                    x2, y2 = contour[-1][0]
                    lines.append([x1, y1, x2, y2])
            
            if not lines:
                return None
                
            return np.array([lines])
        
        return None

    def detect_road_path(self, img):
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
        
        # Define range for green color (roads in the detected roads image)
        lower_green = np.array([40, 40, 40])
        upper_green = np.array([80, 255, 255])
        
        # Create mask for green pixels
        mask = cv2.inRange(hsv, lower_green, upper_green)
        
        # Find contours of the road
        contours, _ = cv2.findContours(mask, cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None

        # Filter contours by minimum area to remove noise
        min_area = 100
        contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]
        
        if not contours:
            return None

        # Get all points from all contours
        all_points = []
        for contour in contours:
            # Simplify the contour
            epsilon = 0.02 * cv2.arcLength(contour, True)
            approx = cv2.approxPolyDP(contour, epsilon, False)
            all_points.extend(approx)

        # Convert points to a more manageable format
        points = np.array([pt[0] for pt in all_points])
        
        # Sort points to create a continuous path
        sorted_points = []
        remaining_points = points.tolist()
        
        # Start with the leftmost point
        start_point = min(remaining_points, key=lambda p: p[0])
        sorted_points.append(start_point)
        remaining_points.remove(start_point)
        
        while remaining_points:
            current = sorted_points[-1]
            min_dist = float('inf')
            next_point = None
            
            # Find the closest point
            for point in remaining_points:
                dist = np.sqrt((point[0] - current[0])**2 + (point[1] - current[1])**2)
                if dist < min_dist:
                    min_dist = dist
                    next_point = point
            
            if next_point is None or min_dist > 50:  # Maximum gap of 50 pixels
                # If the gap is too large, find a new starting point
                next_point = min(remaining_points, key=lambda p: p[0])
            
            sorted_points.append(next_point)
            remaining_points.remove(next_point)
        
        return np.array(sorted_points).reshape(-1, 1, 2)

    def start_route_simulation(self, img, lines):
        if lines is None:
            return
            
        # Create a new window for the simulation
        pygame.init()
        window_size = (1024, 768)  # Larger window for better visibility
        screen = pygame.display.set_mode(window_size)
        pygame.display.set_caption("Route Simulation")

        # Convert cv2 image to pygame surface for background
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img_scaled = cv2.resize(img_rgb, window_size, interpolation=cv2.INTER_AREA)
        background = pygame.surfarray.make_surface(img_scaled)
        background = pygame.transform.rotate(background, -90)
        background = pygame.transform.flip(background, True, False)

        # Convert lines to pygame coordinates
        scale_x = window_size[0] / img.shape[1]
        scale_y = window_size[1] / img.shape[0]
        
        # Create a list of line segments
        line_segments = []
        for line in lines[0]:
            x1, y1, x2, y2 = line
            start_point = (int(x1 * scale_x), int(y1 * scale_y))
            end_point = (int(x2 * scale_x), int(y2 * scale_y))
            line_segments.append((start_point, end_point))

        # Initialize vehicle position at the start of the first line segment
        if line_segments:
            vehicle_pos = line_segments[0][0]
            current_segment_index = 0
            current_target = line_segments[0][1]
            speed = 0.5  # Reduced speed for slower movement
            trail = []  # Store previous positions for trail effect
            trail_length = 20

        clock = pygame.time.Clock()
        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

            # Draw background
            screen.blit(background, (0, 0))

            # Draw all line segments
            line_surface = pygame.Surface(window_size, pygame.SRCALPHA)
            for start_pos, end_pos in line_segments:
                pygame.draw.aaline(line_surface, (0, 100, 255, 200), start_pos, end_pos, 3)
            screen.blit(line_surface, (0, 0))

            if line_segments:
                # Calculate direction to current target
                dx = current_target[0] - vehicle_pos[0]
                dy = current_target[1] - vehicle_pos[1]
                distance = (dx**2 + dy**2)**0.5

                if distance < speed:
                    # Reached current target, move to next segment
                    vehicle_pos = current_target
                    current_segment_index = (current_segment_index + 1) % len(line_segments)
                    
                    # Set new target as the end point of the next segment
                    current_target = line_segments[current_segment_index][1]
                    
                    # If we're starting a new segment, update vehicle position to the start of that segment
                    vehicle_pos = line_segments[current_segment_index][0]
                else:
                    # Move towards target
                    vehicle_pos = (
                        vehicle_pos[0] + (dx/distance * speed),
                        vehicle_pos[1] + (dy/distance * speed)
                    )

                # Update trail
                trail.append(vehicle_pos)
                if len(trail) > trail_length:
                    trail.pop(0)

                # Draw trail with fading effect
                trail_surface = pygame.Surface(window_size, pygame.SRCALPHA)
                for i, pos in enumerate(trail):
                    alpha = int(180 * (i / trail_length))
                    radius = int(3 * (i / trail_length)) + 2
                    pygame.draw.circle(trail_surface, (255, 0, 0, alpha), 
                                    (int(pos[0]), int(pos[1])), radius)
                screen.blit(trail_surface, (0, 0))

                # Draw vehicle
                vehicle_surface = pygame.Surface(window_size, pygame.SRCALPHA)
                pygame.draw.circle(vehicle_surface, (255, 0, 0, 255), 
                                (int(vehicle_pos[0]), int(vehicle_pos[1])), 5)
                screen.blit(vehicle_surface, (0, 0))

            pygame.display.flip()
            clock.tick(60)

        pygame.quit()

    def show_gallery_view(self):
        # Clear existing layout
        for i in reversed(range(self.grid.count())): 
            self.grid.itemAt(i).widget().setParent(None)

        # Add images to grid
        row = 0
        col = 0
        for title, img in self.images_dict.items():
            # Convert cv2 image to QPixmap
            if len(img.shape) == 2:  # Grayscale
                height, width = img.shape
                qimg = QImage(img.data, width, height, width, QImage.Format_Grayscale8)
            else:  # Color
                height, width, channel = img.shape
                bytes_per_line = 3 * width
                qimg = QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            
            # Create QPixmap and scale it
            pixmap = QPixmap.fromImage(qimg)
            scaled_pixmap = pixmap.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)

            # Create label for image
            image_label = QLabel()
            image_label.setPixmap(scaled_pixmap)
            image_label.setAlignment(Qt.AlignCenter)

            # Create label for title
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 5px;")

            # Create view larger button
            view_button = QPushButton("View Larger")
            view_button.setStyleSheet("""
                QPushButton {
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            view_button.clicked.connect(lambda checked, t=title: self.show_large_view(t))

            # Create a container for image and controls
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.addWidget(image_label)
            container_layout.addWidget(title_label)
            container_layout.addWidget(view_button)
            container.setStyleSheet("""
                QWidget {
                    background-color: white;
                    border: 1px solid #cccccc;
                    border-radius: 5px;
                    padding: 10px;
                    margin: 5px;
                }
            """)

            # Add to grid
            self.grid.addWidget(container, row, col)
            
            # Update grid position
            col += 1
            if col > 1:  # 2 images per row
                col = 0
                row += 1

    def show_large_view(self, title):
        # Clear existing layout
        for i in reversed(range(self.grid.count())): 
            self.grid.itemAt(i).widget().setParent(None)

        # Get the image
        img = self.images_dict[title]

        # Detect lines in the image
        self.current_lines = self.detect_lines_in_image(img)

        # Convert cv2 image to QPixmap
        if len(img.shape) == 2:  # Grayscale
            height, width = img.shape
            qimg = QImage(img.data, width, height, width, QImage.Format_Grayscale8)
        else:  # Color
            height, width, channel = img.shape
            bytes_per_line = 3 * width
            qimg = QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()

        # Create QPixmap and scale it
        pixmap = QPixmap.fromImage(qimg)
        scaled_pixmap = pixmap.scaled(1100, 700, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        # Create container for the large view
        container = QWidget()
        container_layout = QVBoxLayout(container)

        # Create label for title
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-weight: bold; font-size: 18px; margin: 10px;")

        # Create label for image
        image_label = QLabel()
        image_label.setPixmap(scaled_pixmap)
        image_label.setAlignment(Qt.AlignCenter)

        # Create button container
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)

        # Create back button
        back_button = QPushButton("Back to Gallery")
        back_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        back_button.clicked.connect(self.show_gallery_view)

        # Create simulate route button
        simulate_button = QPushButton("Simulate Route")
        simulate_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 150px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        simulate_button.clicked.connect(lambda: self.start_route_simulation(img, self.current_lines))

        # Add buttons to button container
        button_layout.addWidget(back_button)
        button_layout.addWidget(simulate_button)
        button_layout.setAlignment(Qt.AlignCenter)

        # Add widgets to container
        container_layout.addWidget(title_label)
        container_layout.addWidget(image_label)
        container_layout.addWidget(button_container)

        # Add container to grid
        self.grid.addWidget(container, 0, 0)

class MapScreenCapture(QWidget):
    def __init__(self, status_callback=None):
        super().__init__()
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.rubber_band = QRubberBand(QRubberBand.Rectangle, self)
        self.setWindowOpacity(0.3)
        self.showFullScreen()
        self.status_callback = status_callback

    def mousePressEvent(self, event):
        self.start_point = event.pos()
        self.rubber_band.setGeometry(QRect(self.start_point, self.start_point))
        self.rubber_band.show()

    def mouseMoveEvent(self, event):
        self.rubber_band.setGeometry(QRect(self.start_point, event.pos()).normalized())

    def mouseReleaseEvent(self, event):
        self.end_point = event.pos()
        self.capture_and_process()
        self.close()

    def update_status(self, message):
        if self.status_callback:
            self.status_callback(message)

    def remove_text(self, image):
        self.update_status("Removing text from image...")
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        mask = np.ones_like(image) * 255
        
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)
            aspect_ratio = float(w)/h
            area = cv2.contourArea(cnt)
            if area < 500 and (aspect_ratio > 0.2 and aspect_ratio < 5):
                cv2.drawContours(mask, [cnt], -1, (0, 0, 0), -1)
        
        result = cv2.bitwise_and(image, mask)
        return result

    def detect_roads(self, image):
        self.update_status("Detecting roads...")
        no_text_img = self.remove_text(image)
        hsv = cv2.cvtColor(no_text_img, cv2.COLOR_BGR2HSV)
        
        lower_white = np.array([0, 0, 200])
        upper_white = np.array([180, 30, 255])
        lower_yellow = np.array([20, 50, 180])
        upper_yellow = np.array([35, 255, 255])
        
        white_mask = cv2.inRange(hsv, lower_white, upper_white)
        yellow_mask = cv2.inRange(hsv, lower_yellow, upper_yellow)
        road_mask = cv2.bitwise_or(white_mask, yellow_mask)
        
        kernel = np.ones((3,3), np.uint8)
        road_mask = cv2.morphologyEx(road_mask, cv2.MORPH_CLOSE, kernel)
        road_mask = cv2.morphologyEx(road_mask, cv2.MORPH_OPEN, kernel)
        
        road_image = image.copy()
        road_image[road_mask > 0] = [0, 255, 0]
        
        return road_image, road_mask

    def detect_and_draw_lines(self, image, gray):
        self.update_status("Detecting line segments...")
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        kernel = np.ones((3,3), np.uint8)
        dilated = cv2.dilate(edges, kernel, iterations=1)
        
        lines = cv2.HoughLinesP(
            dilated,
            rho=1,
            theta=np.pi/180,
            threshold=50,
            minLineLength=30,
            maxLineGap=20
        )
        
        line_image = image.copy()
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                cv2.line(line_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            self.update_status(f"Number of line segments detected: {len(lines)}")
        
        return line_image, lines

    def capture_and_process(self):
        self.update_status("Capturing selected region...")
        # Capture the selected region
        rect = QRect(self.start_point, self.end_point)
        screen = QApplication.primaryScreen()
        original = screen.grabWindow(0, rect.x(), rect.y(), rect.width(), rect.height())
        original.save("original_map.png", "PNG")

        # Load and process images
        self.update_status("Processing captured image...")
        self.original_img = cv2.imread("original_map.png")
        self.grayscale_img = cv2.cvtColor(self.original_img, cv2.COLOR_BGR2GRAY)
        self.line_img, _ = self.detect_and_draw_lines(self.original_img, self.grayscale_img)
        self.road_img, self.road_mask = self.detect_roads(self.original_img)
        self.combined_img = cv2.addWeighted(self.line_img, 0.5, self.road_img, 0.5, 0)
        
        # Save all versions
        self.update_status("Saving processed images...")
        cv2.imwrite("grayscale_map.png", self.grayscale_img)
        cv2.imwrite("detected_lines_map.png", self.line_img)
        cv2.imwrite("detected_roads_map.png", self.road_img)
        cv2.imwrite("combined_view.png", self.combined_img)
        cv2.imwrite("road_mask.png", self.road_mask)

        # Create dictionary of images for gallery
        images_dict = {
            "Original Image": self.original_img,
            "Grayscale": self.grayscale_img,
            "Detected Lines": self.line_img,
            "Detected Roads": self.road_img,
            "Combined View": self.combined_img,
            "Road Mask": self.road_mask
        }

        # Show gallery
        self.update_status("Opening gallery view...")
        self.gallery = ImageViewer(images_dict)

class RouteSimulator(QMainWindow):
    def __init__(self, road_mask):
        super().__init__()
        self.road_mask = road_mask
        self.setWindowTitle("Route Simulator")
        self.setGeometry(100, 100, 800, 600)
        
        # Initialize pygame in a separate thread
        threading.Thread(target=self.run_simulation).start()

    def run_simulation(self):
        pygame.init()
        screen = pygame.display.set_mode((800, 600))
        pygame.display.set_caption("Vehicle Following a Route")

        # Colors
        WHITE = (255, 255, 255)
        BLACK = (0, 0, 0)
        RED = (255, 0, 0)

        # Convert road mask to path points
        path = []
        road_points = np.where(self.road_mask > 0)
        for y, x in zip(road_points[0], road_points[1]):
            path.append((x, y))

        # Sort path points to create a continuous route
        if path:
            sorted_path = [path[0]]
            remaining_points = path[1:]
            while remaining_points:
                current = sorted_path[-1]
                min_dist = float('inf')
                next_point = None
                next_index = None
                
                for i, point in enumerate(remaining_points):
                    dist = ((point[0] - current[0])**2 + (point[1] - current[1])**2)**0.5
                    if dist < min_dist:
                        min_dist = dist
                        next_point = point
                        next_index = i
                
                if next_point:
                    sorted_path.append(next_point)
                    remaining_points.pop(next_index)
                else:
                    break
            
            path = sorted_path

        follower_index = 0
        clock = pygame.time.Clock()

        running = True
        while running:
            screen.fill(WHITE)

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False

            # Draw the path
            if len(path) > 1:
                pygame.draw.lines(screen, BLACK, False, path, 3)

            # Move the follower along the path
            if path and follower_index < len(path):
                pygame.draw.circle(screen, RED, path[follower_index], 8)
                follower_index = (follower_index + 1) % len(path)  # Loop the movement

            pygame.display.flip()
            clock.tick(10)

        pygame.quit()

class MapProcessorAgent(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.screen_capture = None
        self.road_mask = None
        
    def initUI(self):
        # Set window properties
        self.setWindowTitle('Map Processing Agent')
        self.setGeometry(100, 100, 600, 500)  # Adjusted width since we removed one button
        
        # Create central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setAlignment(Qt.AlignCenter)
        
        # Add title
        title = QLabel('Map Processing Agent')
        title.setFont(QFont('Arial', 24, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)
        
        # Add description
        description = QLabel(
            'This tool will help you process Google Maps images:\n'
            '1. Capture selected area\n'
            '2. Convert to grayscale\n'
            '3. Detect road lines\n'
            '4. Remove text\n'
            '5. Show results in gallery view\n'
            '6. Run route simulation'
        )
        description.setFont(QFont('Arial', 12))
        description.setAlignment(Qt.AlignCenter)
        description.setWordWrap(True)
        layout.addWidget(description)
        
        # Add separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
        
        # Create button container with styling
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background-color: #f0f0f0;
                border-radius: 10px;
                padding: 20px;
            }
        """)
        button_layout = QVBoxLayout(button_container)
        
        # Create horizontal layout for buttons
        buttons_horizontal = QHBoxLayout()
        
        # Add process button
        self.process_button = QPushButton('Start Map Processing')
        self.process_button.setFont(QFont('Arial', 14))
        self.process_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 15px 32px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.process_button.clicked.connect(self.start_processing)
        buttons_horizontal.addWidget(self.process_button)
        
        # Add route simulation button
        self.route_button = QPushButton('Run Route Simulation')
        self.route_button.setFont(QFont('Arial', 14))
        self.route_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 15px 32px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #cccccc;
            }
        """)
        self.route_button.clicked.connect(self.start_route_simulation)
        self.route_button.setEnabled(False)  # Initially disabled
        buttons_horizontal.addWidget(self.route_button)
        
        # Add horizontal button layout to main button layout
        button_layout.addLayout(buttons_horizontal)
        
        # Add status label
        self.status_label = QLabel('Ready to process')
        self.status_label.setFont(QFont('Arial', 10))
        self.status_label.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(self.status_label)
        
        # Add button container to main layout
        layout.addWidget(button_container)
        
        # Add instructions
        instructions = QLabel(
            'Instructions:\n'
            '1. Open Google Maps in your browser\n'
            '2. Navigate to desired location\n'
            '3. Click "Start Map Processing"\n'
            '4. Select area by clicking and dragging\n'
            '5. After processing, click "Run Route Simulation"\n'
            '   to start the route simulation'
        )
        instructions.setFont(QFont('Arial', 10))
        instructions.setAlignment(Qt.AlignCenter)
        instructions.setStyleSheet('color: #666;')
        layout.addWidget(instructions)
        
        self.show()
    
    def update_status(self, message):
        """Update the status label with a new message"""
        self.status_label.setText(message)
        QApplication.processEvents()  # Ensure UI updates immediately
    
    def start_processing(self):
        """Start the map processing workflow"""
        try:
            self.status_label.setText('Processing... Please select area on map')
            self.process_button.setEnabled(False)
            self.route_button.setEnabled(False)
            QApplication.processEvents()  # Force UI update
            
            # Create and show the screen capture window
            self.screen_capture = MapScreenCapture(status_callback=self.update_status)
            self.screen_capture.show()  # Ensure the window is shown
            
            # Enable the route button after screen capture is complete
            self.process_button.setEnabled(True)
            self.route_button.setEnabled(True)
            self.status_label.setText('Processing complete! Click "Run Route Simulation" to continue.')
            
        except Exception as e:
            self.status_label.setText(f'Error: {str(e)}')
            self.process_button.setEnabled(True)
            self.route_button.setEnabled(False)
    
    def start_route_simulation(self):
        """Start the route simulation by running route.py"""
        try:
            self.status_label.setText('Starting route simulation...')
            route_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "route.py")
            
            # Run the route.py script in a new process
            process = subprocess.Popen([sys.executable, route_path], 
                                    stdout=subprocess.PIPE, 
                                    stderr=subprocess.PIPE)
            
            self.status_label.setText('Route simulation running...')
            
        except Exception as e:
            self.status_label.setText(f'Error running route simulation: {str(e)}')

def main():
    app = QApplication(sys.argv)
    
    # Set application style
    app.setStyle('Fusion')
    
    # Create and show the main window
    ex = MapProcessorAgent()
    sys.exit(app.exec_())

if __name__ == '__main__':
    main() 
