{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\components\\\\ARNavigation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport './ARNavigation.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ARNavigation = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isARActive, setIsARActive] = useState(false);\n  const [cameraStream, setCameraStream] = useState(null);\n  const [routeData, setRouteData] = useState(null);\n  const [navigationMode, setNavigationMode] = useState('route');\n  const [emergencyCall, setEmergencyCall] = useState(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [arFeatures, setArFeatures] = useState({\n    routeOverlay: true,\n    trafficSignals: true,\n    hazardDetection: true,\n    speedLimit: true,\n    hospitalMarkers: true\n  });\n\n  // Simulated emergency call data\n  const activeEmergency = {\n    id: \"EMG001\",\n    type: \"cardiac arrest\",\n    priority: \"critical\",\n    location: \"T Nagar Commercial Complex\",\n    coords: {\n      lat: 13.0418,\n      lng: 80.2341\n    },\n    destination: \"Apollo Hospital\",\n    destCoords: {\n      lat: 13.0358,\n      lng: 80.2297\n    },\n    estimatedTime: \"8 minutes\",\n    distance: \"3.2 km\"\n  };\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Initialize camera and AR\n  const startARNavigation = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: {\n          facingMode: 'environment',\n          width: {\n            ideal: 1280\n          },\n          height: {\n            ideal: 720\n          }\n        }\n      });\n      setCameraStream(stream);\n      setIsARActive(true);\n      setEmergencyCall(activeEmergency);\n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n\n      // Simulate route calculation\n      setTimeout(() => {\n        setRouteData({\n          totalDistance: \"3.2 km\",\n          estimatedTime: \"8 minutes\",\n          nextTurn: \"Turn right in 200m\",\n          currentSpeed: \"45 km/h\",\n          speedLimit: \"50 km/h\",\n          trafficStatus: \"moderate\"\n        });\n      }, 2000);\n    } catch (error) {\n      console.error('Error accessing camera:', error);\n      // Fallback to demo mode\n      setIsARActive(true);\n      setEmergencyCall(activeEmergency);\n      setRouteData({\n        totalDistance: \"3.2 km\",\n        estimatedTime: \"8 minutes\",\n        nextTurn: \"Turn right in 200m\",\n        currentSpeed: \"45 km/h\",\n        speedLimit: \"50 km/h\",\n        trafficStatus: \"moderate\"\n      });\n    }\n  };\n  const stopARNavigation = () => {\n    if (cameraStream) {\n      cameraStream.getTracks().forEach(track => track.stop());\n      setCameraStream(null);\n    }\n    setIsARActive(false);\n    setEmergencyCall(null);\n    setRouteData(null);\n  };\n  const toggleARFeature = feature => {\n    setArFeatures(prev => ({\n      ...prev,\n      [feature]: !prev[feature]\n    }));\n  };\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  // Simulated OpenCV processing\n  useEffect(() => {\n    if (isARActive && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      const drawAROverlays = () => {\n        // Clear canvas\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n        // Draw route arrow\n        if (arFeatures.routeOverlay && routeData) {\n          ctx.strokeStyle = '#00ff88';\n          ctx.lineWidth = 8;\n          ctx.lineCap = 'round';\n\n          // Draw navigation arrow\n          ctx.beginPath();\n          ctx.moveTo(canvas.width / 2, canvas.height - 100);\n          ctx.lineTo(canvas.width / 2, canvas.height - 200);\n          ctx.lineTo(canvas.width / 2 + 30, canvas.height - 170);\n          ctx.moveTo(canvas.width / 2, canvas.height - 200);\n          ctx.lineTo(canvas.width / 2 - 30, canvas.height - 170);\n          ctx.stroke();\n        }\n\n        // Draw traffic signal detection\n        if (arFeatures.trafficSignals) {\n          ctx.strokeStyle = '#ff4444';\n          ctx.lineWidth = 3;\n          ctx.strokeRect(50, 50, 100, 80);\n          ctx.fillStyle = '#ff4444';\n          ctx.font = '14px Arial';\n          ctx.fillText('STOP', 55, 100);\n        }\n\n        // Draw hospital marker\n        if (arFeatures.hospitalMarkers) {\n          ctx.fillStyle = '#dc2626';\n          ctx.font = '20px Arial';\n          ctx.fillText('🏥 Apollo Hospital', canvas.width - 200, 50);\n          ctx.strokeStyle = '#dc2626';\n          ctx.lineWidth = 2;\n          ctx.strokeRect(canvas.width - 210, 30, 180, 30);\n        }\n\n        // Draw speed limit\n        if (arFeatures.speedLimit && routeData) {\n          ctx.fillStyle = '#ffffff';\n          ctx.fillRect(canvas.width - 100, canvas.height - 100, 80, 80);\n          ctx.strokeStyle = '#ff0000';\n          ctx.lineWidth = 4;\n          ctx.strokeRect(canvas.width - 100, canvas.height - 100, 80, 80);\n          ctx.fillStyle = '#000000';\n          ctx.font = 'bold 16px Arial';\n          ctx.textAlign = 'center';\n          ctx.fillText('50', canvas.width - 60, canvas.height - 55);\n        }\n      };\n      const interval = setInterval(drawAROverlays, 100);\n      return () => clearInterval(interval);\n    }\n  }, [isARActive, arFeatures, routeData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"ar-navigation-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ar-navigation-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83E\\uDD7D AR Navigation Guide\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-time\",\n        children: formatTime(currentTime)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), !isARActive ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ar-setup-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ar-intro\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDE91 Augmented Reality Emergency Navigation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Advanced AR navigation system using OpenCV computer vision to provide real-time route guidance, traffic signal detection, and hazard identification for emergency responders.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ar-features-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ar-feature-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            children: \"\\uD83D\\uDDFA\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Real-time Route Overlay\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Dynamic route visualization with turn-by-turn directions overlaid on live camera feed\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ar-feature-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            children: \"\\uD83D\\uDEA6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Traffic Signal Detection\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"OpenCV-powered traffic light recognition with real-time status updates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ar-feature-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Hazard Identification\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Automatic detection of road hazards, obstacles, and emergency situations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ar-feature-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            children: \"\\uD83C\\uDFE5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Hospital Markers\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"AR markers showing nearby hospitals and emergency facilities\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"emergency-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"\\uD83D\\uDEA8 Active Emergency Call\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"emergency-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Emergency ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: activeEmergency.id\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Type:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value critical\",\n              children: activeEmergency.type\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Location:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: activeEmergency.location\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Destination:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: activeEmergency.destination\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"label\",\n              children: \"Distance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"value\",\n              children: activeEmergency.distance\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"start-ar-btn\",\n        onClick: startARNavigation,\n        children: \"\\uD83E\\uDD7D Start AR Navigation\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ar-active-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"ar-camera-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          ref: videoRef,\n          autoPlay: true,\n          playsInline: true,\n          muted: true,\n          className: \"ar-camera-feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          width: \"1280\",\n          height: \"720\",\n          className: \"ar-overlay-canvas\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ar-ui-overlays\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ar-status-bar\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"emergency-status\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"status-indicator critical\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"CRITICAL EMERGENCY - \", emergencyCall === null || emergencyCall === void 0 ? void 0 : emergencyCall.id]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ar-time\",\n              children: formatTime(currentTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), routeData && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ar-navigation-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"nav-instruction\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-icon\",\n                children: \"\\u27A1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"instruction-text\",\n                children: routeData.nextTurn\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 286,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"nav-stats\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Distance:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-value\",\n                  children: routeData.totalDistance\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"ETA:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-value\",\n                  children: routeData.estimatedTime\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 293,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"stat\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-label\",\n                  children: \"Speed:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"stat-value\",\n                  children: routeData.currentSpeed\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 288,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ar-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `ar-control-btn ${arFeatures.routeOverlay ? 'active' : ''}`,\n              onClick: () => toggleARFeature('routeOverlay'),\n              children: \"\\uD83D\\uDDFA\\uFE0F Route\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `ar-control-btn ${arFeatures.trafficSignals ? 'active' : ''}`,\n              onClick: () => toggleARFeature('trafficSignals'),\n              children: \"\\uD83D\\uDEA6 Signals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `ar-control-btn ${arFeatures.hazardDetection ? 'active' : ''}`,\n              onClick: () => toggleARFeature('hazardDetection'),\n              children: \"\\u26A0\\uFE0F Hazards\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `ar-control-btn ${arFeatures.hospitalMarkers ? 'active' : ''}`,\n              onClick: () => toggleARFeature('hospitalMarkers'),\n              children: \"\\uD83C\\uDFE5 Hospitals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"ar-control-btn stop-btn\",\n              onClick: stopARNavigation,\n              children: \"\\uD83D\\uDED1 Stop AR\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"ar-tech-specs\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDD27 Technical Specifications\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tech-specs-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spec-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Computer Vision\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"OpenCV 4.5+ Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Real-time Object Detection\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"YOLO v5 for Traffic Recognition\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Edge Computing Optimization\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 350,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spec-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"AR Rendering\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 355,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"WebGL-based 3D Overlays\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"60 FPS Real-time Rendering\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Spatial Tracking & Mapping\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Occlusion Handling\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spec-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Navigation Engine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Real-time Route Calculation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Traffic-aware Pathfinding\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Emergency Priority Routing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 369,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Multi-modal Transportation\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spec-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Hardware Requirements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"HD Camera (1080p minimum)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"GPS with RTK Precision\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"IMU Sensor Integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Edge AI Processing Unit\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(ARNavigation, \"4IHqW2kGxejRYm8PBOUaWUbIr1A=\");\n_c = ARNavigation;\nexport default ARNavigation;\nvar _c;\n$RefreshReg$(_c, \"ARNavigation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "ARNavigation", "_s", "currentTime", "setCurrentTime", "Date", "isARActive", "setIsARActive", "cameraStream", "setCameraStream", "routeData", "setRouteData", "navigationMode", "setNavigationMode", "emergencyCall", "setEmergencyCall", "videoRef", "canvasRef", "arFeatures", "setArFeatures", "routeOverlay", "trafficSignals", "hazardDetection", "speedLimit", "hospitalMarkers", "activeEmergency", "id", "type", "priority", "location", "coords", "lat", "lng", "destination", "destCoords", "estimatedTime", "distance", "timer", "setInterval", "clearInterval", "startARNavigation", "stream", "navigator", "mediaDevices", "getUserMedia", "video", "facingMode", "width", "ideal", "height", "current", "srcObject", "setTimeout", "totalDistance", "nextTurn", "currentSpeed", "trafficStatus", "error", "console", "stopARNavigation", "getTracks", "for<PERSON>ach", "track", "stop", "toggleARFeature", "feature", "prev", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "canvas", "ctx", "getContext", "drawAROverlays", "clearRect", "strokeStyle", "lineWidth", "lineCap", "beginPath", "moveTo", "lineTo", "stroke", "strokeRect", "fillStyle", "font", "fillText", "fillRect", "textAlign", "interval", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "ref", "autoPlay", "playsInline", "muted", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/components/ARNavigation.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport './ARNavigation.css';\n\nconst ARNavigation = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isARActive, setIsARActive] = useState(false);\n  const [cameraStream, setCameraStream] = useState(null);\n  const [routeData, setRouteData] = useState(null);\n  const [navigationMode, setNavigationMode] = useState('route');\n  const [emergencyCall, setEmergencyCall] = useState(null);\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n  const [arFeatures, setArFeatures] = useState({\n    routeOverlay: true,\n    trafficSignals: true,\n    hazardDetection: true,\n    speedLimit: true,\n    hospitalMarkers: true\n  });\n\n  // Simulated emergency call data\n  const activeEmergency = {\n    id: \"EMG001\",\n    type: \"cardiac arrest\",\n    priority: \"critical\",\n    location: \"T Nagar Commercial Complex\",\n    coords: { lat: 13.0418, lng: 80.2341 },\n    destination: \"Apollo Hospital\",\n    destCoords: { lat: 13.0358, lng: 80.2297 },\n    estimatedTime: \"8 minutes\",\n    distance: \"3.2 km\"\n  };\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Initialize camera and AR\n  const startARNavigation = async () => {\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia({\n        video: { \n          facingMode: 'environment',\n          width: { ideal: 1280 },\n          height: { ideal: 720 }\n        }\n      });\n      \n      setCameraStream(stream);\n      setIsARActive(true);\n      setEmergencyCall(activeEmergency);\n      \n      if (videoRef.current) {\n        videoRef.current.srcObject = stream;\n      }\n      \n      // Simulate route calculation\n      setTimeout(() => {\n        setRouteData({\n          totalDistance: \"3.2 km\",\n          estimatedTime: \"8 minutes\",\n          nextTurn: \"Turn right in 200m\",\n          currentSpeed: \"45 km/h\",\n          speedLimit: \"50 km/h\",\n          trafficStatus: \"moderate\"\n        });\n      }, 2000);\n      \n    } catch (error) {\n      console.error('Error accessing camera:', error);\n      // Fallback to demo mode\n      setIsARActive(true);\n      setEmergencyCall(activeEmergency);\n      setRouteData({\n        totalDistance: \"3.2 km\",\n        estimatedTime: \"8 minutes\",\n        nextTurn: \"Turn right in 200m\",\n        currentSpeed: \"45 km/h\",\n        speedLimit: \"50 km/h\",\n        trafficStatus: \"moderate\"\n      });\n    }\n  };\n\n  const stopARNavigation = () => {\n    if (cameraStream) {\n      cameraStream.getTracks().forEach(track => track.stop());\n      setCameraStream(null);\n    }\n    setIsARActive(false);\n    setEmergencyCall(null);\n    setRouteData(null);\n  };\n\n  const toggleARFeature = (feature) => {\n    setArFeatures(prev => ({\n      ...prev,\n      [feature]: !prev[feature]\n    }));\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  // Simulated OpenCV processing\n  useEffect(() => {\n    if (isARActive && canvasRef.current) {\n      const canvas = canvasRef.current;\n      const ctx = canvas.getContext('2d');\n      \n      const drawAROverlays = () => {\n        // Clear canvas\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        \n        // Draw route arrow\n        if (arFeatures.routeOverlay && routeData) {\n          ctx.strokeStyle = '#00ff88';\n          ctx.lineWidth = 8;\n          ctx.lineCap = 'round';\n          \n          // Draw navigation arrow\n          ctx.beginPath();\n          ctx.moveTo(canvas.width / 2, canvas.height - 100);\n          ctx.lineTo(canvas.width / 2, canvas.height - 200);\n          ctx.lineTo(canvas.width / 2 + 30, canvas.height - 170);\n          ctx.moveTo(canvas.width / 2, canvas.height - 200);\n          ctx.lineTo(canvas.width / 2 - 30, canvas.height - 170);\n          ctx.stroke();\n        }\n        \n        // Draw traffic signal detection\n        if (arFeatures.trafficSignals) {\n          ctx.strokeStyle = '#ff4444';\n          ctx.lineWidth = 3;\n          ctx.strokeRect(50, 50, 100, 80);\n          ctx.fillStyle = '#ff4444';\n          ctx.font = '14px Arial';\n          ctx.fillText('STOP', 55, 100);\n        }\n        \n        // Draw hospital marker\n        if (arFeatures.hospitalMarkers) {\n          ctx.fillStyle = '#dc2626';\n          ctx.font = '20px Arial';\n          ctx.fillText('🏥 Apollo Hospital', canvas.width - 200, 50);\n          ctx.strokeStyle = '#dc2626';\n          ctx.lineWidth = 2;\n          ctx.strokeRect(canvas.width - 210, 30, 180, 30);\n        }\n        \n        // Draw speed limit\n        if (arFeatures.speedLimit && routeData) {\n          ctx.fillStyle = '#ffffff';\n          ctx.fillRect(canvas.width - 100, canvas.height - 100, 80, 80);\n          ctx.strokeStyle = '#ff0000';\n          ctx.lineWidth = 4;\n          ctx.strokeRect(canvas.width - 100, canvas.height - 100, 80, 80);\n          ctx.fillStyle = '#000000';\n          ctx.font = 'bold 16px Arial';\n          ctx.textAlign = 'center';\n          ctx.fillText('50', canvas.width - 60, canvas.height - 55);\n        }\n      };\n      \n      const interval = setInterval(drawAROverlays, 100);\n      return () => clearInterval(interval);\n    }\n  }, [isARActive, arFeatures, routeData]);\n\n  return (\n    <div className=\"ar-navigation-container\">\n      <div className=\"ar-navigation-header\">\n        <h1>🥽 AR Navigation Guide</h1>\n        <div className=\"header-time\">{formatTime(currentTime)}</div>\n      </div>\n\n      {!isARActive ? (\n        <div className=\"ar-setup-section\">\n          <div className=\"ar-intro\">\n            <h2>🚑 Augmented Reality Emergency Navigation</h2>\n            <p>\n              Advanced AR navigation system using OpenCV computer vision to provide real-time \n              route guidance, traffic signal detection, and hazard identification for emergency responders.\n            </p>\n          </div>\n\n          <div className=\"ar-features-grid\">\n            <div className=\"ar-feature-card\">\n              <div className=\"feature-icon\">🗺️</div>\n              <h3>Real-time Route Overlay</h3>\n              <p>Dynamic route visualization with turn-by-turn directions overlaid on live camera feed</p>\n            </div>\n            \n            <div className=\"ar-feature-card\">\n              <div className=\"feature-icon\">🚦</div>\n              <h3>Traffic Signal Detection</h3>\n              <p>OpenCV-powered traffic light recognition with real-time status updates</p>\n            </div>\n            \n            <div className=\"ar-feature-card\">\n              <div className=\"feature-icon\">⚠️</div>\n              <h3>Hazard Identification</h3>\n              <p>Automatic detection of road hazards, obstacles, and emergency situations</p>\n            </div>\n            \n            <div className=\"ar-feature-card\">\n              <div className=\"feature-icon\">🏥</div>\n              <h3>Hospital Markers</h3>\n              <p>AR markers showing nearby hospitals and emergency facilities</p>\n            </div>\n          </div>\n\n          <div className=\"emergency-info\">\n            <h3>🚨 Active Emergency Call</h3>\n            <div className=\"emergency-details\">\n              <div className=\"emergency-item\">\n                <span className=\"label\">Emergency ID:</span>\n                <span className=\"value\">{activeEmergency.id}</span>\n              </div>\n              <div className=\"emergency-item\">\n                <span className=\"label\">Type:</span>\n                <span className=\"value critical\">{activeEmergency.type}</span>\n              </div>\n              <div className=\"emergency-item\">\n                <span className=\"label\">Location:</span>\n                <span className=\"value\">{activeEmergency.location}</span>\n              </div>\n              <div className=\"emergency-item\">\n                <span className=\"label\">Destination:</span>\n                <span className=\"value\">{activeEmergency.destination}</span>\n              </div>\n              <div className=\"emergency-item\">\n                <span className=\"label\">Distance:</span>\n                <span className=\"value\">{activeEmergency.distance}</span>\n              </div>\n            </div>\n          </div>\n\n          <button className=\"start-ar-btn\" onClick={startARNavigation}>\n            🥽 Start AR Navigation\n          </button>\n        </div>\n      ) : (\n        <div className=\"ar-active-section\">\n          <div className=\"ar-camera-container\">\n            <video\n              ref={videoRef}\n              autoPlay\n              playsInline\n              muted\n              className=\"ar-camera-feed\"\n            />\n            <canvas\n              ref={canvasRef}\n              width=\"1280\"\n              height=\"720\"\n              className=\"ar-overlay-canvas\"\n            />\n            \n            {/* AR UI Overlays */}\n            <div className=\"ar-ui-overlays\">\n              {/* Top Status Bar */}\n              <div className=\"ar-status-bar\">\n                <div className=\"emergency-status\">\n                  <span className=\"status-indicator critical\"></span>\n                  <span>CRITICAL EMERGENCY - {emergencyCall?.id}</span>\n                </div>\n                <div className=\"ar-time\">{formatTime(currentTime)}</div>\n              </div>\n\n              {/* Navigation Info */}\n              {routeData && (\n                <div className=\"ar-navigation-info\">\n                  <div className=\"nav-instruction\">\n                    <div className=\"instruction-icon\">➡️</div>\n                    <div className=\"instruction-text\">{routeData.nextTurn}</div>\n                  </div>\n                  <div className=\"nav-stats\">\n                    <div className=\"stat\">\n                      <span className=\"stat-label\">Distance:</span>\n                      <span className=\"stat-value\">{routeData.totalDistance}</span>\n                    </div>\n                    <div className=\"stat\">\n                      <span className=\"stat-label\">ETA:</span>\n                      <span className=\"stat-value\">{routeData.estimatedTime}</span>\n                    </div>\n                    <div className=\"stat\">\n                      <span className=\"stat-label\">Speed:</span>\n                      <span className=\"stat-value\">{routeData.currentSpeed}</span>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              {/* Bottom Controls */}\n              <div className=\"ar-controls\">\n                <button \n                  className={`ar-control-btn ${arFeatures.routeOverlay ? 'active' : ''}`}\n                  onClick={() => toggleARFeature('routeOverlay')}\n                >\n                  🗺️ Route\n                </button>\n                <button \n                  className={`ar-control-btn ${arFeatures.trafficSignals ? 'active' : ''}`}\n                  onClick={() => toggleARFeature('trafficSignals')}\n                >\n                  🚦 Signals\n                </button>\n                <button \n                  className={`ar-control-btn ${arFeatures.hazardDetection ? 'active' : ''}`}\n                  onClick={() => toggleARFeature('hazardDetection')}\n                >\n                  ⚠️ Hazards\n                </button>\n                <button \n                  className={`ar-control-btn ${arFeatures.hospitalMarkers ? 'active' : ''}`}\n                  onClick={() => toggleARFeature('hospitalMarkers')}\n                >\n                  🏥 Hospitals\n                </button>\n                <button className=\"ar-control-btn stop-btn\" onClick={stopARNavigation}>\n                  🛑 Stop AR\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Technical Specifications */}\n      <div className=\"ar-tech-specs\">\n        <h3>🔧 Technical Specifications</h3>\n        <div className=\"tech-specs-grid\">\n          <div className=\"spec-item\">\n            <h4>Computer Vision</h4>\n            <ul>\n              <li>OpenCV 4.5+ Integration</li>\n              <li>Real-time Object Detection</li>\n              <li>YOLO v5 for Traffic Recognition</li>\n              <li>Edge Computing Optimization</li>\n            </ul>\n          </div>\n          \n          <div className=\"spec-item\">\n            <h4>AR Rendering</h4>\n            <ul>\n              <li>WebGL-based 3D Overlays</li>\n              <li>60 FPS Real-time Rendering</li>\n              <li>Spatial Tracking & Mapping</li>\n              <li>Occlusion Handling</li>\n            </ul>\n          </div>\n          \n          <div className=\"spec-item\">\n            <h4>Navigation Engine</h4>\n            <ul>\n              <li>Real-time Route Calculation</li>\n              <li>Traffic-aware Pathfinding</li>\n              <li>Emergency Priority Routing</li>\n              <li>Multi-modal Transportation</li>\n            </ul>\n          </div>\n          \n          <div className=\"spec-item\">\n            <h4>Hardware Requirements</h4>\n            <ul>\n              <li>HD Camera (1080p minimum)</li>\n              <li>GPS with RTK Precision</li>\n              <li>IMU Sensor Integration</li>\n              <li>Edge AI Processing Unit</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ARNavigation;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5B,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,IAAIS,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACY,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,OAAO,CAAC;EAC7D,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAMoB,QAAQ,GAAGlB,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMmB,SAAS,GAAGnB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,YAAY,EAAE,IAAI;IAClBC,cAAc,EAAE,IAAI;IACpBC,eAAe,EAAE,IAAI;IACrBC,UAAU,EAAE,IAAI;IAChBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,eAAe,GAAG;IACtBC,EAAE,EAAE,QAAQ;IACZC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,4BAA4B;IACtCC,MAAM,EAAE;MAAEC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IACtCC,WAAW,EAAE,iBAAiB;IAC9BC,UAAU,EAAE;MAAEH,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IAC1CG,aAAa,EAAE,WAAW;IAC1BC,QAAQ,EAAE;EACZ,CAAC;;EAED;EACAvC,SAAS,CAAC,MAAM;IACd,MAAMwC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BlC,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMkC,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMC,SAAS,CAACC,YAAY,CAACC,YAAY,CAAC;QACvDC,KAAK,EAAE;UACLC,UAAU,EAAE,aAAa;UACzBC,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAC;UACtBC,MAAM,EAAE;YAAED,KAAK,EAAE;UAAI;QACvB;MACF,CAAC,CAAC;MAEFvC,eAAe,CAACgC,MAAM,CAAC;MACvBlC,aAAa,CAAC,IAAI,CAAC;MACnBQ,gBAAgB,CAACU,eAAe,CAAC;MAEjC,IAAIT,QAAQ,CAACkC,OAAO,EAAE;QACpBlC,QAAQ,CAACkC,OAAO,CAACC,SAAS,GAAGV,MAAM;MACrC;;MAEA;MACAW,UAAU,CAAC,MAAM;QACfzC,YAAY,CAAC;UACX0C,aAAa,EAAE,QAAQ;UACvBlB,aAAa,EAAE,WAAW;UAC1BmB,QAAQ,EAAE,oBAAoB;UAC9BC,YAAY,EAAE,SAAS;UACvBhC,UAAU,EAAE,SAAS;UACrBiC,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IAEV,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C;MACAlD,aAAa,CAAC,IAAI,CAAC;MACnBQ,gBAAgB,CAACU,eAAe,CAAC;MACjCd,YAAY,CAAC;QACX0C,aAAa,EAAE,QAAQ;QACvBlB,aAAa,EAAE,WAAW;QAC1BmB,QAAQ,EAAE,oBAAoB;QAC9BC,YAAY,EAAE,SAAS;QACvBhC,UAAU,EAAE,SAAS;QACrBiC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAInD,YAAY,EAAE;MAChBA,YAAY,CAACoD,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;MACvDtD,eAAe,CAAC,IAAI,CAAC;IACvB;IACAF,aAAa,CAAC,KAAK,CAAC;IACpBQ,gBAAgB,CAAC,IAAI,CAAC;IACtBJ,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;EAED,MAAMqD,eAAe,GAAIC,OAAO,IAAK;IACnC9C,aAAa,CAAC+C,IAAI,KAAK;MACrB,GAAGA,IAAI;MACP,CAACD,OAAO,GAAG,CAACC,IAAI,CAACD,OAAO;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;;EAED;EACA5E,SAAS,CAAC,MAAM;IACd,IAAIS,UAAU,IAAIW,SAAS,CAACiC,OAAO,EAAE;MACnC,MAAMwB,MAAM,GAAGzD,SAAS,CAACiC,OAAO;MAChC,MAAMyB,GAAG,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;MAEnC,MAAMC,cAAc,GAAGA,CAAA,KAAM;QAC3B;QACAF,GAAG,CAACG,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEJ,MAAM,CAAC3B,KAAK,EAAE2B,MAAM,CAACzB,MAAM,CAAC;;QAEhD;QACA,IAAI/B,UAAU,CAACE,YAAY,IAAIV,SAAS,EAAE;UACxCiE,GAAG,CAACI,WAAW,GAAG,SAAS;UAC3BJ,GAAG,CAACK,SAAS,GAAG,CAAC;UACjBL,GAAG,CAACM,OAAO,GAAG,OAAO;;UAErB;UACAN,GAAG,CAACO,SAAS,CAAC,CAAC;UACfP,GAAG,CAACQ,MAAM,CAACT,MAAM,CAAC3B,KAAK,GAAG,CAAC,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,CAAC;UACjD0B,GAAG,CAACS,MAAM,CAACV,MAAM,CAAC3B,KAAK,GAAG,CAAC,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,CAAC;UACjD0B,GAAG,CAACS,MAAM,CAACV,MAAM,CAAC3B,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,CAAC;UACtD0B,GAAG,CAACQ,MAAM,CAACT,MAAM,CAAC3B,KAAK,GAAG,CAAC,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,CAAC;UACjD0B,GAAG,CAACS,MAAM,CAACV,MAAM,CAAC3B,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,CAAC;UACtD0B,GAAG,CAACU,MAAM,CAAC,CAAC;QACd;;QAEA;QACA,IAAInE,UAAU,CAACG,cAAc,EAAE;UAC7BsD,GAAG,CAACI,WAAW,GAAG,SAAS;UAC3BJ,GAAG,CAACK,SAAS,GAAG,CAAC;UACjBL,GAAG,CAACW,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;UAC/BX,GAAG,CAACY,SAAS,GAAG,SAAS;UACzBZ,GAAG,CAACa,IAAI,GAAG,YAAY;UACvBb,GAAG,CAACc,QAAQ,CAAC,MAAM,EAAE,EAAE,EAAE,GAAG,CAAC;QAC/B;;QAEA;QACA,IAAIvE,UAAU,CAACM,eAAe,EAAE;UAC9BmD,GAAG,CAACY,SAAS,GAAG,SAAS;UACzBZ,GAAG,CAACa,IAAI,GAAG,YAAY;UACvBb,GAAG,CAACc,QAAQ,CAAC,oBAAoB,EAAEf,MAAM,CAAC3B,KAAK,GAAG,GAAG,EAAE,EAAE,CAAC;UAC1D4B,GAAG,CAACI,WAAW,GAAG,SAAS;UAC3BJ,GAAG,CAACK,SAAS,GAAG,CAAC;UACjBL,GAAG,CAACW,UAAU,CAACZ,MAAM,CAAC3B,KAAK,GAAG,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC;QACjD;;QAEA;QACA,IAAI7B,UAAU,CAACK,UAAU,IAAIb,SAAS,EAAE;UACtCiE,GAAG,CAACY,SAAS,GAAG,SAAS;UACzBZ,GAAG,CAACe,QAAQ,CAAChB,MAAM,CAAC3B,KAAK,GAAG,GAAG,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;UAC7D0B,GAAG,CAACI,WAAW,GAAG,SAAS;UAC3BJ,GAAG,CAACK,SAAS,GAAG,CAAC;UACjBL,GAAG,CAACW,UAAU,CAACZ,MAAM,CAAC3B,KAAK,GAAG,GAAG,EAAE2B,MAAM,CAACzB,MAAM,GAAG,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;UAC/D0B,GAAG,CAACY,SAAS,GAAG,SAAS;UACzBZ,GAAG,CAACa,IAAI,GAAG,iBAAiB;UAC5Bb,GAAG,CAACgB,SAAS,GAAG,QAAQ;UACxBhB,GAAG,CAACc,QAAQ,CAAC,IAAI,EAAEf,MAAM,CAAC3B,KAAK,GAAG,EAAE,EAAE2B,MAAM,CAACzB,MAAM,GAAG,EAAE,CAAC;QAC3D;MACF,CAAC;MAED,MAAM2C,QAAQ,GAAGtD,WAAW,CAACuC,cAAc,EAAE,GAAG,CAAC;MACjD,OAAO,MAAMtC,aAAa,CAACqD,QAAQ,CAAC;IACtC;EACF,CAAC,EAAE,CAACtF,UAAU,EAAEY,UAAU,EAAER,SAAS,CAAC,CAAC;EAEvC,oBACEV,OAAA;IAAK6F,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC9F,OAAA;MAAK6F,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnC9F,OAAA;QAAA8F,QAAA,EAAI;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC/BlG,OAAA;QAAK6F,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAE3B,UAAU,CAAChE,WAAW;MAAC;QAAA4F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,EAEL,CAAC5F,UAAU,gBACVN,OAAA;MAAK6F,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/B9F,OAAA;QAAK6F,SAAS,EAAC,UAAU;QAAAC,QAAA,gBACvB9F,OAAA;UAAA8F,QAAA,EAAI;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClDlG,OAAA;UAAA8F,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENlG,OAAA;QAAK6F,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9F,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvClG,OAAA;YAAA8F,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChClG,OAAA;YAAA8F,QAAA,EAAG;UAAqF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzF,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtClG,OAAA;YAAA8F,QAAA,EAAI;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjClG,OAAA;YAAA8F,QAAA,EAAG;UAAsE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtClG,OAAA;YAAA8F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BlG,OAAA;YAAA8F,QAAA,EAAG;UAAwE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9F,OAAA;YAAK6F,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtClG,OAAA;YAAA8F,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBlG,OAAA;YAAA8F,QAAA,EAAG;UAA4D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlG,OAAA;QAAK6F,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7B9F,OAAA;UAAA8F,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjClG,OAAA;UAAK6F,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC9F,OAAA;YAAK6F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9F,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5ClG,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAErE,eAAe,CAACC;YAAE;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC,eACNlG,OAAA;YAAK6F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9F,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpClG,OAAA;cAAM6F,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAErE,eAAe,CAACE;YAAI;cAAAoE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNlG,OAAA;YAAK6F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9F,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxClG,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAErE,eAAe,CAACI;YAAQ;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC,eACNlG,OAAA;YAAK6F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9F,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3ClG,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAErE,eAAe,CAACQ;YAAW;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNlG,OAAA;YAAK6F,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9F,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxClG,OAAA;cAAM6F,SAAS,EAAC,OAAO;cAAAC,QAAA,EAAErE,eAAe,CAACW;YAAQ;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlG,OAAA;QAAQ6F,SAAS,EAAC,cAAc;QAACM,OAAO,EAAE3D,iBAAkB;QAAAsD,QAAA,EAAC;MAE7D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,gBAENlG,OAAA;MAAK6F,SAAS,EAAC,mBAAmB;MAAAC,QAAA,eAChC9F,OAAA;QAAK6F,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClC9F,OAAA;UACEoG,GAAG,EAAEpF,QAAS;UACdqF,QAAQ;UACRC,WAAW;UACXC,KAAK;UACLV,SAAS,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eACFlG,OAAA;UACEoG,GAAG,EAAEnF,SAAU;UACf8B,KAAK,EAAC,MAAM;UACZE,MAAM,EAAC,KAAK;UACZ4C,SAAS,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAGFlG,OAAA;UAAK6F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAE7B9F,OAAA;YAAK6F,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5B9F,OAAA;cAAK6F,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9F,OAAA;gBAAM6F,SAAS,EAAC;cAA2B;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDlG,OAAA;gBAAA8F,QAAA,GAAM,uBAAqB,EAAChF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEY,EAAE;cAAA;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACNlG,OAAA;cAAK6F,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAE3B,UAAU,CAAChE,WAAW;YAAC;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,EAGLxF,SAAS,iBACRV,OAAA;YAAK6F,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACjC9F,OAAA;cAAK6F,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9B9F,OAAA;gBAAK6F,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC1ClG,OAAA;gBAAK6F,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,EAAEpF,SAAS,CAAC4C;cAAQ;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,eACNlG,OAAA;cAAK6F,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9F,OAAA;gBAAK6F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB9F,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAS;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC7ClG,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpF,SAAS,CAAC2C;gBAAa;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNlG,OAAA;gBAAK6F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB9F,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxClG,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpF,SAAS,CAACyB;gBAAa;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNlG,OAAA;gBAAK6F,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnB9F,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1ClG,OAAA;kBAAM6F,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAAEpF,SAAS,CAAC6C;gBAAY;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDlG,OAAA;YAAK6F,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9F,OAAA;cACE6F,SAAS,EAAE,kBAAkB3E,UAAU,CAACE,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;cACvE+E,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,cAAc,CAAE;cAAA8B,QAAA,EAChD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cACE6F,SAAS,EAAE,kBAAkB3E,UAAU,CAACG,cAAc,GAAG,QAAQ,GAAG,EAAE,EAAG;cACzE8E,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,gBAAgB,CAAE;cAAA8B,QAAA,EAClD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cACE6F,SAAS,EAAE,kBAAkB3E,UAAU,CAACI,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC1E6E,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,iBAAiB,CAAE;cAAA8B,QAAA,EACnD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cACE6F,SAAS,EAAE,kBAAkB3E,UAAU,CAACM,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;cAC1E2E,OAAO,EAAEA,CAAA,KAAMnC,eAAe,CAAC,iBAAiB,CAAE;cAAA8B,QAAA,EACnD;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA;cAAQ6F,SAAS,EAAC,yBAAyB;cAACM,OAAO,EAAExC,gBAAiB;cAAAmC,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDlG,OAAA;MAAK6F,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5B9F,OAAA;QAAA8F,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpClG,OAAA;QAAK6F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9F,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAA8F,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxBlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChClG,OAAA;cAAA8F,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnClG,OAAA;cAAA8F,QAAA,EAAI;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxClG,OAAA;cAAA8F,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAA8F,QAAA,EAAI;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrBlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChClG,OAAA;cAAA8F,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnClG,OAAA;cAAA8F,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnClG,OAAA;cAAA8F,QAAA,EAAI;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAA8F,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpClG,OAAA;cAAA8F,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClClG,OAAA;cAAA8F,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnClG,OAAA;cAAA8F,QAAA,EAAI;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENlG,OAAA;UAAK6F,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9F,OAAA;YAAA8F,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BlG,OAAA;YAAA8F,QAAA,gBACE9F,OAAA;cAAA8F,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClClG,OAAA;cAAA8F,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BlG,OAAA;cAAA8F,QAAA,EAAI;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/BlG,OAAA;cAAA8F,QAAA,EAAI;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChG,EAAA,CA/XID,YAAY;AAAAuG,EAAA,GAAZvG,YAAY;AAiYlB,eAAeA,YAAY;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}