/* Patient Transport Optimizer Styles */
.transport-optimizer {
  min-height: calc(100vh - 120px);
  background: var(--bg-secondary);
  padding: 24px;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.optimizer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  box-shadow: var(--shadow-xl);
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-info h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-info p {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 500;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.optimizer-content {
  display: grid;
  grid-template-columns: 350px 1fr 300px;
  gap: 24px;
  height: calc(100vh - 240px);
}

/* Panel Styles */
.panel {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--accent-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.panel:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.panel:hover::before {
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.panel-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: color 0.3s ease;
}

/* Left Panel */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.transport-requests-panel {
  flex: 1;
}

.new-request-btn {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-request-btn:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.requests-list {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.requests-list::-webkit-scrollbar {
  width: 6px;
}

.requests-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.requests-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.request-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-left: 4px solid;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.request-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.request-card.priority-critical {
  border-left-color: var(--error);
  background: var(--glass-bg);
}

.request-card.priority-high {
  border-left-color: #ea580c;
  background: var(--glass-bg);
}

.request-card.priority-medium {
  border-left-color: var(--warning);
  background: var(--glass-bg);
}

.request-card.priority-low {
  border-left-color: var(--success);
  background: var(--glass-bg);
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.request-id {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 14px;
  font-family: 'Inter', monospace;
  transition: color 0.3s ease;
}

.priority-badge {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.priority-badge.critical {
  background: var(--error);
  color: white;
}

.priority-badge.high {
  background: #ea580c;
  color: white;
}

.priority-badge.medium {
  background: var(--warning);
  color: white;
}

.priority-badge.low {
  background: var(--success);
  color: white;
}

.request-details {
  font-size: 13px;
  line-height: 1.5;
}

.patient-info {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.condition {
  color: #dc2626;
  font-weight: 500;
  margin-bottom: 8px;
}

.locations {
  margin-bottom: 8px;
}

.locations div {
  color: #64748b;
  margin-bottom: 2px;
}

.request-meta {
  margin-bottom: 8px;
  font-size: 12px;
  color: #64748b;
}

.request-meta div {
  margin-bottom: 2px;
}

.request-status {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 8px;
}

.request-status.status-optimizing {
  background: #dbeafe;
  color: #1d4ed8;
}

.request-status.status-dispatched {
  background: #fef3c7;
  color: #d97706;
}

.request-status.status-en_route {
  background: #fecaca;
  color: #dc2626;
}

.request-status.status-completed {
  background: #dcfce7;
  color: #166534;
}

.optimized-route {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  padding: 8px;
  font-size: 12px;
  color: #0c4a6e;
}

.optimized-route div {
  margin-bottom: 2px;
}

/* Metrics Panel */
.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.metric-card {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.metric-card .metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.metric-card .metric-label {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.metric-trend {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.metric-trend.positive {
  background: #dcfce7;
  color: #166534;
}

.metric-trend.neutral {
  background: #f1f5f9;
  color: #475569;
}

.metric-trend.negative {
  background: #fef2f2;
  color: #dc2626;
}

/* Center Panel - Map */
.center-panel {
  display: flex;
  flex-direction: column;
}

.map-panel {
  flex: 1;
}

.map-controls {
  display: flex;
  gap: 8px;
}

.map-btn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.map-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.map-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.transport-map {
  width: 100%;
  height: calc(100% - 120px);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  background: #f8fafc;
  min-height: 400px;
}

.map-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  padding: 12px 0 0 0;
  border-top: 1px solid #e2e8f0;
  margin-top: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #64748b;
}

.legend-icon {
  width: 16px;
  height: 16px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  font-weight: bold;
}

.legend-icon.pickup {
  background: #3b82f6;
}

.legend-icon.hospital {
  background: #dc2626;
}

.legend-icon.ambulance-available {
  background: #10b981;
}

.legend-icon.ambulance-busy {
  background: #f59e0b;
}

.legend-icon.route {
  background: #8b5cf6;
  color: white;
}

/* Right Panel */
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.hospital-capacity-panel,
.traffic-analytics-panel,
.ambulances-panel {
  flex: 1;
}

/* Hospital Capacity */
.hospital-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
}

.hospital-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px;
  transition: all 0.2s ease;
}

.hospital-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.hospital-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.hospital-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 13px;
}

.capacity-indicator {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.capacity-indicator.high {
  background: #fef2f2;
  color: #dc2626;
}

.capacity-indicator.medium {
  background: #fffbeb;
  color: #d97706;
}

.capacity-indicator.low {
  background: #f0fdf4;
  color: #059669;
}

.hospital-details {
  font-size: 12px;
}

.hospital-stat {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  color: #64748b;
}

.hospital-stat span:last-child {
  font-weight: 600;
  color: #1e293b;
}

.hospital-specialties {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.specialty-tag {
  padding: 2px 6px;
  background: #e0f2fe;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  font-size: 10px;
  color: #0c4a6e;
}

/* Traffic Analytics */
.traffic-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.traffic-stat {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.traffic-stat .stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.traffic-stat .stat-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.traffic-stat .stat-trend {
  font-size: 9px;
  font-weight: 600;
  padding: 2px 4px;
  border-radius: 3px;
}

.traffic-stat .stat-trend.high {
  background: #fef2f2;
  color: #dc2626;
}

.traffic-stat .stat-trend.normal {
  background: #f0fdf4;
  color: #059669;
}

.traffic-stat .stat-trend.low {
  background: #fffbeb;
  color: #d97706;
}

.traffic-stat .stat-trend.positive {
  background: #dbeafe;
  color: #1d4ed8;
}

/* Ambulances */
.ambulances-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 250px;
  overflow-y: auto;
}

.ambulance-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  transition: all 0.2s ease;
}

.ambulance-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.ambulance-card.status-available {
  border-left: 4px solid #10b981;
}

.ambulance-card.status-dispatched {
  border-left: 4px solid #f59e0b;
}

.ambulance-card.status-en_route {
  border-left: 4px solid #ef4444;
}

.ambulance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ambulance-id {
  font-weight: 600;
  color: #1e293b;
  font-size: 12px;
  font-family: 'Inter', monospace;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 9px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-badge.available {
  background: #dcfce7;
  color: #166534;
}

.status-badge.dispatched {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.en_route {
  background: #fecaca;
  color: #991b1b;
}

.ambulance-details {
  font-size: 11px;
}

.fuel-level {
  margin-bottom: 8px;
  color: #64748b;
}

.fuel-bar {
  width: 100%;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.fuel-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.equipment-list {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.equipment-tag {
  padding: 2px 4px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 3px;
  font-size: 9px;
  color: #475569;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .optimizer-content {
    grid-template-columns: 300px 1fr 250px;
  }
}

@media (max-width: 1024px) {
  .optimizer-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    flex-direction: row;
    gap: 16px;
  }

  .transport-requests-panel,
  .hospital-capacity-panel,
  .traffic-analytics-panel,
  .ambulances-panel {
    flex: 1;
    min-height: 300px;
  }

  .header-stats {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .transport-optimizer {
    padding: 16px;
  }

  .optimizer-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .optimizer-content {
    gap: 12px;
  }

  .left-panel,
  .right-panel {
    flex-direction: column;
  }

  .metrics-grid,
  .traffic-stats {
    grid-template-columns: 1fr;
  }
}
