import pygame
import sys
import requests
import threading
import time

# ESP8266 IP
ESP_IP = "http://***********"

# Initialize pygame
pygame.init()
WIDTH, HEIGHT = 800, 600
screen = pygame.display.set_mode((WIDTH, HEIGHT))
pygame.display.set_caption("Vehicle Path & Mode Control")
font = pygame.font.SysFont(None, 36)

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
BLUE = (0, 0, 255)
GRAY = (200, 200, 200)

# UI Buttons
autonomous_btn = pygame.Rect(50, 520, 200, 50)
path_follow_btn = pygame.Rect(300, 520, 200, 50)

# Path logic
path = []
drawing = False
follower_index = 0

# Mode tracking
current_mode = "autonomous"
relay_status = False

# Fetch ESP8266 status periodically
def fetch_status():
    global current_mode, relay_status
    while True:
        try:
            res = requests.get(f"{ESP_IP}/status", timeout=1)
            data = res.json()
            current_mode = data.get("mode", "unknown")
            relay_status = data.get("relay_on", False)
        except:
            current_mode = "offline"
        time.sleep(2)

# Start background thread for status fetching
threading.Thread(target=fetch_status, daemon=True).start()

# Main loop
clock = pygame.time.Clock()
running = True

while running:
    screen.fill(WHITE)
    mx, my = pygame.mouse.get_pos()

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

        elif event.type == pygame.MOUSEBUTTONDOWN:
            if autonomous_btn.collidepoint(event.pos):
                requests.get(f"{ESP_IP}/mode/autonomous")
            elif path_follow_btn.collidepoint(event.pos):
                requests.get(f"{ESP_IP}/mode/path_follow")
            else:
                drawing = True
                path = []

        elif event.type == pygame.MOUSEBUTTONUP:
            drawing = False
            follower_index = 0

        elif event.type == pygame.MOUSEMOTION and drawing:
            path.append(event.pos)

    # Draw path
    if len(path) > 1:
        pygame.draw.lines(screen, BLACK, False, path, 3)

    # Move follower in path follow mode
    if current_mode == "path_follow" and path and follower_index < len(path):
        pygame.draw.circle(screen, RED, path[follower_index], 8)
        follower_index += 1

    # Draw UI Buttons
    pygame.draw.rect(screen, GRAY, autonomous_btn)
    pygame.draw.rect(screen, GRAY, path_follow_btn)
    screen.blit(font.render("Autonomous Mode", True, BLACK), (autonomous_btn.x + 20, autonomous_btn.y + 10))
    screen.blit(font.render("Path Follow Mode", True, BLACK), (path_follow_btn.x + 20, path_follow_btn.y + 10))

    # Status display
    screen.blit(font.render(f"Mode: {current_mode}", True, BLUE), (550, 20))
    relay_text = "ON" if relay_status else "OFF"
    screen.blit(font.render(f"Relay: {relay_text}", True, BLUE), (550, 60))

    pygame.display.flip()
    clock.tick(60)

pygame.quit()
sys.exit()
