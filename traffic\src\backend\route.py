import pygame
import numpy as np
from heapq import heappush, heappop
import math

class Button:
    def __init__(self, x, y, width, height, text, color, hover_color):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.color = color
        self.hover_color = hover_color
        self.current_color = color
        self.is_hovered = False
        
    def draw(self, screen):
        pygame.draw.rect(screen, self.current_color, self.rect, border_radius=5)
        font = pygame.font.Font(None, 32)
        text_surface = font.render(self.text, True, (255, 255, 255))
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)
        
    def handle_event(self, event):
        if event.type == pygame.MOUSEMOTION:
            self.is_hovered = self.rect.collidepoint(event.pos)
            self.current_color = self.hover_color if self.is_hovered else self.color
            
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1 and self.is_hovered:
                return True
        return False

class Node:
    def __init__(self, position, g_cost=float('inf'), h_cost=0):
        self.position = position
        self.g_cost = g_cost  # Cost from start to this node
        self.h_cost = h_cost  # Estimated cost from this node to end
        self.f_cost = g_cost + h_cost  # Total cost
        self.parent = None

    def __lt__(self, other):
        return self.f_cost < other.f_cost

class ObstacleAvoidanceSimulation:
    def __init__(self):
        pygame.init()
        self.window_size = (800, 600)
        self.screen = pygame.display.set_mode(self.window_size)
        pygame.display.set_caption("Route Simulation with Obstacle Avoidance")

        # Colors
        self.WHITE = (255, 255, 255)
        self.BLACK = (0, 0, 0)
        self.RED = (255, 0, 0)
        self.BLUE = (0, 0, 255)
        self.GREEN = (0, 255, 0)
        self.GRAY = (128, 128, 128)
        self.DARK_BLUE = (0, 0, 139)
        self.DARK_RED = (139, 0, 0)
        
        # Mode switch button
        self.mode_button = Button(10, 10, 150, 30, "Place Obstacles", self.DARK_RED, self.RED)
        self.is_obstacle_mode = True
        
        # Clear button
        self.clear_button = Button(170, 10, 80, 30, "Clear", self.DARK_BLUE, self.BLUE)
        
        # Simulation parameters
        self.vehicle_pos = None
        self.vehicle_radius = 5
        self.speed = 1
        self.obstacles = []
        self.drawing = False
        self.path = []
        self.original_path = []
        self.trail = []
        self.trail_length = 20
        self.grid_size = 8
        self.safety_distance = 30  # Minimum distance to obstacles before finding alternative path
        self.look_ahead_distance = 50  # Distance to look ahead for obstacles
        self.path_memory = []  # Remember previously taken paths
        
        # Status text
        self.font = pygame.font.Font(None, 20)

    def draw_status(self):
        if self.is_obstacle_mode:
            status = "Left click: Place obstacles | Right click: Remove obstacles"
        else:
            status = "Left click: Set start point | Left click again: Set end point"
        
        text_surface = self.font.render(status, True, self.BLACK)
        text_rect = text_surface.get_rect(center=(self.window_size[0] // 2, 25))
        self.screen.blit(text_surface, text_rect)

    def distance(self, p1, p2):
        return math.sqrt((p2[0] - p1[0])**2 + (p2[1] - p1[1])**2)
    
    def get_neighbors(self, node, obstacles):
        neighbors = []
        x, y = node.position
        
        # Define possible movements (8 directions)
        directions = [(0, 1), (1, 0), (0, -1), (-1, 0),
                     (1, 1), (-1, 1), (1, -1), (-1, -1)]
        
        for dx, dy in directions:
            new_x = x + dx * self.grid_size
            new_y = y + dy * self.grid_size
            
            # Check bounds
            if 0 <= new_x < self.window_size[0] and 0 <= new_y < self.window_size[1]:
                # Check collision with obstacles
                valid = True
                for obs in obstacles:
                    if self.distance((new_x, new_y), obs[0]) < obs[1] + self.vehicle_radius:
                        valid = False
                        break
                
                if valid:
                    neighbors.append((new_x, new_y))
        
        return neighbors
    
    def line_intersects_obstacle(self, start, end, obstacle):
        # Check if a line segment intersects with a circular obstacle
        center, radius = obstacle
        
        # Vector from start to end
        d = (end[0] - start[0], end[1] - start[1])
        # Vector from start to circle center
        f = (start[0] - center[0], start[1] - center[1])
        
        # Calculate quadratic equation coefficients
        a = d[0] * d[0] + d[1] * d[1]
        b = 2 * (f[0] * d[0] + f[1] * d[1])
        c = f[0] * f[0] + f[1] * f[1] - radius * radius
        
        discriminant = b * b - 4 * a * c
        if discriminant < 0:
            return False
        
        t1 = (-b + math.sqrt(discriminant)) / (2 * a)
        t2 = (-b - math.sqrt(discriminant)) / (2 * a)
        
        return (0 <= t1 <= 1) or (0 <= t2 <= 1)

    def find_path(self, start, end, obstacles):
        # First check if direct path is possible
        direct_path_possible = True
        for obstacle in obstacles:
            if self.line_intersects_obstacle(start, end, (obstacle[0], obstacle[1] + self.safety_distance)):
                direct_path_possible = False
                break
        
        if direct_path_possible:
            return [start, end]
        
        # If direct path is not possible, use A* pathfinding
        start_node = Node(start, 0, self.distance(start, end))
        end_node = Node(end)
        
        open_set = []
        closed_set = set()
        node_dict = {}  # Dictionary to store nodes by position
        
        heappush(open_set, start_node)
        node_dict[start] = start_node
        
        while open_set:
            current = heappop(open_set)
            
            if self.distance(current.position, end) < self.grid_size:
                # Reconstruct path
                path = []
                while current:
                    path.append(current.position)
                    current = current.parent
                return self.smooth_path(path[::-1], obstacles)
            
            closed_set.add(current.position)
            
            for neighbor_pos in self.get_neighbors(current, obstacles):
                if neighbor_pos in closed_set:
                    continue
                
                g_cost = current.g_cost + self.distance(current.position, neighbor_pos)
                
                if neighbor_pos not in node_dict:
                    h_cost = self.distance(neighbor_pos, end)
                    neighbor = Node(neighbor_pos, g_cost, h_cost)
                    node_dict[neighbor_pos] = neighbor
                else:
                    neighbor = node_dict[neighbor_pos]
                    if g_cost >= neighbor.g_cost:
                        continue
                    
                neighbor.g_cost = g_cost
                neighbor.f_cost = g_cost + neighbor.h_cost
                neighbor.parent = current
                
                if neighbor_pos not in [n.position for n in open_set]:
                    heappush(open_set, neighbor)
        
        return None

    def smooth_path(self, path, obstacles):
        if len(path) < 3:
            return path
            
        smoothed = [path[0]]
        current_index = 0
        
        while current_index < len(path) - 1:
            # Try to connect current point with furthest possible point
            for i in range(len(path) - 1, current_index, -1):
                can_connect = True
                for obstacle in obstacles:
                    if self.line_intersects_obstacle(path[current_index], path[i], obstacle):
                        can_connect = False
                        break
                
                if can_connect:
                    smoothed.append(path[i])
                    current_index = i
                    break
            else:
                current_index += 1
                smoothed.append(path[current_index])
        
        return smoothed

    def find_alternative_paths(self, current_pos, current_direction, final_destination):
        # Generate potential alternative paths when obstacle is detected
        alternatives = []
        # Increase the range of angles to try for better obstacle avoidance
        angles = [-60, -45, -30, -15, 0, 15, 30, 45, 60]  # Added wider angles
        
        for angle in angles:
            # Calculate new direction vector
            rad = math.radians(angle)
            dx = current_direction[0] * math.cos(rad) - current_direction[1] * math.sin(rad)
            dy = current_direction[0] * math.sin(rad) + current_direction[1] * math.cos(rad)
            
            # Normalize direction vector
            length = math.sqrt(dx*dx + dy*dy)
            if length > 0:
                dx /= length
                dy /= length
            
            # Calculate end point of potential path
            # Use longer look_ahead_distance for wider scanning
            scan_distance = self.look_ahead_distance * 1.5
            end_point = (
                int(current_pos[0] + dx * scan_distance),
                int(current_pos[1] + dy * scan_distance)
            )
            
            # Check if end point is within bounds
            if (0 <= end_point[0] < self.window_size[0] and 
                0 <= end_point[1] < self.window_size[1]):
                
                # Check if path is safe
                if self.is_path_safe(current_pos, end_point):
                    # Calculate path score based on:
                    # 1. Angle deviation
                    # 2. Distance to final destination
                    # 3. Path safety
                    angle_cost = abs(angle) * 0.5
                    dist_to_final = self.distance(end_point, final_destination)
                    dist_cost = dist_to_final * 0.3
                    safety_cost = self.calculate_path_cost(current_pos, end_point)
                    
                    total_score = angle_cost + dist_cost + safety_cost
                    alternatives.append((end_point, total_score))
        
        # Sort alternatives by score
        alternatives.sort(key=lambda x: x[1])
        return [path[0] for path in alternatives]

    def calculate_path_cost(self, start, end):
        # Calculate cost of path based on:
        # 1. Distance from original path
        # 2. Proximity to obstacles
        # 3. Previous successful paths
        cost = 0
        
        # Cost based on obstacle proximity
        for obstacle in self.obstacles:
            dist = self.distance(obstacle[0], ((start[0] + end[0])/2, (start[1] + end[1])/2))
            if dist < self.safety_distance * 2:
                cost += (self.safety_distance * 2 - dist) * 2
        
        # Cost based on previous successful paths
        min_dist_to_memory = float('inf')
        for path in self.path_memory:
            if len(path) >= 2:
                for i in range(len(path) - 1):
                    dist = self.point_to_line_distance(end, path[i], path[i+1])
                    min_dist_to_memory = min(min_dist_to_memory, dist)
        
        # Prefer paths closer to previously successful paths
        cost += min_dist_to_memory * 0.5
        
        return cost

    def point_to_line_distance(self, point, line_start, line_end):
        # Calculate distance from point to line segment
        x, y = point
        x1, y1 = line_start
        x2, y2 = line_end
        
        A = x - x1
        B = y - y1
        C = x2 - x1
        D = y2 - y1
        
        dot = A * C + B * D
        len_sq = C * C + D * D
        
        if len_sq == 0:
            return self.distance(point, line_start)
            
        param = dot / len_sq
        
        if param < 0:
            return self.distance(point, line_start)
        elif param > 1:
            return self.distance(point, line_end)
        else:
            return self.distance(point, (x1 + param * C, y1 + param * D))

    def update_vehicle_position(self, current_path_index):
        if not self.path or not self.vehicle_pos or current_path_index >= len(self.path):
            return current_path_index

        # Get final destination (end of the original line)
        final_destination = self.original_path[-1]
        target = self.path[current_path_index]
        dx = target[0] - self.vehicle_pos[0]
        dy = target[1] - self.vehicle_pos[1]
        distance = math.sqrt(dx*dx + dy*dy)

        # Check if we've reached the final destination
        dist_to_final = self.distance(self.vehicle_pos, final_destination)
        if dist_to_final < self.grid_size:
            # Stop at final destination
            self.vehicle_pos = final_destination
            return current_path_index

        # If we're at current target, get new path to final destination
        if distance < 0.1:  # Small threshold to avoid division by zero
            if current_path_index == len(self.path) - 1:
                # We've reached the end of current path, find new path to final destination
                new_path = self.find_path(self.vehicle_pos, final_destination, self.obstacles)
                if new_path:
                    self.path = new_path
                    return 0  # Reset path index
            else:
                # Move to next point in current path
                return current_path_index + 1

        current_direction = (dx/distance, dy/distance)  # Normalize direction

        # Look ahead for obstacles
        look_ahead_points = []
        for dist in [self.look_ahead_distance * 0.5, self.look_ahead_distance]:  # Check at two distances
            next_pos = (
                self.vehicle_pos[0] + current_direction[0] * dist,
                self.vehicle_pos[1] + current_direction[1] * dist
            )
            look_ahead_points.append(next_pos)

        # Check if any look-ahead points are unsafe
        path_unsafe = False
        for next_pos in look_ahead_points:
            if not self.is_path_safe(self.vehicle_pos, next_pos):
                path_unsafe = True
                break

        if path_unsafe:
            # Find alternative paths
            alternatives = self.find_alternative_paths(self.vehicle_pos, current_direction, final_destination)
            
            if alternatives:
                # Try to find a path to the final destination from the best alternative
                new_path = None
                for alt in alternatives:
                    potential_path = self.find_path(self.vehicle_pos, alt, self.obstacles)
                    if potential_path:
                        # Try to connect this path to the final destination
                        complete_path = self.find_path(alt, final_destination, self.obstacles)
                        if complete_path:
                            # Combine the paths
                            new_path = potential_path[:-1] + complete_path
                            break
                
                if new_path:
                    self.path = new_path
                    if len(self.path_memory) > 5:
                        self.path_memory.pop(0)
                    self.path_memory.append(new_path)
                    return 0  # Reset path index
        
        # Move vehicle
        if distance < self.speed:
            self.vehicle_pos = target
            return current_path_index + 1
        else:
            self.vehicle_pos = (
                self.vehicle_pos[0] + (dx/distance * self.speed),
                self.vehicle_pos[1] + (dy/distance * self.speed)
            )
            return current_path_index

    def is_path_safe(self, current_pos, next_pos):
        # Check if the path between current_pos and next_pos is safe
        for obstacle in self.obstacles:
            # Check if path comes too close to obstacle
            if self.line_intersects_obstacle(current_pos, next_pos, (obstacle[0], obstacle[1] + self.safety_distance)):
                return False
            
            # Also check the midpoint
            mid_point = ((current_pos[0] + next_pos[0])/2, (current_pos[1] + next_pos[1])/2)
            if self.distance(mid_point, obstacle[0]) < obstacle[1] + self.safety_distance:
                return False
        return True

    def run(self):
        clock = pygame.time.Clock()
        drawing_line = False
        line_start = None
        current_path_index = 0
        simulation_complete = False

        running = True
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif self.mode_button.handle_event(event):
                    self.is_obstacle_mode = not self.is_obstacle_mode
                    self.mode_button.text = "Place Obstacles" if self.is_obstacle_mode else "Draw Path"
                    drawing_line = False
                    line_start = None
                    simulation_complete = False
                elif self.clear_button.handle_event(event):
                    self.obstacles = []
                    self.path = []
                    self.original_path = []
                    self.vehicle_pos = None
                    self.trail = []
                    self.path_memory = []
                    drawing_line = False
                    line_start = None
                    current_path_index = 0
                    simulation_complete = False
                elif event.type == pygame.MOUSEBUTTONDOWN:
                    mouse_pos = pygame.mouse.get_pos()
                    if mouse_pos[1] < 50:  # Reduced height of button area
                        continue
                    
                    if self.is_obstacle_mode:
                        if event.button == 1:  # Left click
                            self.obstacles.append((mouse_pos, 20))
                        elif event.button == 3:  # Right click
                            for i, (center, radius) in enumerate(self.obstacles):
                                if self.distance(center, mouse_pos) < radius:
                                    self.obstacles.pop(i)
                                    break
                    else:
                        if event.button == 1:  # Left click
                            if not drawing_line:
                                line_start = mouse_pos
                                drawing_line = True
                                self.path = []
                                self.original_path = []
                                self.vehicle_pos = None
                                self.trail = []
                                current_path_index = 0
                                simulation_complete = False
                            else:
                                line_end = mouse_pos
                                drawing_line = False
                                self.original_path = [line_start, line_end]
                                self.path = self.find_path(line_start, line_end, self.obstacles)
                                if self.path:
                                    self.vehicle_pos = self.path[0]
                                    current_path_index = 0
                                    self.trail = []

            # Clear screen
            self.screen.fill(self.WHITE)
            
            # Draw buttons and status
            self.mode_button.draw(self.screen)
            self.clear_button.draw(self.screen)
            self.draw_status()
            
            # Draw obstacles with safety radius
            for center, radius in self.obstacles:
                # Draw actual obstacle
                pygame.draw.circle(self.screen, self.RED, center, radius)
                # Draw safety radius
                pygame.draw.circle(self.screen, (*self.RED, 50), center, 
                                radius + self.safety_distance, 1)
            
            # Draw temporary line while drawing
            if drawing_line and line_start:
                pygame.draw.line(self.screen, self.GRAY, line_start, pygame.mouse.get_pos(), 2)
            
            # Draw original path
            if self.original_path:
                pygame.draw.lines(self.screen, self.GRAY, False, self.original_path, 2)
            
            # Draw optimized path
            if self.path:
                pygame.draw.lines(self.screen, self.BLUE, False, self.path, 2)
            
            # Draw previous paths (memory)
            for past_path in self.path_memory[:-1]:  # Don't draw current path
                if past_path:
                    pygame.draw.lines(self.screen, (*self.BLACK, 50), False, past_path, 1)
            
            # Update and draw vehicle if simulation is not complete
            if self.path and self.vehicle_pos and not simulation_complete:
                old_index = current_path_index
                current_path_index = self.update_vehicle_position(current_path_index)
                
                # Check if we've reached the final destination
                if self.original_path and self.distance(self.vehicle_pos, self.original_path[-1]) < self.grid_size:
                    simulation_complete = True
                
                # Update and draw trail
                self.trail.append(self.vehicle_pos)
                if len(self.trail) > self.trail_length:
                    self.trail.pop(0)
                
                for i, pos in enumerate(self.trail):
                    alpha = int(180 * (i / self.trail_length))
                    radius = int(3 * (i / self.trail_length)) + 2
                    trail_surface = pygame.Surface(self.window_size, pygame.SRCALPHA)
                    pygame.draw.circle(trail_surface, (*self.GREEN, alpha), 
                                    (int(pos[0]), int(pos[1])), radius)
                    self.screen.blit(trail_surface, (0, 0))
                
                # Draw vehicle
                pygame.draw.circle(self.screen, self.GREEN, 
                                 (int(self.vehicle_pos[0]), int(self.vehicle_pos[1])), 
                                 self.vehicle_radius)
                
                # Draw look-ahead line
                if len(self.path) > current_path_index:
                    target = self.path[current_path_index]
                    pygame.draw.line(self.screen, (*self.BLUE, 128), 
                                   self.vehicle_pos, target, 1)

            pygame.display.flip()
            clock.tick(60)

        pygame.quit()

if __name__ == "__main__":
    simulation = ObstacleAvoidanceSimulation()
    simulation.run()
