import React, { useState, useEffect } from 'react';
import './MobileApp.css';

const MobileApp = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [emergencyForm, setEmergencyForm] = useState({
    emergencyType: '',
    description: '',
    location: '',
    patientAge: '',
    patientGender: '',
    symptoms: [],
    urgency: 'medium',
    contactNumber: '',
    additionalInfo: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [isVoiceRecording, setIsVoiceRecording] = useState(false);
  const [voiceTranscript, setVoiceTranscript] = useState('');

  // Emergency types with icons
  const emergencyTypes = [
    { id: 'cardiac', name: 'Heart Attack / Cardiac Emergency', icon: '💓', priority: 'critical' },
    { id: 'accident', name: 'Road Accident / Trauma', icon: '🚗', priority: 'high' },
    { id: 'breathing', name: 'Breathing Difficulty', icon: '🫁', priority: 'high' },
    { id: 'stroke', name: 'Stroke / Neurological', icon: '🧠', priority: 'critical' },
    { id: 'overdose', name: 'Poisoning / Overdose', icon: '💊', priority: 'high' },
    { id: 'injury', name: 'Severe Injury / Bleeding', icon: '🩸', priority: 'medium' },
    { id: 'pregnancy', name: 'Pregnancy Emergency', icon: '🤱', priority: 'high' },
    { id: 'other', name: 'Other Emergency', icon: '🚨', priority: 'medium' }
  ];

  // Common symptoms
  const commonSymptoms = [
    'Chest pain', 'Difficulty breathing', 'Unconscious', 'Severe bleeding',
    'Broken bones', 'Severe pain', 'Nausea/Vomiting', 'Dizziness',
    'High fever', 'Seizure', 'Confusion', 'Weakness'
  ];

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Get user location
  useEffect(() => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setUserLocation({
            lat: position.coords.latitude,
            lng: position.coords.longitude
          });
        },
        (error) => {
          console.log('Location access denied:', error);
        }
      );
    }
  }, []);

  const handleInputChange = (field, value) => {
    setEmergencyForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSymptomToggle = (symptom) => {
    setEmergencyForm(prev => ({
      ...prev,
      symptoms: prev.symptoms.includes(symptom)
        ? prev.symptoms.filter(s => s !== symptom)
        : [...prev.symptoms, symptom]
    }));
  };

  const handleEmergencyTypeSelect = (type) => {
    setEmergencyForm(prev => ({
      ...prev,
      emergencyType: type.id,
      urgency: type.priority
    }));
  };

  const startVoiceRecording = () => {
    setIsVoiceRecording(true);
    // Simulate voice recording
    setTimeout(() => {
      setVoiceTranscript("Help! I need an ambulance at Marina Beach. My friend is having chest pain and difficulty breathing.");
      setIsVoiceRecording(false);
      // Auto-fill form based on voice
      setEmergencyForm(prev => ({
        ...prev,
        emergencyType: 'cardiac',
        description: 'Chest pain and difficulty breathing',
        location: 'Marina Beach',
        symptoms: ['Chest pain', 'Difficulty breathing'],
        urgency: 'critical'
      }));
    }, 3000);
  };

  const submitEmergencyRequest = async () => {
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      const emergencyId = `EMG${Date.now().toString().slice(-3)}`;
      setSubmissionStatus({
        success: true,
        emergencyId,
        estimatedArrival: '8-12 minutes',
        assignedAmbulance: 'AMB001',
        message: 'Emergency request submitted successfully! Ambulance is being dispatched.'
      });
      setIsSubmitting(false);
    }, 2000);
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="mobile-app-container">
      <div className="mobile-app-header">
        <h1>📱 Emergency Mobile App Demo</h1>
        <div className="app-time">{formatTime(currentTime)}</div>
      </div>

      <div className="mobile-demo-section">
        <div className="demo-description">
          <h2>📱 Interactive Mobile App Demo</h2>
          <p>Experience our emergency ambulance request app in a realistic mobile interface. This demo shows how citizens can quickly request emergency medical assistance through our user-friendly mobile application.</p>
        </div>

        {/* Mobile Phone Frame */}
        <div className="mobile-phone-frame">
          <div className="phone-screen">
            <div className="phone-status-bar">
              <div className="status-left">
                <span className="signal-bars">📶</span>
                <span className="carrier">Emergency</span>
              </div>
              <div className="status-center">
                <span className="phone-time">{formatTime(currentTime)}</span>
              </div>
              <div className="status-right">
                <span className="battery">🔋 85%</span>
              </div>
            </div>

            <div className="phone-content">
              {!submissionStatus ? (
                <div className="mobile-app-interface">
                  <div className="app-header">
                    <div className="app-title">
                      <span className="app-icon">🚨</span>
                      <h3>Emergency SOS</h3>
                    </div>
                    <div className="location-indicator">
                      📍 Chennai, Tamil Nadu
                    </div>
                  </div>

                  <div className="quick-emergency-buttons">
                    <button
                      className="quick-btn critical"
                      onClick={() => {
                        setEmergencyForm(prev => ({
                          ...prev,
                          emergencyType: 'cardiac',
                          urgency: 'critical',
                          description: 'Heart attack emergency'
                        }));
                      }}
                    >
                      💓 Heart Attack
                    </button>
                    <button
                      className="quick-btn high"
                      onClick={() => {
                        setEmergencyForm(prev => ({
                          ...prev,
                          emergencyType: 'accident',
                          urgency: 'high',
                          description: 'Road accident with injuries'
                        }));
                      }}
                    >
                      🚗 Accident
                    </button>
                    <button
                      className="quick-btn high"
                      onClick={() => {
                        setEmergencyForm(prev => ({
                          ...prev,
                          emergencyType: 'breathing',
                          urgency: 'high',
                          description: 'Severe breathing difficulty'
                        }));
                      }}
                    >
                      🫁 Breathing
                    </button>
                  </div>

                  <div className="emergency-form-mobile">
                    <div className="form-group-mobile">
                      <label>Emergency Type</label>
                      <select
                        value={emergencyForm.emergencyType}
                        onChange={(e) => handleInputChange('emergencyType', e.target.value)}
                        className="mobile-select"
                      >
                        <option value="">Select Emergency Type</option>
                        {emergencyTypes.map(type => (
                          <option key={type.id} value={type.id}>
                            {type.icon} {type.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group-mobile">
                      <label>Your Location</label>
                      <input
                        type="text"
                        value={emergencyForm.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        placeholder="Current location..."
                        className="mobile-input"
                      />
                      <button className="location-btn">📍 Use GPS</button>
                    </div>

                    <div className="form-group-mobile">
                      <label>Describe Emergency</label>
                      <textarea
                        value={emergencyForm.description}
                        onChange={(e) => handleInputChange('description', e.target.value)}
                        placeholder="What happened? Current condition..."
                        className="mobile-textarea"
                        rows="3"
                      />
                    </div>

                    <div className="form-row-mobile">
                      <div className="form-group-mobile">
                        <label>Age</label>
                        <input
                          type="number"
                          value={emergencyForm.patientAge}
                          onChange={(e) => handleInputChange('patientAge', e.target.value)}
                          placeholder="Age"
                          className="mobile-input"
                        />
                      </div>
                      <div className="form-group-mobile">
                        <label>Contact</label>
                        <input
                          type="tel"
                          value={emergencyForm.contactNumber}
                          onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                          placeholder="+91 XXXXX"
                          className="mobile-input"
                        />
                      </div>
                    </div>

                    <div className="voice-section-mobile">
                      <button
                        className={`voice-btn-mobile ${isVoiceRecording ? 'recording' : ''}`}
                        onClick={startVoiceRecording}
                        disabled={isVoiceRecording}
                      >
                        {isVoiceRecording ? '🔴 Recording...' : '🎤 Voice Description'}
                      </button>
                      {voiceTranscript && (
                        <div className="voice-transcript-mobile">
                          "{voiceTranscript}"
                        </div>
                      )}
                    </div>

                    <button
                      className="emergency-call-btn"
                      onClick={submitEmergencyRequest}
                      disabled={isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location}
                    >
                      {isSubmitting ? '🚑 Calling Ambulance...' : '🚨 CALL AMBULANCE NOW'}
                    </button>
                  </div>
                </div>
              ) : (
                <div className="success-screen-mobile">
                  <div className="success-animation">
                    <div className="checkmark">✅</div>
                    <div className="pulse-ring"></div>
                  </div>
                  <h3>Ambulance Dispatched!</h3>
                  <div className="success-details-mobile">
                    <div className="detail-row">
                      <span>Emergency ID:</span>
                      <span>{submissionStatus.emergencyId}</span>
                    </div>
                    <div className="detail-row">
                      <span>Ambulance:</span>
                      <span>{submissionStatus.assignedAmbulance}</span>
                    </div>
                    <div className="detail-row">
                      <span>ETA:</span>
                      <span>{submissionStatus.estimatedArrival}</span>
                    </div>
                  </div>

                  <div className="tracking-section">
                    <div className="ambulance-icon">🚑</div>
                    <div className="tracking-text">Tracking ambulance in real-time...</div>
                    <div className="progress-bar">
                      <div className="progress-fill"></div>
                    </div>
                  </div>

                  <button
                    className="new-emergency-btn"
                    onClick={() => {
                      setSubmissionStatus(null);
                      setEmergencyForm({
                        emergencyType: '',
                        description: '',
                        location: '',
                        patientAge: '',
                        patientGender: '',
                        symptoms: [],
                        urgency: 'medium',
                        contactNumber: '',
                        additionalInfo: ''
                      });
                      setVoiceTranscript('');
                    }}
                  >
                    New Emergency
                  </button>
                </div>
              )}
            </div>

            <div className="phone-home-indicator"></div>
          </div>
        </div>
      </div>

      {/* Desktop Form (Original) */}
      <div className="desktop-form-section">
        {!submissionStatus ? (
          <div className="emergency-request-form">
            <div className="form-section">
              <h2>🚨 Request Emergency Ambulance (Desktop View)</h2>
              <p className="form-description">
                Fill out this form to request immediate medical assistance.
                Your location will be automatically detected if available.
              </p>

            {/* Emergency Type Selection */}
            <div className="form-group">
              <label>Emergency Type *</label>
              <div className="emergency-types-grid">
                {emergencyTypes.map(type => (
                  <button
                    key={type.id}
                    className={`emergency-type-btn ${emergencyForm.emergencyType === type.id ? 'selected' : ''}`}
                    onClick={() => handleEmergencyTypeSelect(type)}
                  >
                    <span className="emergency-icon">{type.icon}</span>
                    <span className="emergency-name">{type.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Voice Input */}
            <div className="form-group">
              <label>🎤 Voice Description (Optional)</label>
              <div className="voice-input-section">
                <button
                  className={`voice-btn ${isVoiceRecording ? 'recording' : ''}`}
                  onClick={startVoiceRecording}
                  disabled={isVoiceRecording}
                >
                  {isVoiceRecording ? '🔴 Recording...' : '🎤 Describe Emergency'}
                </button>
                {voiceTranscript && (
                  <div className="voice-transcript">
                    <strong>Transcript:</strong> {voiceTranscript}
                  </div>
                )}
              </div>
            </div>

            {/* Location */}
            <div className="form-group">
              <label>Location *</label>
              <input
                type="text"
                value={emergencyForm.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="Enter your current location or address"
                className="form-input"
              />
              {userLocation && (
                <div className="location-detected">
                  📍 Location detected: {userLocation.lat.toFixed(4)}, {userLocation.lng.toFixed(4)}
                </div>
              )}
            </div>

            {/* Description */}
            <div className="form-group">
              <label>Emergency Description *</label>
              <textarea
                value={emergencyForm.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Describe what happened and current condition"
                className="form-textarea"
                rows="3"
              />
            </div>

            {/* Patient Information */}
            <div className="form-row">
              <div className="form-group">
                <label>Patient Age</label>
                <input
                  type="number"
                  value={emergencyForm.patientAge}
                  onChange={(e) => handleInputChange('patientAge', e.target.value)}
                  placeholder="Age"
                  className="form-input"
                />
              </div>
              <div className="form-group">
                <label>Gender</label>
                <select
                  value={emergencyForm.patientGender}
                  onChange={(e) => handleInputChange('patientGender', e.target.value)}
                  className="form-select"
                >
                  <option value="">Select</option>
                  <option value="male">Male</option>
                  <option value="female">Female</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            {/* Symptoms */}
            <div className="form-group">
              <label>Symptoms (Select all that apply)</label>
              <div className="symptoms-grid">
                {commonSymptoms.map(symptom => (
                  <button
                    key={symptom}
                    className={`symptom-btn ${emergencyForm.symptoms.includes(symptom) ? 'selected' : ''}`}
                    onClick={() => handleSymptomToggle(symptom)}
                  >
                    {symptom}
                  </button>
                ))}
              </div>
            </div>

            {/* Contact Number */}
            <div className="form-group">
              <label>Contact Number *</label>
              <input
                type="tel"
                value={emergencyForm.contactNumber}
                onChange={(e) => handleInputChange('contactNumber', e.target.value)}
                placeholder="+91 XXXXX XXXXX"
                className="form-input"
              />
            </div>

            {/* Urgency Level */}
            <div className="form-group">
              <label>Urgency Level</label>
              <div className="urgency-buttons">
                {['low', 'medium', 'high', 'critical'].map(level => (
                  <button
                    key={level}
                    className={`urgency-btn ${emergencyForm.urgency === level ? 'selected' : ''} ${level}`}
                    onClick={() => handleInputChange('urgency', level)}
                  >
                    {level.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>

            {/* Submit Button */}
            <button
              className="submit-emergency-btn"
              onClick={submitEmergencyRequest}
              disabled={isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location || !emergencyForm.contactNumber}
            >
              {isSubmitting ? '🚑 Dispatching Ambulance...' : '🚨 REQUEST EMERGENCY AMBULANCE'}
            </button>
          </div>
        </div>
      ) : (
        <div className="submission-success">
          <div className="success-icon">✅</div>
          <h2>Emergency Request Submitted!</h2>
          <div className="success-details">
            <div className="detail-item">
              <strong>Emergency ID:</strong> {submissionStatus.emergencyId}
            </div>
            <div className="detail-item">
              <strong>Assigned Ambulance:</strong> {submissionStatus.assignedAmbulance}
            </div>
            <div className="detail-item">
              <strong>Estimated Arrival:</strong> {submissionStatus.estimatedArrival}
            </div>
          </div>
          <div className="success-message">
            {submissionStatus.message}
          </div>
          <button
            className="new-request-btn"
            onClick={() => {
              setSubmissionStatus(null);
              setEmergencyForm({
                emergencyType: '',
                description: '',
                location: '',
                patientAge: '',
                patientGender: '',
                symptoms: [],
                urgency: 'medium',
                contactNumber: '',
                additionalInfo: ''
              });
              setVoiceTranscript('');
            }}
          >
            Submit New Request
          </button>
        </div>
      )}
      </div>
    </div>
  );
};

export default MobileApp;
