{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\components\\\\Storyboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './Storyboard.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Storyboard = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeStep, setActiveStep] = useState(null);\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  const storyboardSteps = [{\n    id: 1,\n    title: \"Emergency Detection\",\n    icon: \"🚨\",\n    description: \"Multiple channels detect emergencies across the city\",\n    details: [\"Mobile App: Citizens report emergencies directly\", \"Voice Calls: Multilingual AI processes emergency calls\", \"IoT Sensors: Smart city sensors detect accidents\", \"Smart Watches: Health monitoring alerts\", \"Hospital Integration: Direct medical facility requests\"],\n    technologies: [\"Whisper ASR\", \"NLP Classification\", \"IoT Integration\", \"Mobile SDK\"],\n    timeframe: \"0-30 seconds\",\n    color: \"#dc2626\"\n  }, {\n    id: 2,\n    title: \"AI Classification\",\n    icon: \"🧠\",\n    description: \"Advanced AI analyzes and classifies emergency severity\",\n    details: [\"Symptom Analysis: AI processes reported symptoms\", \"Risk Assessment: Calculates emergency severity score\", \"Medical Classification: Categorizes emergency type\", \"Priority Assignment: Assigns critical/high/medium/low priority\", \"Hospital Recommendation: Suggests optimal medical facility\"],\n    technologies: [\"Machine Learning\", \"Medical AI\", \"Risk Scoring\", \"Decision Trees\"],\n    timeframe: \"5-15 seconds\",\n    color: \"#7c3aed\"\n  }, {\n    id: 3,\n    title: \"Smart Dispatch\",\n    icon: \"🎯\",\n    description: \"AI-powered ambulance selection and route optimization\",\n    details: [\"Ambulance Selection: AI chooses optimal ambulance based on multiple factors\", \"Distance Calculation: Real-time location-based routing\", \"Specialization Matching: Matches medical equipment to emergency type\", \"Traffic Analysis: Considers current traffic conditions\", \"Fuel & Maintenance: Factors in ambulance readiness status\"],\n    technologies: [\"Optimization Algorithms\", \"Real-time Analytics\", \"GPS Integration\", \"Fleet Management\"],\n    timeframe: \"10-20 seconds\",\n    color: \"#059669\"\n  }, {\n    id: 4,\n    title: \"Traffic Signal Control\",\n    icon: \"🚦\",\n    description: \"AI hijacks traffic signals to create green corridors\",\n    details: [\"Route Prediction: AI predicts ambulance path\", \"Signal Coordination: Synchronizes traffic lights along route\", \"Green Wave Creation: Creates continuous green light corridor\", \"Traffic Flow Management: Minimizes disruption to regular traffic\", \"Real-time Adjustment: Adapts to route changes dynamically\"],\n    technologies: [\"Traffic AI\", \"Signal Control Systems\", \"Route Prediction\", \"Smart City Integration\"],\n    timeframe: \"Continuous during transport\",\n    color: \"#f59e0b\"\n  }, {\n    id: 5,\n    title: \"Real-time Tracking\",\n    icon: \"📍\",\n    description: \"Live monitoring and coordination throughout the emergency\",\n    details: [\"GPS Tracking: Real-time ambulance location monitoring\", \"ETA Updates: Continuous arrival time calculations\", \"Communication Hub: Coordination between dispatch, ambulance, and hospital\", \"Status Updates: Live updates to all stakeholders\", \"Route Optimization: Dynamic route adjustments based on conditions\"],\n    technologies: [\"GPS Systems\", \"Real-time Communication\", \"WebSocket\", \"Mobile Notifications\"],\n    timeframe: \"Throughout journey\",\n    color: \"#3b82f6\"\n  }, {\n    id: 6,\n    title: \"Hospital Preparation\",\n    icon: \"🏥\",\n    description: \"Automated hospital notification and preparation\",\n    details: [\"Hospital Alert: Automatic notification to receiving hospital\", \"Medical Team Preparation: Staff alerted based on emergency type\", \"Resource Allocation: Equipment and room preparation\", \"Patient Information: Medical history and current status shared\", \"Arrival Coordination: Seamless handoff from ambulance to hospital\"],\n    technologies: [\"Hospital Integration APIs\", \"Medical Records\", \"Resource Management\", \"Communication Systems\"],\n    timeframe: \"5-10 minutes before arrival\",\n    color: \"#dc2626\"\n  }];\n  const systemFeatures = [{\n    category: \"AI & Machine Learning\",\n    features: [\"Emergency Classification with 94% accuracy\", \"Predictive heatmap analytics for resource deployment\", \"Smart ambulance selection algorithms\", \"Traffic pattern analysis and prediction\", \"Medical risk assessment and triage\"]\n  }, {\n    category: \"Multilingual Support\",\n    features: [\"Voice recognition in 6+ Indian languages\", \"OpenAI Whisper ASR integration\", \"Real-time language detection\", \"Cultural context understanding\", \"Accessibility for all literacy levels\"]\n  }, {\n    category: \"Smart City Integration\",\n    features: [\"Traffic signal control and coordination\", \"IoT sensor network integration\", \"Smart city platform compatibility\", \"Real-time data sharing with city systems\", \"Scalable microservices architecture\"]\n  }, {\n    category: \"Mobile & Web Platforms\",\n    features: [\"Citizen emergency reporting app\", \"Professional dispatch center interface\", \"Hospital integration dashboard\", \"Real-time tracking and notifications\", \"Cross-platform compatibility\"]\n  }];\n  const performanceMetrics = [{\n    metric: \"Average Response Time\",\n    value: \"2.8 minutes\",\n    improvement: \"45% faster\"\n  }, {\n    metric: \"AI Classification Accuracy\",\n    value: \"94.2%\",\n    improvement: \"Industry leading\"\n  }, {\n    metric: \"Traffic Signal Optimization\",\n    value: \"35% faster\",\n    improvement: \"Route time reduction\"\n  }, {\n    metric: \"System Uptime\",\n    value: \"99.8%\",\n    improvement: \"Enterprise grade\"\n  }, {\n    metric: \"Multi-language Support\",\n    value: \"6 languages\",\n    improvement: \"Universal access\"\n  }, {\n    metric: \"Hospital Integration\",\n    value: \"Real-time\",\n    improvement: \"Seamless handoff\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"storyboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"storyboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCCB System Storyboard & Flow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-time\",\n        children: formatTime(currentTime)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"storyboard-intro\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"intro-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDE91 Smart Emergency Medical Services - Complete System Flow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"This comprehensive storyboard illustrates the complete flow of our AI-powered Emergency Medical Services dispatch system, from initial emergency detection through final hospital delivery. Each step is optimized using advanced AI, machine learning, and smart city integration technologies.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"storyboard-flow\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDD04 Emergency Response Flow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flow-timeline\",\n        children: storyboardSteps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flow-step ${activeStep === step.id ? 'active' : ''}`,\n          onClick: () => setActiveStep(activeStep === step.id ? null : step.id),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-connector\",\n            style: {\n              backgroundColor: step.color\n            },\n            children: index < storyboardSteps.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"connector-line\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-card\",\n            style: {\n              borderColor: step.color\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-icon\",\n                style: {\n                  backgroundColor: step.color\n                },\n                children: step.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"step-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: step.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"step-timeframe\",\n                  style: {\n                    color: step.color\n                  },\n                  children: [\"\\u23F1\\uFE0F \", step.timeframe]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this), activeStep === step.id && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"step-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"details-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: \"\\uD83D\\uDCCB Process Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  children: step.details.map((detail, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n                    children: detail\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"technologies-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                  children: \"\\u2699\\uFE0F Technologies Used\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tech-tags\",\n                  children: step.technologies.map((tech, idx) => /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"tech-tag\",\n                    style: {\n                      backgroundColor: `${step.color}20`,\n                      color: step.color\n                    },\n                    children: tech\n                  }, idx, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 27\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, step.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features-overview\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDF1F System Features & Capabilities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-grid\",\n        children: systemFeatures.map((category, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: category.category\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            children: category.features.map((feature, idx) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: feature\n            }, idx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"performance-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCCA Performance Metrics & Impact\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"metrics-grid\",\n        children: performanceMetrics.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"metric-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-value\",\n            children: item.value\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-name\",\n            children: item.metric\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-improvement\",\n            children: item.improvement\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 273,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"architecture-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDFD7\\uFE0F System Architecture\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"architecture-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"architecture-description\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Microservices-Based Architecture\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Our system is built on a modern microservices architecture that ensures scalability, reliability, and seamless integration with existing smart city infrastructure.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"architecture-components\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"component-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83C\\uDFAF Core Services\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Emergency Classification Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Dispatch Optimization Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Voice Processing Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Traffic Signal Control Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"component-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDD17 Integration Layer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Hospital Management APIs\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Smart City Gateway\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Mobile App SDK\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"IoT Sensor Integration\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"component-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n              children: \"\\uD83D\\uDCCA Analytics & AI\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Heatmap Analytics Service\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Predictive Modeling Engine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Real-time Decision Engine\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                children: \"Performance Monitoring\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 321,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 283,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"impact-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83C\\uDFAF Impact & Benefits\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"impact-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"impact-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"impact-icon\",\n            children: \"\\u26A1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Faster Response Times\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"AI-powered dispatch reduces average response time by 45%, potentially saving thousands of lives annually.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"impact-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"impact-icon\",\n            children: \"\\uD83C\\uDF0D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Universal Accessibility\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Multilingual voice interface removes barriers, ensuring emergency services are accessible to all citizens.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"impact-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"impact-icon\",\n            children: \"\\uD83C\\uDFD9\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Smart City Integration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Seamless integration with traffic systems and IoT infrastructure creates a truly connected emergency response network.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 344,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"impact-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"impact-icon\",\n            children: \"\\uD83D\\uDCC8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Scalable Solution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Microservices architecture allows the system to scale across multiple cities and regions efficiently.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 331,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tech-stack-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"\\uD83D\\uDCBB Technology Stack\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 360,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tech-stack-grid\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Frontend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-items\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"React.js\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"CSS3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 366,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Google Maps API\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"WebSocket\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"AI & ML\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-items\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"OpenAI Whisper\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"TensorFlow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"NLP Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Predictive Analytics\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Backend\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 383,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-items\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Node.js\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 385,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Microservices\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"REST APIs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 387,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Real-time Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-category\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"Integration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 393,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-items\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"IoT Sensors\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Traffic Systems\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Hospital APIs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"tech-item\",\n              children: \"Mobile SDKs\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 359,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(Storyboard, \"WoJB7RqybIId+C64QIPS3KR7Fzk=\");\n_c = Storyboard;\nexport default Storyboard;\nvar _c;\n$RefreshReg$(_c, \"Storyboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "Storyboard", "_s", "currentTime", "setCurrentTime", "Date", "activeStep", "setActiveStep", "timer", "setInterval", "clearInterval", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "storyboardSteps", "id", "title", "icon", "description", "details", "technologies", "timeframe", "color", "systemFeatures", "category", "features", "performanceMetrics", "metric", "value", "improvement", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "index", "onClick", "style", "backgroundColor", "length", "borderColor", "detail", "idx", "tech", "feature", "item", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/components/Storyboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './Storyboard.css';\n\nconst Storyboard = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeStep, setActiveStep] = useState(null);\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const storyboardSteps = [\n    {\n      id: 1,\n      title: \"Emergency Detection\",\n      icon: \"🚨\",\n      description: \"Multiple channels detect emergencies across the city\",\n      details: [\n        \"Mobile App: Citizens report emergencies directly\",\n        \"Voice Calls: Multilingual AI processes emergency calls\",\n        \"IoT Sensors: Smart city sensors detect accidents\",\n        \"Smart Watches: Health monitoring alerts\",\n        \"Hospital Integration: Direct medical facility requests\"\n      ],\n      technologies: [\"Whisper ASR\", \"NLP Classification\", \"IoT Integration\", \"Mobile SDK\"],\n      timeframe: \"0-30 seconds\",\n      color: \"#dc2626\"\n    },\n    {\n      id: 2,\n      title: \"AI Classification\",\n      icon: \"🧠\",\n      description: \"Advanced AI analyzes and classifies emergency severity\",\n      details: [\n        \"Symptom Analysis: AI processes reported symptoms\",\n        \"Risk Assessment: Calculates emergency severity score\",\n        \"Medical Classification: Categorizes emergency type\",\n        \"Priority Assignment: Assigns critical/high/medium/low priority\",\n        \"Hospital Recommendation: Suggests optimal medical facility\"\n      ],\n      technologies: [\"Machine Learning\", \"Medical AI\", \"Risk Scoring\", \"Decision Trees\"],\n      timeframe: \"5-15 seconds\",\n      color: \"#7c3aed\"\n    },\n    {\n      id: 3,\n      title: \"Smart Dispatch\",\n      icon: \"🎯\",\n      description: \"AI-powered ambulance selection and route optimization\",\n      details: [\n        \"Ambulance Selection: AI chooses optimal ambulance based on multiple factors\",\n        \"Distance Calculation: Real-time location-based routing\",\n        \"Specialization Matching: Matches medical equipment to emergency type\",\n        \"Traffic Analysis: Considers current traffic conditions\",\n        \"Fuel & Maintenance: Factors in ambulance readiness status\"\n      ],\n      technologies: [\"Optimization Algorithms\", \"Real-time Analytics\", \"GPS Integration\", \"Fleet Management\"],\n      timeframe: \"10-20 seconds\",\n      color: \"#059669\"\n    },\n    {\n      id: 4,\n      title: \"Traffic Signal Control\",\n      icon: \"🚦\",\n      description: \"AI hijacks traffic signals to create green corridors\",\n      details: [\n        \"Route Prediction: AI predicts ambulance path\",\n        \"Signal Coordination: Synchronizes traffic lights along route\",\n        \"Green Wave Creation: Creates continuous green light corridor\",\n        \"Traffic Flow Management: Minimizes disruption to regular traffic\",\n        \"Real-time Adjustment: Adapts to route changes dynamically\"\n      ],\n      technologies: [\"Traffic AI\", \"Signal Control Systems\", \"Route Prediction\", \"Smart City Integration\"],\n      timeframe: \"Continuous during transport\",\n      color: \"#f59e0b\"\n    },\n    {\n      id: 5,\n      title: \"Real-time Tracking\",\n      icon: \"📍\",\n      description: \"Live monitoring and coordination throughout the emergency\",\n      details: [\n        \"GPS Tracking: Real-time ambulance location monitoring\",\n        \"ETA Updates: Continuous arrival time calculations\",\n        \"Communication Hub: Coordination between dispatch, ambulance, and hospital\",\n        \"Status Updates: Live updates to all stakeholders\",\n        \"Route Optimization: Dynamic route adjustments based on conditions\"\n      ],\n      technologies: [\"GPS Systems\", \"Real-time Communication\", \"WebSocket\", \"Mobile Notifications\"],\n      timeframe: \"Throughout journey\",\n      color: \"#3b82f6\"\n    },\n    {\n      id: 6,\n      title: \"Hospital Preparation\",\n      icon: \"🏥\",\n      description: \"Automated hospital notification and preparation\",\n      details: [\n        \"Hospital Alert: Automatic notification to receiving hospital\",\n        \"Medical Team Preparation: Staff alerted based on emergency type\",\n        \"Resource Allocation: Equipment and room preparation\",\n        \"Patient Information: Medical history and current status shared\",\n        \"Arrival Coordination: Seamless handoff from ambulance to hospital\"\n      ],\n      technologies: [\"Hospital Integration APIs\", \"Medical Records\", \"Resource Management\", \"Communication Systems\"],\n      timeframe: \"5-10 minutes before arrival\",\n      color: \"#dc2626\"\n    }\n  ];\n\n  const systemFeatures = [\n    {\n      category: \"AI & Machine Learning\",\n      features: [\n        \"Emergency Classification with 94% accuracy\",\n        \"Predictive heatmap analytics for resource deployment\",\n        \"Smart ambulance selection algorithms\",\n        \"Traffic pattern analysis and prediction\",\n        \"Medical risk assessment and triage\"\n      ]\n    },\n    {\n      category: \"Multilingual Support\",\n      features: [\n        \"Voice recognition in 6+ Indian languages\",\n        \"OpenAI Whisper ASR integration\",\n        \"Real-time language detection\",\n        \"Cultural context understanding\",\n        \"Accessibility for all literacy levels\"\n      ]\n    },\n    {\n      category: \"Smart City Integration\",\n      features: [\n        \"Traffic signal control and coordination\",\n        \"IoT sensor network integration\",\n        \"Smart city platform compatibility\",\n        \"Real-time data sharing with city systems\",\n        \"Scalable microservices architecture\"\n      ]\n    },\n    {\n      category: \"Mobile & Web Platforms\",\n      features: [\n        \"Citizen emergency reporting app\",\n        \"Professional dispatch center interface\",\n        \"Hospital integration dashboard\",\n        \"Real-time tracking and notifications\",\n        \"Cross-platform compatibility\"\n      ]\n    }\n  ];\n\n  const performanceMetrics = [\n    { metric: \"Average Response Time\", value: \"2.8 minutes\", improvement: \"45% faster\" },\n    { metric: \"AI Classification Accuracy\", value: \"94.2%\", improvement: \"Industry leading\" },\n    { metric: \"Traffic Signal Optimization\", value: \"35% faster\", improvement: \"Route time reduction\" },\n    { metric: \"System Uptime\", value: \"99.8%\", improvement: \"Enterprise grade\" },\n    { metric: \"Multi-language Support\", value: \"6 languages\", improvement: \"Universal access\" },\n    { metric: \"Hospital Integration\", value: \"Real-time\", improvement: \"Seamless handoff\" }\n  ];\n\n  return (\n    <div className=\"storyboard-container\">\n      <div className=\"storyboard-header\">\n        <h1>📋 System Storyboard & Flow</h1>\n        <div className=\"header-time\">{formatTime(currentTime)}</div>\n      </div>\n\n      <div className=\"storyboard-intro\">\n        <div className=\"intro-content\">\n          <h2>🚑 Smart Emergency Medical Services - Complete System Flow</h2>\n          <p>\n            This comprehensive storyboard illustrates the complete flow of our AI-powered Emergency Medical Services \n            dispatch system, from initial emergency detection through final hospital delivery. Each step is optimized \n            using advanced AI, machine learning, and smart city integration technologies.\n          </p>\n        </div>\n      </div>\n\n      {/* Main Storyboard Flow */}\n      <div className=\"storyboard-flow\">\n        <h3>🔄 Emergency Response Flow</h3>\n        <div className=\"flow-timeline\">\n          {storyboardSteps.map((step, index) => (\n            <div \n              key={step.id} \n              className={`flow-step ${activeStep === step.id ? 'active' : ''}`}\n              onClick={() => setActiveStep(activeStep === step.id ? null : step.id)}\n            >\n              <div className=\"step-connector\" style={{ backgroundColor: step.color }}>\n                {index < storyboardSteps.length - 1 && <div className=\"connector-line\"></div>}\n              </div>\n              \n              <div className=\"step-card\" style={{ borderColor: step.color }}>\n                <div className=\"step-header\">\n                  <div className=\"step-icon\" style={{ backgroundColor: step.color }}>\n                    {step.icon}\n                  </div>\n                  <div className=\"step-info\">\n                    <h4>{step.title}</h4>\n                    <p>{step.description}</p>\n                    <div className=\"step-timeframe\" style={{ color: step.color }}>\n                      ⏱️ {step.timeframe}\n                    </div>\n                  </div>\n                </div>\n\n                {activeStep === step.id && (\n                  <div className=\"step-details\">\n                    <div className=\"details-section\">\n                      <h5>📋 Process Details</h5>\n                      <ul>\n                        {step.details.map((detail, idx) => (\n                          <li key={idx}>{detail}</li>\n                        ))}\n                      </ul>\n                    </div>\n                    \n                    <div className=\"technologies-section\">\n                      <h5>⚙️ Technologies Used</h5>\n                      <div className=\"tech-tags\">\n                        {step.technologies.map((tech, idx) => (\n                          <span key={idx} className=\"tech-tag\" style={{ backgroundColor: `${step.color}20`, color: step.color }}>\n                            {tech}\n                          </span>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* System Features Overview */}\n      <div className=\"features-overview\">\n        <h3>🌟 System Features & Capabilities</h3>\n        <div className=\"features-grid\">\n          {systemFeatures.map((category, index) => (\n            <div key={index} className=\"feature-category\">\n              <h4>{category.category}</h4>\n              <ul>\n                {category.features.map((feature, idx) => (\n                  <li key={idx}>{feature}</li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Performance Metrics */}\n      <div className=\"performance-section\">\n        <h3>📊 Performance Metrics & Impact</h3>\n        <div className=\"metrics-grid\">\n          {performanceMetrics.map((item, index) => (\n            <div key={index} className=\"metric-card\">\n              <div className=\"metric-value\">{item.value}</div>\n              <div className=\"metric-name\">{item.metric}</div>\n              <div className=\"metric-improvement\">{item.improvement}</div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Architecture Overview */}\n      <div className=\"architecture-section\">\n        <h3>🏗️ System Architecture</h3>\n        <div className=\"architecture-content\">\n          <div className=\"architecture-description\">\n            <h4>Microservices-Based Architecture</h4>\n            <p>\n              Our system is built on a modern microservices architecture that ensures scalability, \n              reliability, and seamless integration with existing smart city infrastructure.\n            </p>\n          </div>\n          \n          <div className=\"architecture-components\">\n            <div className=\"component-group\">\n              <h5>🎯 Core Services</h5>\n              <ul>\n                <li>Emergency Classification Service</li>\n                <li>Dispatch Optimization Service</li>\n                <li>Voice Processing Service</li>\n                <li>Traffic Signal Control Service</li>\n              </ul>\n            </div>\n            \n            <div className=\"component-group\">\n              <h5>🔗 Integration Layer</h5>\n              <ul>\n                <li>Hospital Management APIs</li>\n                <li>Smart City Gateway</li>\n                <li>Mobile App SDK</li>\n                <li>IoT Sensor Integration</li>\n              </ul>\n            </div>\n            \n            <div className=\"component-group\">\n              <h5>📊 Analytics & AI</h5>\n              <ul>\n                <li>Heatmap Analytics Service</li>\n                <li>Predictive Modeling Engine</li>\n                <li>Real-time Decision Engine</li>\n                <li>Performance Monitoring</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Impact & Benefits */}\n      <div className=\"impact-section\">\n        <h3>🎯 Impact & Benefits</h3>\n        <div className=\"impact-grid\">\n          <div className=\"impact-card\">\n            <div className=\"impact-icon\">⚡</div>\n            <h4>Faster Response Times</h4>\n            <p>AI-powered dispatch reduces average response time by 45%, potentially saving thousands of lives annually.</p>\n          </div>\n\n          <div className=\"impact-card\">\n            <div className=\"impact-icon\">🌍</div>\n            <h4>Universal Accessibility</h4>\n            <p>Multilingual voice interface removes barriers, ensuring emergency services are accessible to all citizens.</p>\n          </div>\n\n          <div className=\"impact-card\">\n            <div className=\"impact-icon\">🏙️</div>\n            <h4>Smart City Integration</h4>\n            <p>Seamless integration with traffic systems and IoT infrastructure creates a truly connected emergency response network.</p>\n          </div>\n\n          <div className=\"impact-card\">\n            <div className=\"impact-icon\">📈</div>\n            <h4>Scalable Solution</h4>\n            <p>Microservices architecture allows the system to scale across multiple cities and regions efficiently.</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Technology Stack */}\n      <div className=\"tech-stack-section\">\n        <h3>💻 Technology Stack</h3>\n        <div className=\"tech-stack-grid\">\n          <div className=\"tech-category\">\n            <h4>Frontend</h4>\n            <div className=\"tech-items\">\n              <span className=\"tech-item\">React.js</span>\n              <span className=\"tech-item\">CSS3</span>\n              <span className=\"tech-item\">Google Maps API</span>\n              <span className=\"tech-item\">WebSocket</span>\n            </div>\n          </div>\n\n          <div className=\"tech-category\">\n            <h4>AI & ML</h4>\n            <div className=\"tech-items\">\n              <span className=\"tech-item\">OpenAI Whisper</span>\n              <span className=\"tech-item\">TensorFlow</span>\n              <span className=\"tech-item\">NLP Processing</span>\n              <span className=\"tech-item\">Predictive Analytics</span>\n            </div>\n          </div>\n\n          <div className=\"tech-category\">\n            <h4>Backend</h4>\n            <div className=\"tech-items\">\n              <span className=\"tech-item\">Node.js</span>\n              <span className=\"tech-item\">Microservices</span>\n              <span className=\"tech-item\">REST APIs</span>\n              <span className=\"tech-item\">Real-time Processing</span>\n            </div>\n          </div>\n\n          <div className=\"tech-category\">\n            <h4>Integration</h4>\n            <div className=\"tech-items\">\n              <span className=\"tech-item\">IoT Sensors</span>\n              <span className=\"tech-item\">Traffic Systems</span>\n              <span className=\"tech-item\">Hospital APIs</span>\n              <span className=\"tech-item\">Mobile SDKs</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Storyboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMU,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BL,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMK,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,eAAe,GAAG,CACtB;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sDAAsD;IACnEC,OAAO,EAAE,CACP,kDAAkD,EAClD,wDAAwD,EACxD,kDAAkD,EAClD,yCAAyC,EACzC,wDAAwD,CACzD;IACDC,YAAY,EAAE,CAAC,aAAa,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,YAAY,CAAC;IACpFC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mBAAmB;IAC1BC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,wDAAwD;IACrEC,OAAO,EAAE,CACP,kDAAkD,EAClD,sDAAsD,EACtD,oDAAoD,EACpD,gEAAgE,EAChE,4DAA4D,CAC7D;IACDC,YAAY,EAAE,CAAC,kBAAkB,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC;IAClFC,SAAS,EAAE,cAAc;IACzBC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,gBAAgB;IACvBC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,uDAAuD;IACpEC,OAAO,EAAE,CACP,6EAA6E,EAC7E,wDAAwD,EACxD,sEAAsE,EACtE,wDAAwD,EACxD,2DAA2D,CAC5D;IACDC,YAAY,EAAE,CAAC,yBAAyB,EAAE,qBAAqB,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;IACvGC,SAAS,EAAE,eAAe;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,sDAAsD;IACnEC,OAAO,EAAE,CACP,8CAA8C,EAC9C,8DAA8D,EAC9D,8DAA8D,EAC9D,kEAAkE,EAClE,2DAA2D,CAC5D;IACDC,YAAY,EAAE,CAAC,YAAY,EAAE,wBAAwB,EAAE,kBAAkB,EAAE,wBAAwB,CAAC;IACpGC,SAAS,EAAE,6BAA6B;IACxCC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,2DAA2D;IACxEC,OAAO,EAAE,CACP,uDAAuD,EACvD,mDAAmD,EACnD,2EAA2E,EAC3E,kDAAkD,EAClD,mEAAmE,CACpE;IACDC,YAAY,EAAE,CAAC,aAAa,EAAE,yBAAyB,EAAE,WAAW,EAAE,sBAAsB,CAAC;IAC7FC,SAAS,EAAE,oBAAoB;IAC/BC,KAAK,EAAE;EACT,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE,IAAI;IACVC,WAAW,EAAE,iDAAiD;IAC9DC,OAAO,EAAE,CACP,8DAA8D,EAC9D,iEAAiE,EACjE,qDAAqD,EACrD,gEAAgE,EAChE,mEAAmE,CACpE;IACDC,YAAY,EAAE,CAAC,2BAA2B,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,uBAAuB,CAAC;IAC9GC,SAAS,EAAE,6BAA6B;IACxCC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB;IACEC,QAAQ,EAAE,uBAAuB;IACjCC,QAAQ,EAAE,CACR,4CAA4C,EAC5C,sDAAsD,EACtD,sCAAsC,EACtC,yCAAyC,EACzC,oCAAoC;EAExC,CAAC,EACD;IACED,QAAQ,EAAE,sBAAsB;IAChCC,QAAQ,EAAE,CACR,0CAA0C,EAC1C,gCAAgC,EAChC,8BAA8B,EAC9B,gCAAgC,EAChC,uCAAuC;EAE3C,CAAC,EACD;IACED,QAAQ,EAAE,wBAAwB;IAClCC,QAAQ,EAAE,CACR,yCAAyC,EACzC,gCAAgC,EAChC,mCAAmC,EACnC,0CAA0C,EAC1C,qCAAqC;EAEzC,CAAC,EACD;IACED,QAAQ,EAAE,wBAAwB;IAClCC,QAAQ,EAAE,CACR,iCAAiC,EACjC,wCAAwC,EACxC,gCAAgC,EAChC,sCAAsC,EACtC,8BAA8B;EAElC,CAAC,CACF;EAED,MAAMC,kBAAkB,GAAG,CACzB;IAAEC,MAAM,EAAE,uBAAuB;IAAEC,KAAK,EAAE,aAAa;IAAEC,WAAW,EAAE;EAAa,CAAC,EACpF;IAAEF,MAAM,EAAE,4BAA4B;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAmB,CAAC,EACzF;IAAEF,MAAM,EAAE,6BAA6B;IAAEC,KAAK,EAAE,YAAY;IAAEC,WAAW,EAAE;EAAuB,CAAC,EACnG;IAAEF,MAAM,EAAE,eAAe;IAAEC,KAAK,EAAE,OAAO;IAAEC,WAAW,EAAE;EAAmB,CAAC,EAC5E;IAAEF,MAAM,EAAE,wBAAwB;IAAEC,KAAK,EAAE,aAAa;IAAEC,WAAW,EAAE;EAAmB,CAAC,EAC3F;IAAEF,MAAM,EAAE,sBAAsB;IAAEC,KAAK,EAAE,WAAW;IAAEC,WAAW,EAAE;EAAmB,CAAC,CACxF;EAED,oBACEjC,OAAA;IAAKkC,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnCnC,OAAA;MAAKkC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnC,OAAA;QAAAmC,QAAA,EAAI;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpCvC,OAAA;QAAKkC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAExB,UAAU,CAACR,WAAW;MAAC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzD,CAAC,eAENvC,OAAA;MAAKkC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BnC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BnC,OAAA;UAAAmC,QAAA,EAAI;QAA0D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnEvC,OAAA;UAAAmC,QAAA,EAAG;QAIH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnC,OAAA;QAAAmC,QAAA,EAAI;MAA0B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnCvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BjB,eAAe,CAACsB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/B1C,OAAA;UAEEkC,SAAS,EAAE,aAAa5B,UAAU,KAAKmC,IAAI,CAACtB,EAAE,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjEwB,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAACD,UAAU,KAAKmC,IAAI,CAACtB,EAAE,GAAG,IAAI,GAAGsB,IAAI,CAACtB,EAAE,CAAE;UAAAgB,QAAA,gBAEtEnC,OAAA;YAAKkC,SAAS,EAAC,gBAAgB;YAACU,KAAK,EAAE;cAAEC,eAAe,EAAEJ,IAAI,CAACf;YAAM,CAAE;YAAAS,QAAA,EACpEO,KAAK,GAAGxB,eAAe,CAAC4B,MAAM,GAAG,CAAC,iBAAI9C,OAAA;cAAKkC,SAAS,EAAC;YAAgB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,WAAW;YAACU,KAAK,EAAE;cAAEG,WAAW,EAAEN,IAAI,CAACf;YAAM,CAAE;YAAAS,QAAA,gBAC5DnC,OAAA;cAAKkC,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BnC,OAAA;gBAAKkC,SAAS,EAAC,WAAW;gBAACU,KAAK,EAAE;kBAAEC,eAAe,EAAEJ,IAAI,CAACf;gBAAM,CAAE;gBAAAS,QAAA,EAC/DM,IAAI,CAACpB;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACNvC,OAAA;gBAAKkC,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBnC,OAAA;kBAAAmC,QAAA,EAAKM,IAAI,CAACrB;gBAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrBvC,OAAA;kBAAAmC,QAAA,EAAIM,IAAI,CAACnB;gBAAW;kBAAAc,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzBvC,OAAA;kBAAKkC,SAAS,EAAC,gBAAgB;kBAACU,KAAK,EAAE;oBAAElB,KAAK,EAAEe,IAAI,CAACf;kBAAM,CAAE;kBAAAS,QAAA,GAAC,eACzD,EAACM,IAAI,CAAChB,SAAS;gBAAA;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,EAELjC,UAAU,KAAKmC,IAAI,CAACtB,EAAE,iBACrBnB,OAAA;cAAKkC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BnC,OAAA;gBAAKkC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BnC,OAAA;kBAAAmC,QAAA,EAAI;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC3BvC,OAAA;kBAAAmC,QAAA,EACGM,IAAI,CAAClB,OAAO,CAACiB,GAAG,CAAC,CAACQ,MAAM,EAAEC,GAAG,kBAC5BjD,OAAA;oBAAAmC,QAAA,EAAea;kBAAM,GAAZC,GAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAc,CAC3B;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eAENvC,OAAA;gBAAKkC,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnCnC,OAAA;kBAAAmC,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BvC,OAAA;kBAAKkC,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACvBM,IAAI,CAACjB,YAAY,CAACgB,GAAG,CAAC,CAACU,IAAI,EAAED,GAAG,kBAC/BjD,OAAA;oBAAgBkC,SAAS,EAAC,UAAU;oBAACU,KAAK,EAAE;sBAAEC,eAAe,EAAE,GAAGJ,IAAI,CAACf,KAAK,IAAI;sBAAEA,KAAK,EAAEe,IAAI,CAACf;oBAAM,CAAE;oBAAAS,QAAA,EACnGe;kBAAI,GADID,GAAG;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAER,CACP;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA,GA7CDE,IAAI,CAACtB,EAAE;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8CT,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCnC,OAAA;QAAAmC,QAAA,EAAI;MAAiC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1CvC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BR,cAAc,CAACa,GAAG,CAAC,CAACZ,QAAQ,EAAEc,KAAK,kBAClC1C,OAAA;UAAiBkC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC3CnC,OAAA;YAAAmC,QAAA,EAAKP,QAAQ,CAACA;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5BvC,OAAA;YAAAmC,QAAA,EACGP,QAAQ,CAACC,QAAQ,CAACW,GAAG,CAAC,CAACW,OAAO,EAAEF,GAAG,kBAClCjD,OAAA;cAAAmC,QAAA,EAAegB;YAAO,GAAbF,GAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GANGG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCnC,OAAA;QAAAmC,QAAA,EAAI;MAA+B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACxCvC,OAAA;QAAKkC,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BL,kBAAkB,CAACU,GAAG,CAAC,CAACY,IAAI,EAAEV,KAAK,kBAClC1C,OAAA;UAAiBkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBACtCnC,OAAA;YAAKkC,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEiB,IAAI,CAACpB;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDvC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEiB,IAAI,CAACrB;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDvC,OAAA;YAAKkC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAEiB,IAAI,CAACnB;UAAW;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAHpDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACnCnC,OAAA;QAAAmC,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChCvC,OAAA;QAAKkC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCnC,OAAA;UAAKkC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCnC,OAAA;YAAAmC,QAAA,EAAI;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCvC,OAAA;YAAAmC,QAAA,EAAG;UAGH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCnC,OAAA;YAAKkC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnC,OAAA;cAAAmC,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzBvC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAAmC,QAAA,EAAI;cAAgC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzCvC,OAAA;gBAAAmC,QAAA,EAAI;cAA6B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtCvC,OAAA;gBAAAmC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCvC,OAAA;gBAAAmC,QAAA,EAAI;cAA8B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnC,OAAA;cAAAmC,QAAA,EAAI;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7BvC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAAmC,QAAA,EAAI;cAAwB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjCvC,OAAA;gBAAAmC,QAAA,EAAI;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3BvC,OAAA;gBAAAmC,QAAA,EAAI;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvBvC,OAAA;gBAAAmC,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAENvC,OAAA;YAAKkC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BnC,OAAA;cAAAmC,QAAA,EAAI;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC1BvC,OAAA;cAAAmC,QAAA,gBACEnC,OAAA;gBAAAmC,QAAA,EAAI;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCvC,OAAA;gBAAAmC,QAAA,EAAI;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnCvC,OAAA;gBAAAmC,QAAA,EAAI;cAAyB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClCvC,OAAA;gBAAAmC,QAAA,EAAI;cAAsB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BnC,OAAA;QAAAmC,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BvC,OAAA;QAAKkC,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BnC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpCvC,OAAA;YAAAmC,QAAA,EAAI;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BvC,OAAA;YAAAmC,QAAA,EAAG;UAAyG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7G,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCvC,OAAA;YAAAmC,QAAA,EAAI;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChCvC,OAAA;YAAAmC,QAAA,EAAG;UAA0G;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9G,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtCvC,OAAA;YAAAmC,QAAA,EAAI;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/BvC,OAAA;YAAAmC,QAAA,EAAG;UAAsH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1H,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnC,OAAA;YAAKkC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrCvC,OAAA;YAAAmC,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BvC,OAAA;YAAAmC,QAAA,EAAG;UAAqG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvC,OAAA;MAAKkC,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCnC,OAAA;QAAAmC,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BvC,OAAA;QAAKkC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BnC,OAAA;UAAKkC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnC,OAAA;YAAAmC,QAAA,EAAI;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACjBvC,OAAA;YAAKkC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3CvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnC,OAAA;YAAAmC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBvC,OAAA;YAAKkC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC7CvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnC,OAAA;YAAAmC,QAAA,EAAI;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChBvC,OAAA;YAAKkC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1CvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5CvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAKkC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BnC,OAAA;YAAAmC,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpBvC,OAAA;YAAKkC,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBnC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvC,OAAA;cAAMkC,SAAS,EAAC,WAAW;cAAAC,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CAjZID,UAAU;AAAAoD,EAAA,GAAVpD,UAAU;AAmZhB,eAAeA,UAAU;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}