{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport \"./App.css\";\nimport PatientTransportOptimizer from \"./components/PatientTransportOptimizer\";\nimport TrafficSignalHijacking from \"./components/TrafficSignalHijacking\";\nimport MobileApp from \"./components/MobileApp\";\n\n// Emergency locations and hospitals in Chennai\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst emergencyLocations = [{\n  id: 1,\n  name: \"Chennai Central Railway Station\",\n  coords: {\n    lat: 13.0827,\n    lng: 80.2707\n  },\n  type: \"transport\"\n}, {\n  id: 2,\n  name: \"Marina Beach\",\n  coords: {\n    lat: 13.0500,\n    lng: 80.2824\n  },\n  type: \"public\"\n}, {\n  id: 3,\n  name: \"T Nagar Commercial Complex\",\n  coords: {\n    lat: 13.0418,\n    lng: 80.2341\n  },\n  type: \"commercial\"\n}, {\n  id: 4,\n  name: \"Velachery IT Corridor\",\n  coords: {\n    lat: 12.9756,\n    lng: 80.2207\n  },\n  type: \"business\"\n}, {\n  id: 5,\n  name: \"Adyar Residential Area\",\n  coords: {\n    lat: 13.0067,\n    lng: 80.2206\n  },\n  type: \"residential\"\n}, {\n  id: 6,\n  name: \"Anna Nagar Metro Station\",\n  coords: {\n    lat: 13.0850,\n    lng: 80.2101\n  },\n  type: \"transport\"\n}, {\n  id: 7,\n  name: \"Guindy Industrial Estate\",\n  coords: {\n    lat: 13.0067,\n    lng: 80.2206\n  },\n  type: \"industrial\"\n}];\nconst hospitals = [{\n  id: 1,\n  name: \"Apollo Hospital\",\n  coords: {\n    lat: 13.0358,\n    lng: 80.2297\n  },\n  capacity: 85,\n  emergency: true\n}, {\n  id: 2,\n  name: \"Fortis Malar Hospital\",\n  coords: {\n    lat: 13.0067,\n    lng: 80.2206\n  },\n  capacity: 92,\n  emergency: true\n}, {\n  id: 3,\n  name: \"MIOT International\",\n  coords: {\n    lat: 12.9756,\n    lng: 80.2207\n  },\n  capacity: 78,\n  emergency: true\n}, {\n  id: 4,\n  name: \"Stanley Medical College\",\n  coords: {\n    lat: 13.0827,\n    lng: 80.2707\n  },\n  capacity: 65,\n  emergency: true\n}, {\n  id: 5,\n  name: \"Rajiv Gandhi Govt Hospital\",\n  coords: {\n    lat: 13.1358,\n    lng: 80.2297\n  },\n  capacity: 45,\n  emergency: true\n}];\n\n// Enhanced ambulance data with AI capabilities\nconst initialAmbulances = [{\n  id: \"AMB001\",\n  status: \"available\",\n  location: {\n    lat: 13.0827,\n    lng: 80.2707\n  },\n  driver: \"Raj Kumar\",\n  medic: \"Dr. Priya\",\n  fuel: 85,\n  lastMaintenance: \"2024-01-10\",\n  equipment: [\"AED\", \"Ventilator\", \"Cardiac Monitor\"],\n  specialization: \"cardiac\",\n  aiScore: 0.92,\n  responseTime: 3.2\n}, {\n  id: \"AMB002\",\n  status: \"dispatched\",\n  location: {\n    lat: 13.0500,\n    lng: 80.2824\n  },\n  driver: \"Suresh M\",\n  medic: \"Dr. Arun\",\n  fuel: 92,\n  lastMaintenance: \"2024-01-08\",\n  equipment: [\"Trauma Kit\", \"Spinal Board\", \"IV Fluids\"],\n  specialization: \"trauma\",\n  aiScore: 0.88,\n  responseTime: 2.8,\n  emergency: {\n    type: \"cardiac\",\n    priority: \"high\",\n    eta: \"4 min\",\n    aiConfidence: 0.94\n  }\n}, {\n  id: \"AMB003\",\n  status: \"en-route\",\n  location: {\n    lat: 13.0418,\n    lng: 80.2341\n  },\n  driver: \"Karthik S\",\n  medic: \"Dr. Meera\",\n  fuel: 67,\n  lastMaintenance: \"2024-01-12\",\n  equipment: [\"Pediatric Kit\", \"Oxygen\", \"Defibrillator\"],\n  specialization: \"pediatric\",\n  aiScore: 0.91,\n  responseTime: 3.5,\n  emergency: {\n    type: \"accident\",\n    priority: \"critical\",\n    eta: \"7 min\",\n    aiConfidence: 0.97\n  }\n}, {\n  id: \"AMB004\",\n  status: \"maintenance\",\n  location: {\n    lat: 12.9756,\n    lng: 80.2207\n  },\n  driver: \"Venkat R\",\n  medic: \"Dr. Lakshmi\",\n  fuel: 23,\n  lastMaintenance: \"2024-01-15\",\n  equipment: [\"Basic Life Support\"],\n  specialization: \"general\",\n  aiScore: 0.85,\n  responseTime: 4.1\n}];\nconst App = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [darkMode, setDarkMode] = useState(false);\n  const [map, setMap] = useState(null);\n  const [ambulances, setAmbulances] = useState(initialAmbulances);\n  const [selectedAmbulance, setSelectedAmbulance] = useState(null);\n  const [emergencyAlerts, setEmergencyAlerts] = useState([]);\n  const [trafficData, setTrafficData] = useState({\n    congestion: 65,\n    incidents: 3,\n    avgSpeed: 28\n  });\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [systemStatus, setSystemStatus] = useState(\"operational\");\n  const [mapLayers, setMapLayers] = useState({\n    traffic: true,\n    hospitals: true,\n    routes: true\n  });\n  const mapRef = useRef(null);\n  const [mapMarkers, setMapMarkers] = useState({\n    hospitals: [],\n    ambulances: [],\n    emergencies: [],\n    routes: []\n  });\n  const [activeEmergencies, setActiveEmergencies] = useState([{\n    id: \"EMG001\",\n    type: \"cardiac arrest\",\n    priority: \"critical\",\n    location: \"T Nagar Commercial Complex\",\n    coords: {\n      lat: 13.0418,\n      lng: 80.2341\n    },\n    reportedAt: new Date(Date.now() - 300000),\n    // 5 minutes ago\n    assignedAmbulance: \"AMB002\",\n    status: \"dispatched\",\n    aiClassification: {\n      confidence: 0.94,\n      symptoms: [\"chest pain\", \"shortness of breath\", \"unconscious\"],\n      riskScore: 0.89,\n      recommendedHospital: \"Apollo Hospital\",\n      estimatedSeverity: \"high\"\n    },\n    source: \"mobile_app\",\n    patientInfo: {\n      age: 65,\n      gender: \"male\",\n      vitals: {\n        heartRate: 45,\n        bp: \"80/40\"\n      }\n    }\n  }, {\n    id: \"EMG002\",\n    type: \"road accident\",\n    priority: \"high\",\n    location: \"Anna Nagar Metro Station\",\n    coords: {\n      lat: 13.0850,\n      lng: 80.2101\n    },\n    reportedAt: new Date(Date.now() - 180000),\n    // 3 minutes ago\n    assignedAmbulance: \"AMB003\",\n    status: \"en-route\",\n    aiClassification: {\n      confidence: 0.97,\n      symptoms: [\"multiple injuries\", \"bleeding\", \"trauma\"],\n      riskScore: 0.85,\n      recommendedHospital: \"Stanley Medical College\",\n      estimatedSeverity: \"moderate\"\n    },\n    source: \"iot_sensor\",\n    patientInfo: {\n      age: 28,\n      gender: \"female\",\n      vitals: {\n        heartRate: 110,\n        bp: \"90/60\"\n      }\n    }\n  }]);\n\n  // AI System State\n  const [aiSystem, setAiSystem] = useState({\n    status: \"active\",\n    modelsLoaded: true,\n    lastUpdate: new Date(),\n    accuracy: 0.94,\n    processedCalls: 1247,\n    falsePositives: 23,\n    avgResponseTime: 2.8\n  });\n\n  // Voice Interface State\n  const [voiceSystem, setVoiceSystem] = useState({\n    isListening: false,\n    supportedLanguages: ['English', 'Tamil', 'Hindi', 'Telugu', 'Malayalam', 'Kannada'],\n    currentLanguage: 'English',\n    whisperModel: 'whisper-large-v3',\n    accuracy: 0.96,\n    processedVoiceCalls: 342\n  });\n\n  // Heatmap Data\n  const [heatmapData, setHeatmapData] = useState({\n    emergencyHotspots: [{\n      area: 'T Nagar',\n      risk: 0.89,\n      incidents: 45,\n      ambulancesDeployed: 3\n    }, {\n      area: 'Anna Nagar',\n      risk: 0.76,\n      incidents: 32,\n      ambulancesDeployed: 2\n    }, {\n      area: 'Velachery',\n      risk: 0.82,\n      incidents: 38,\n      ambulancesDeployed: 2\n    }, {\n      area: 'Adyar',\n      risk: 0.65,\n      incidents: 28,\n      ambulancesDeployed: 2\n    }, {\n      area: 'Guindy',\n      risk: 0.71,\n      incidents: 31,\n      ambulancesDeployed: 1\n    }],\n    lastUpdated: new Date(),\n    predictionAccuracy: 0.87\n  });\n\n  // System Architecture Status\n  const [systemArchitecture, setSystemArchitecture] = useState({\n    microservices: [{\n      name: 'Emergency Classification Service',\n      status: 'active',\n      uptime: '99.9%'\n    }, {\n      name: 'Dispatch Optimization Service',\n      status: 'active',\n      uptime: '99.8%'\n    }, {\n      name: 'Voice Processing Service',\n      status: 'active',\n      uptime: '99.7%'\n    }, {\n      name: 'Heatmap Analytics Service',\n      status: 'active',\n      uptime: '99.9%'\n    }, {\n      name: 'Hospital Integration API',\n      status: 'active',\n      uptime: '99.6%'\n    }, {\n      name: 'Smart City Gateway',\n      status: 'active',\n      uptime: '99.8%'\n    }],\n    apiCalls: 15420,\n    regions: ['Chennai', 'Bangalore', 'Hyderabad'],\n    scalingStatus: 'auto-scaling enabled'\n  });\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Dark mode toggle functionality\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  // Apply theme to document\n  useEffect(() => {\n    document.documentElement.setAttribute('data-theme', darkMode ? 'dark' : 'light');\n  }, [darkMode]);\n\n  // Simulate real-time data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Update traffic data\n      setTrafficData(prev => ({\n        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),\n        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),\n        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))\n      }));\n\n      // Simulate ambulance position updates\n      setAmbulances(prev => prev.map(amb => ({\n        ...amb,\n        location: {\n          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,\n          lng: amb.location.lng + (Math.random() - 0.5) * 0.001\n        }\n      })));\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  useEffect(() => {\n    const initMap = () => {\n      const newMap = new window.google.maps.Map(document.getElementById(\"emergency-map\"), {\n        zoom: 12,\n        center: {\n          lat: 13.0827,\n          lng: 80.2707\n        },\n        mapTypeControl: false,\n        streetViewControl: false,\n        fullscreenControl: false,\n        styles: [{\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#f5f5f5\"\n          }]\n        }, {\n          elementType: \"labels.text.fill\",\n          stylers: [{\n            color: \"#616161\"\n          }]\n        }, {\n          elementType: \"labels.text.stroke\",\n          stylers: [{\n            color: \"#f5f5f5\"\n          }]\n        }, {\n          featureType: \"road\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#ffffff\"\n          }]\n        }, {\n          featureType: \"road\",\n          elementType: \"geometry.stroke\",\n          stylers: [{\n            color: \"#e0e0e0\"\n          }]\n        }, {\n          featureType: \"road.highway\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#ffd54f\"\n          }]\n        }, {\n          featureType: \"water\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#c9c9c9\"\n          }]\n        }, {\n          featureType: \"poi\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#eeeeee\"\n          }]\n        }]\n      });\n      const trafficLayer = new window.google.maps.TrafficLayer();\n      trafficLayer.setMap(newMap);\n\n      // Add hospital markers\n      hospitals.forEach(hospital => {\n        const hospitalMarker = new window.google.maps.Marker({\n          position: hospital.coords,\n          map: newMap,\n          title: hospital.name,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#dc2626\"/>\n                <path d=\"M12 6v12M6 12h12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(24, 24)\n          }\n        });\n        const infoWindow = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${hospital.name}</h4>\n              <p style=\"margin: 0; color: #64748b; font-size: 13px;\">Capacity: ${hospital.capacity}%</p>\n              <p style=\"margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;\">✓ Emergency Services Available</p>\n            </div>\n          `\n        });\n        hospitalMarker.addListener('click', () => {\n          infoWindow.open(newMap, hospitalMarker);\n        });\n      });\n\n      // Add ambulance markers with animation\n      const ambulanceMarkers = [];\n      ambulances.forEach(ambulance => {\n        const ambulanceMarker = new window.google.maps.Marker({\n          position: ambulance.location,\n          map: newMap,\n          title: `${ambulance.id} - ${ambulance.status}`,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <rect x=\"2\" y=\"12\" width=\"28\" height=\"12\" rx=\"2\" fill=\"${getStatusColor(ambulance.status)}\"/>\n                <rect x=\"6\" y=\"8\" width=\"12\" height=\"8\" rx=\"1\" fill=\"${getStatusColor(ambulance.status)}\"/>\n                <circle cx=\"8\" cy=\"26\" r=\"3\" fill=\"#374151\"/>\n                <circle cx=\"24\" cy=\"26\" r=\"3\" fill=\"#374151\"/>\n                <path d=\"M14 16h4M16 14v4\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(32, 32)\n          }\n        });\n        const ambulanceInfo = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${ambulance.id}</h4>\n              <p style=\"margin: 0; color: #64748b; font-size: 13px;\">Status: <span style=\"color: ${getStatusColor(ambulance.status)}; font-weight: 600;\">${ambulance.status.toUpperCase()}</span></p>\n              <p style=\"margin: 4px 0 0 0; color: #64748b; font-size: 13px;\">Driver: ${ambulance.driver}</p>\n              <p style=\"margin: 2px 0 0 0; color: #64748b; font-size: 13px;\">Medic: ${ambulance.medic}</p>\n              <p style=\"margin: 4px 0 0 0; color: #64748b; font-size: 13px;\">Fuel: ${ambulance.fuel}%</p>\n              ${ambulance.emergency ? `<p style=\"margin: 4px 0 0 0; color: #dc2626; font-size: 12px; font-weight: 600;\">🚨 ${ambulance.emergency.type} - ETA: ${ambulance.emergency.eta}</p>` : ''}\n            </div>\n          `\n        });\n        ambulanceMarker.addListener('click', () => {\n          ambulanceInfo.open(newMap, ambulanceMarker);\n        });\n        ambulanceMarkers.push({\n          marker: ambulanceMarker,\n          ambulance: ambulance\n        });\n      });\n\n      // Add emergency incident markers\n      activeEmergencies.forEach(emergency => {\n        const emergencyMarker = new window.google.maps.Marker({\n          position: emergency.coords,\n          map: newMap,\n          title: `Emergency: ${emergency.type}`,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <circle cx=\"14\" cy=\"14\" r=\"12\" fill=\"${getPriorityColor(emergency.priority)}\" opacity=\"0.8\"/>\n                <circle cx=\"14\" cy=\"14\" r=\"8\" fill=\"${getPriorityColor(emergency.priority)}\"/>\n                <text x=\"14\" y=\"18\" text-anchor=\"middle\" fill=\"white\" font-size=\"12\" font-weight=\"bold\">!</text>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(28, 28)\n          },\n          animation: window.google.maps.Animation.BOUNCE\n        });\n        const emergencyInfo = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${emergency.id}</h4>\n              <p style=\"margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;\">${emergency.type.toUpperCase()}</p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Priority: <span style=\"color: ${getPriorityColor(emergency.priority)}; font-weight: 600;\">${emergency.priority.toUpperCase()}</span></p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Location: ${emergency.location}</p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Reported: ${Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago</p>\n              ${emergency.assignedAmbulance ? `<p style=\"margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;\">🚑 ${emergency.assignedAmbulance} dispatched</p>` : ''}\n            </div>\n          `\n        });\n        emergencyMarker.addListener('click', () => {\n          emergencyInfo.open(newMap, emergencyMarker);\n        });\n\n        // Draw route from assigned ambulance to emergency if available\n        if (emergency.assignedAmbulance) {\n          const assignedAmb = ambulances.find(amb => amb.id === emergency.assignedAmbulance);\n          if (assignedAmb) {\n            const routePath = new window.google.maps.Polyline({\n              path: [assignedAmb.location, emergency.coords],\n              geodesic: true,\n              strokeColor: getPriorityColor(emergency.priority),\n              strokeOpacity: 0.8,\n              strokeWeight: 4,\n              icons: [{\n                icon: {\n                  path: window.google.maps.SymbolPath.FORWARD_CLOSED_ARROW,\n                  scale: 3,\n                  fillColor: getPriorityColor(emergency.priority),\n                  fillOpacity: 1,\n                  strokeWeight: 1,\n                  strokeColor: '#ffffff'\n                },\n                offset: '50%'\n              }]\n            });\n            routePath.setMap(newMap);\n          }\n        }\n      });\n\n      // Animate ambulances with realistic movement\n      setInterval(() => {\n        ambulanceMarkers.forEach(({\n          marker,\n          ambulance\n        }) => {\n          if (ambulance.status === 'dispatched' || ambulance.status === 'en-route') {\n            const currentPos = marker.getPosition();\n            // Simulate movement towards emergency location\n            const emergency = activeEmergencies.find(e => e.assignedAmbulance === ambulance.id);\n            if (emergency) {\n              const targetLat = emergency.coords.lat;\n              const targetLng = emergency.coords.lng;\n              const currentLat = currentPos.lat();\n              const currentLng = currentPos.lng();\n\n              // Move 10% closer to target each update\n              const newLat = currentLat + (targetLat - currentLat) * 0.1;\n              const newLng = currentLng + (targetLng - currentLng) * 0.1;\n              marker.setPosition({\n                lat: newLat,\n                lng: newLng\n              });\n            } else {\n              // Random patrol movement for available ambulances\n              const newLat = currentPos.lat() + (Math.random() - 0.5) * 0.001;\n              const newLng = currentPos.lng() + (Math.random() - 0.5) * 0.001;\n              marker.setPosition({\n                lat: newLat,\n                lng: newLng\n              });\n            }\n          }\n        });\n      }, 2000);\n\n      // Add traffic congestion simulation circles\n      const trafficAreas = [{\n        center: {\n          lat: 13.0827,\n          lng: 80.2707\n        },\n        radius: 500,\n        level: 'high'\n      }, {\n        center: {\n          lat: 13.0500,\n          lng: 80.2824\n        },\n        radius: 300,\n        level: 'medium'\n      }, {\n        center: {\n          lat: 13.0418,\n          lng: 80.2341\n        },\n        radius: 400,\n        level: 'high'\n      }, {\n        center: {\n          lat: 12.9756,\n          lng: 80.2207\n        },\n        radius: 200,\n        level: 'low'\n      }];\n      trafficAreas.forEach(area => {\n        const trafficCircle = new window.google.maps.Circle({\n          strokeColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',\n          strokeOpacity: 0.6,\n          strokeWeight: 2,\n          fillColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',\n          fillOpacity: 0.15,\n          map: newMap,\n          center: area.center,\n          radius: area.radius\n        });\n      });\n      setMap(newMap);\n      mapRef.current = newMap;\n    };\n    const loadGoogleMapsScript = () => {\n      if (!window.google || !window.google.maps) {\n        const script = document.createElement(\"script\");\n        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;\n        script.async = true;\n        script.defer = true;\n        document.body.appendChild(script);\n        script.onload = initMap;\n      } else {\n        initMap();\n      }\n    };\n    loadGoogleMapsScript();\n  }, []);\n\n  // Utility functions for ambulance management\n  const getStatusColor = status => {\n    switch (status) {\n      case 'available':\n        return '#00ff88';\n      case 'dispatched':\n        return '#ffaa00';\n      case 'en-route':\n        return '#ff4444';\n      case 'maintenance':\n        return '#666666';\n      default:\n        return '#00e5ff';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'critical':\n        return '#ff0044';\n      case 'high':\n        return '#ff6600';\n      case 'medium':\n        return '#ffaa00';\n      case 'low':\n        return '#00ff88';\n      default:\n        return '#00e5ff';\n    }\n  };\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  const calculateDistance = (lat1, lng1, lat2, lng2) => {\n    const R = 6371; // Earth's radius in km\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLng = (lng2 - lng1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n  };\n\n  // AI-Enhanced Emergency Classification\n  const classifyEmergency = (symptoms, vitals, context) => {\n    // Simulate AI classification algorithm\n    const emergencyTypes = {\n      'cardiac arrest': {\n        keywords: ['chest pain', 'heart', 'cardiac', 'unconscious'],\n        severity: 0.95\n      },\n      'stroke': {\n        keywords: ['speech', 'paralysis', 'face', 'weakness'],\n        severity: 0.90\n      },\n      'road accident': {\n        keywords: ['accident', 'collision', 'trauma', 'bleeding'],\n        severity: 0.85\n      },\n      'respiratory distress': {\n        keywords: ['breathing', 'asthma', 'choking'],\n        severity: 0.80\n      },\n      'overdose': {\n        keywords: ['drugs', 'poison', 'overdose', 'unconscious'],\n        severity: 0.88\n      }\n    };\n    let bestMatch = {\n      type: 'general emergency',\n      confidence: 0.5,\n      severity: 0.6\n    };\n    for (const [type, data] of Object.entries(emergencyTypes)) {\n      const matches = data.keywords.filter(keyword => symptoms.some(symptom => symptom.toLowerCase().includes(keyword))).length;\n      const confidence = matches / data.keywords.length * 0.7 + Math.random() * 0.3;\n      if (confidence > bestMatch.confidence) {\n        bestMatch = {\n          type,\n          confidence,\n          severity: data.severity\n        };\n      }\n    }\n    return bestMatch;\n  };\n\n  // AI-Driven Smart Ambulance Selection\n  const findOptimalAmbulance = emergency => {\n    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');\n    if (availableAmbulances.length === 0) return null;\n    return availableAmbulances.map(ambulance => {\n      var _emergency$aiClassifi;\n      const distance = calculateDistance(emergency.coords.lat, emergency.coords.lng, ambulance.location.lat, ambulance.location.lng);\n\n      // AI scoring factors\n      const distanceScore = Math.max(0, 1 - distance / 10); // Normalize to 0-1\n      const specializationScore = ambulance.specialization === emergency.type.split(' ')[0] ? 1 : 0.7;\n      const equipmentScore = ((_emergency$aiClassifi = emergency.aiClassification) === null || _emergency$aiClassifi === void 0 ? void 0 : _emergency$aiClassifi.riskScore) > 0.8 ? ambulance.equipment.length / 5 : 0.8;\n      const responseScore = Math.max(0, 1 - ambulance.responseTime / 5);\n      const fuelScore = ambulance.fuel > 30 ? 1 : 0.5;\n\n      // Weighted AI score\n      const totalScore = (distanceScore * 0.3 + specializationScore * 0.25 + equipmentScore * 0.2 + responseScore * 0.15 + fuelScore * 0.1) * ambulance.aiScore;\n      return {\n        ambulance,\n        score: totalScore,\n        distance,\n        eta: distance * 2\n      };\n    }).sort((a, b) => b.score - a.score)[0];\n  };\n\n  // Predictive Hospital Assignment using LSTM-like logic\n  const selectOptimalHospital = (emergency, ambulance) => {\n    const hospitalScores = hospitals.map(hospital => {\n      const distance = calculateDistance(emergency.coords.lat, emergency.coords.lng, hospital.coords.lat, hospital.coords.lng);\n\n      // Simulate LSTM prediction factors\n      const capacityScore = hospital.capacity / 100;\n      const distanceScore = Math.max(0, 1 - distance / 15);\n      const specializationScore = hospital.emergency ? 1 : 0.6;\n      const trafficFactor = trafficData.congestion > 70 ? 0.8 : 1;\n\n      // Historical pattern simulation (LSTM would use real historical data)\n      const timeOfDay = new Date().getHours();\n      const historicalScore = timeOfDay >= 8 && timeOfDay <= 18 ? 0.9 : 1; // Rush hour penalty\n\n      const totalScore = (capacityScore * 0.4 + distanceScore * 0.3 + specializationScore * 0.2 + historicalScore * 0.1) * trafficFactor;\n      return {\n        hospital,\n        score: totalScore,\n        distance,\n        eta: distance * 2.5\n      };\n    }).sort((a, b) => b.score - a.score)[0];\n    return hospitalScores;\n  };\n\n  // Enhanced AI-Driven Dispatch\n  const dispatchAmbulance = (emergencyId, ambulanceId = null) => {\n    const emergency = activeEmergencies.find(e => e.id === emergencyId);\n    if (!emergency) return;\n    let selectedAmbulance;\n    if (ambulanceId) {\n      // Manual dispatch\n      selectedAmbulance = ambulances.find(a => a.id === ambulanceId);\n    } else {\n      // AI-driven automatic dispatch\n      const optimal = findOptimalAmbulance(emergency);\n      selectedAmbulance = optimal === null || optimal === void 0 ? void 0 : optimal.ambulance;\n    }\n    if (!selectedAmbulance) return;\n\n    // Get optimal hospital using AI\n    const optimalHospital = selectOptimalHospital(emergency, selectedAmbulance);\n    setAmbulances(prev => prev.map(amb => amb.id === selectedAmbulance.id ? {\n      ...amb,\n      status: 'dispatched',\n      emergency: {\n        ...emergency,\n        assignedHospital: optimalHospital.hospital.name,\n        eta: `${Math.round(optimalHospital.eta)} min`,\n        aiConfidence: emergency.aiClassification.confidence\n      }\n    } : amb));\n    setActiveEmergencies(prev => prev.map(e => e.id === emergencyId ? {\n      ...e,\n      assignedAmbulance: selectedAmbulance.id,\n      status: 'dispatched',\n      assignedHospital: optimalHospital.hospital.name,\n      estimatedArrival: new Date(Date.now() + optimalHospital.eta * 60000)\n    } : e));\n\n    // Update AI system stats\n    setAiSystem(prev => ({\n      ...prev,\n      processedCalls: prev.processedCalls + 1,\n      lastUpdate: new Date()\n    }));\n  };\n\n  // Map control functions\n  const toggleMapLayer = layerName => {\n    setMapLayers(prev => ({\n      ...prev,\n      [layerName]: !prev[layerName]\n    }));\n\n    // Here you would typically show/hide the actual map layers\n    // For demo purposes, we'll just update the button states\n  };\n\n  // AI-Enhanced Emergency Call Simulation\n  const simulateEmergencyCall = () => {\n    const emergencyScenarios = [{\n      symptoms: ['chest pain', 'shortness of breath', 'sweating'],\n      vitals: {\n        heartRate: 45 + Math.random() * 20,\n        bp: '80/40'\n      },\n      context: 'home',\n      age: 55 + Math.random() * 20\n    }, {\n      symptoms: ['bleeding', 'trauma', 'unconscious'],\n      vitals: {\n        heartRate: 100 + Math.random() * 30,\n        bp: '90/60'\n      },\n      context: 'road',\n      age: 25 + Math.random() * 30\n    }, {\n      symptoms: ['difficulty speaking', 'weakness', 'confusion'],\n      vitals: {\n        heartRate: 80 + Math.random() * 20,\n        bp: '160/90'\n      },\n      context: 'office',\n      age: 60 + Math.random() * 15\n    }];\n    const locations = ['Anna Nagar', 'T Nagar', 'Velachery', 'Adyar', 'Guindy'];\n    const sources = ['mobile_app', 'phone_call', 'iot_sensor', 'smart_watch'];\n    const scenario = emergencyScenarios[Math.floor(Math.random() * emergencyScenarios.length)];\n    const aiClassification = classifyEmergency(scenario.symptoms, scenario.vitals, scenario.context);\n    const newEmergency = {\n      id: `EMG${String(Date.now()).slice(-3)}`,\n      type: aiClassification.type,\n      priority: aiClassification.severity > 0.9 ? 'critical' : aiClassification.severity > 0.8 ? 'high' : 'medium',\n      location: locations[Math.floor(Math.random() * locations.length)],\n      coords: {\n        lat: 13.0827 + (Math.random() - 0.5) * 0.1,\n        lng: 80.2707 + (Math.random() - 0.5) * 0.1\n      },\n      reportedAt: new Date(),\n      assignedAmbulance: null,\n      status: 'pending',\n      aiClassification: {\n        confidence: aiClassification.confidence,\n        symptoms: scenario.symptoms,\n        riskScore: aiClassification.severity,\n        recommendedHospital: hospitals[Math.floor(Math.random() * hospitals.length)].name,\n        estimatedSeverity: aiClassification.severity > 0.9 ? 'critical' : 'moderate'\n      },\n      source: sources[Math.floor(Math.random() * sources.length)],\n      patientInfo: {\n        age: Math.round(scenario.age),\n        gender: Math.random() > 0.5 ? 'male' : 'female',\n        vitals: scenario.vitals\n      }\n    };\n    setActiveEmergencies(prev => [...prev, newEmergency]);\n\n    // Auto-dispatch if AI confidence is high\n    if (aiClassification.confidence > 0.85) {\n      setTimeout(() => {\n        dispatchAmbulance(newEmergency.id);\n      }, 2000);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dispatch-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"dispatch-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"system-title\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"pulse-dot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 13\n          }, this), \"Emergency Medical Services - Dispatch Center\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 737,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"system-status\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: `status-indicator ${systemStatus}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this), \"System Status: \", systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 736,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-center\",\n        children: /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"main-navigation\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`,\n            onClick: () => setCurrentPage('dashboard'),\n            children: \"\\uD83C\\uDFE0 Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 749,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${currentPage === 'transport-optimizer' ? 'active' : ''}`,\n            onClick: () => setCurrentPage('transport-optimizer'),\n            children: \"\\uD83D\\uDE91 Transport Optimizer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 755,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${currentPage === 'traffic-hijacking' ? 'active' : ''}`,\n            onClick: () => setCurrentPage('traffic-hijacking'),\n            children: \"\\uD83D\\uDEA6 Traffic Signal AI\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 761,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `nav-btn ${currentPage === 'mobile-app' ? 'active' : ''}`,\n            onClick: () => setCurrentPage('mobile-app'),\n            children: \"\\uD83D\\uDCF1 Mobile App\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 748,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 747,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"current-time\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"time-display\",\n            children: formatTime(currentTime)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 778,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"date-display\",\n            children: currentTime.toLocaleDateString()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 779,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 777,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"theme-toggle\",\n          onClick: toggleDarkMode,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"theme-toggle-icon sun\",\n            children: \"\\u2600\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"theme-toggle-icon moon\",\n            children: \"\\uD83C\\uDF19\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 781,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 776,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 735,\n      columnNumber: 7\n    }, this), currentPage === 'dashboard' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"main-map-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel main-map-panel\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDDFA\\uFE0F Live Emergency Dispatch Map\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 796,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"map-controls\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `map-btn ${mapLayers.traffic ? 'active' : ''}`,\n                onClick: () => toggleMapLayer('traffic'),\n                children: \"Traffic Layer\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 798,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `map-btn ${mapLayers.hospitals ? 'active' : ''}`,\n                onClick: () => toggleMapLayer('hospitals'),\n                children: \"Hospitals\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 804,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `map-btn ${mapLayers.routes ? 'active' : ''}`,\n                onClick: () => toggleMapLayer('routes'),\n                children: \"Active Routes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 810,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"map-btn\",\n                onClick: simulateEmergencyCall,\n                style: {\n                  marginLeft: '12px',\n                  backgroundColor: '#dc2626',\n                  color: 'white',\n                  border: '1px solid #dc2626'\n                },\n                children: \"+ Simulate Emergency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 797,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 795,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'relative',\n              height: 'calc(100% - 80px)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"emergency-map\",\n              className: \"emergency-map\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"fleet-status-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"fleet-summary-overlay\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"available-count\",\n                  children: [ambulances.filter(a => a.status === 'available').length, \" Available\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 832,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"active-count\",\n                  children: [ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length, \" Deployed\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 835,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    color: '#dc2626',\n                    fontWeight: '600'\n                  },\n                  children: [activeEmergencies.length, \" Active Emergencies\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 838,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 831,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 830,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 826,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-legend\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"legend-icon\",\n                style: {\n                  backgroundColor: '#10b981'\n                },\n                children: \"\\uD83D\\uDE91\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 848,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Available\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 847,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"legend-icon\",\n                style: {\n                  backgroundColor: '#f59e0b'\n                },\n                children: \"\\uD83D\\uDE91\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 852,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Dispatched\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"legend-icon\",\n                style: {\n                  backgroundColor: '#ef4444'\n                },\n                children: \"\\uD83D\\uDE91\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 856,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"En Route\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 857,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"legend-icon\",\n                style: {\n                  backgroundColor: '#dc2626'\n                },\n                children: \"\\uD83C\\uDFE5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Hospital\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"legend-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"legend-icon\",\n                style: {\n                  backgroundColor: '#dc2626'\n                },\n                children: \"!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 864,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Emergency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 865,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 863,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 846,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"advanced-features-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel voice-interface-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"panel-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83C\\uDFA4 Multilingual Voice Interface\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 876,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"voice-status\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `status-indicator ${voiceSystem.isListening ? 'listening' : 'ready'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 878,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: voiceSystem.isListening ? 'Listening...' : 'Ready'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 879,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 877,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 875,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"voice-interface-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"voice-stats\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"voice-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-value\",\n                    children: voiceSystem.supportedLanguages.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-label\",\n                    children: \"Languages Supported\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 887,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 885,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"voice-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-value\",\n                    children: [(voiceSystem.accuracy * 100).toFixed(1), \"%\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 890,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-label\",\n                    children: \"Voice Recognition Accuracy\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 891,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"voice-stat\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-value\",\n                    children: voiceSystem.processedVoiceCalls\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 894,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"stat-label\",\n                    children: \"Voice Calls Processed\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 893,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 884,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"voice-features\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"voice-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83D\\uDDE3\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 902,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: \"Whisper ASR Integration\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 904,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Advanced speech recognition using OpenAI Whisper model for accurate transcription across multiple languages and accents.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 905,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"tech-badge\",\n                        children: [\"Model: \", voiceSystem.whisperModel]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 906,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 903,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 901,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"voice-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83C\\uDF10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 911,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: \"Multi-Language Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 913,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [\"Supports \", voiceSystem.supportedLanguages.join(', '), \" with real-time language detection and emergency classification.\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 914,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"language-pills\",\n                        children: voiceSystem.supportedLanguages.slice(0, 4).map(lang => /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"language-pill\",\n                          children: lang\n                        }, lang, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 917,\n                          columnNumber: 29\n                        }, this))\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 915,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 912,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 910,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"voice-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83E\\uDDE0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 924,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: \"NLP Emergency Classification\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 926,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Advanced natural language processing to understand emergency context, severity, and location from voice descriptions.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 927,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"tech-badge\",\n                        children: [\"Accuracy: \", (voiceSystem.accuracy * 100).toFixed(1), \"%\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 928,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 923,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"voice-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\u267F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 933,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        children: \"Accessibility & Inclusion\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 935,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Removes literacy and language barriers, enabling emergency access for all community members regardless of education level.\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 936,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"accessibility-badge\",\n                        children: \"Universal Access\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 937,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 934,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 932,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"voice-demo\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"voice-demo-btn\",\n                  onClick: () => setVoiceSystem(prev => ({\n                    ...prev,\n                    isListening: !prev.isListening\n                  })),\n                  children: voiceSystem.isListening ? '🛑 Stop Listening' : '🎤 Start Voice Demo'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 944,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"voice-demo-text\",\n                  children: voiceSystem.isListening ? 'Listening for emergency... Say \"Help, I need an ambulance at Marina Beach\"' : 'Click to test multilingual voice emergency reporting'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 943,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 883,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 874,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel heatmap-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"panel-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83D\\uDD25 Predictive Heatmap Analytics\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 963,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"heatmap-accuracy\",\n                children: [\"Prediction Accuracy: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"accuracy-value\",\n                  children: [(heatmapData.predictionAccuracy * 100).toFixed(1), \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 965,\n                  columnNumber: 40\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 964,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 962,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"heatmap-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"heatmap-overview\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"heatmap-description\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83C\\uDFAF Smart Resource Deployment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 972,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"AI-powered predictive analytics analyze historical emergency data, traffic patterns, population density, and temporal factors to optimize ambulance placement across the city.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 973,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 971,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 970,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hotspots-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDCCD Current Emergency Hotspots\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hotspots-list\",\n                  children: heatmapData.emergencyHotspots.map((hotspot, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hotspot-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"hotspot-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hotspot-name\",\n                        children: hotspot.area\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 983,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `risk-level ${hotspot.risk > 0.8 ? 'high' : hotspot.risk > 0.7 ? 'medium' : 'low'}`,\n                        children: hotspot.risk > 0.8 ? 'HIGH RISK' : hotspot.risk > 0.7 ? 'MEDIUM RISK' : 'LOW RISK'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 984,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 982,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"hotspot-stats\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"hotspot-stat\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-label\",\n                          children: \"Risk Score:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 990,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-value\",\n                          children: [(hotspot.risk * 100).toFixed(0), \"%\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 991,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 989,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"hotspot-stat\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-label\",\n                          children: \"Incidents (30d):\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 994,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-value\",\n                          children: hotspot.incidents\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 995,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 993,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"hotspot-stat\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-label\",\n                          children: \"Deployed Units:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 998,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"stat-value\",\n                          children: [hotspot.ambulancesDeployed, \" \\uD83D\\uDE91\"]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 999,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 997,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 988,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 981,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 977,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"heatmap-features\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"heatmap-feature-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"heatmap-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1010,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Historical Data Analysis\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1012,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Analyzes 5+ years of emergency data to identify patterns and trends\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1013,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1011,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1009,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"heatmap-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\u23F0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Temporal Predictions\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1019,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Considers time of day, day of week, and seasonal variations\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1020,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1018,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1016,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"heatmap-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83C\\uDFD9\\uFE0F\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1024,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Urban Analytics\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1026,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Factors in population density, traffic patterns, and event schedules\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1027,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1025,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1023,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"heatmap-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83C\\uDFAF\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1031,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Dynamic Redeployment\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1033,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Real-time ambulance repositioning based on changing risk patterns\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1034,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1032,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1030,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1008,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1007,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 961,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel architecture-panel\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"panel-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"\\uD83C\\uDFD7\\uFE0F Modular & Scalable Architecture\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1045,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"architecture-status\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-indicator operational\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1047,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: systemArchitecture.scalingStatus\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1048,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1046,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1044,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"architecture-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"architecture-overview\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"architecture-description\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\uD83D\\uDD27 Microservices Architecture\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1055,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Built on a distributed microservices architecture with open APIs, enabling seamless integration into smart city ecosystems and horizontal scaling across multiple regions.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1054,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"architecture-stats\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: systemArchitecture.apiCalls.toLocaleString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1061,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"API Calls Today\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1062,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1060,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: systemArchitecture.regions.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1065,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Active Regions\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1064,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-stat\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-value\",\n                      children: systemArchitecture.microservices.length\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1069,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"stat-label\",\n                      children: \"Microservices\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1070,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1068,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1059,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1053,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"microservices-grid\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\u2699\\uFE0F System Components\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1076,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"microservices-list\",\n                  children: systemArchitecture.microservices.map((service, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"microservice-card\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"service-header\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"service-name\",\n                        children: service.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `service-status ${service.status}`,\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"status-dot\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1083,\n                          columnNumber: 29\n                        }, this), service.status.toUpperCase()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1082,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1080,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"service-uptime\",\n                      children: [\"Uptime: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"uptime-value\",\n                        children: service.uptime\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1088,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1087,\n                      columnNumber: 25\n                    }, this)]\n                  }, index, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1077,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1075,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"architecture-features\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"arch-feature-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83D\\uDD17\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1098,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Open API Integration\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1100,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"RESTful APIs and GraphQL endpoints for seamless third-party integration\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1101,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1099,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83C\\uDF10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1105,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Smart City Ready\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1107,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Compatible with smart city platforms and IoT infrastructure\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1108,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1106,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1104,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83D\\uDCC8\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1112,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Auto-Scaling\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1114,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Kubernetes-based auto-scaling for handling varying emergency loads\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1115,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1113,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1111,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83D\\uDD12\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1119,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Enterprise Security\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"End-to-end encryption, HIPAA compliance, and audit trails\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1122,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1120,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\uD83C\\uDF0D\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1126,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Multi-Region Support\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1128,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: [\"Currently deployed in \", systemArchitecture.regions.join(', '), \" with expansion capabilities\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1127,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1125,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"arch-feature\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"feature-icon\",\n                      children: \"\\u26A1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1133,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                        children: \"Real-Time Processing\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1135,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        children: \"Event-driven architecture with sub-second response times\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1136,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1134,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1132,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1096,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"integration-showcase\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\uD83D\\uDD0C Integration Capabilities\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1143,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"integration-grid\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83C\\uDFE5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1146,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Hospital Management Systems\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1147,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1145,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83D\\uDEA6\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1150,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Traffic Management Systems\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1151,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1149,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83D\\uDCF1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1154,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Mobile Emergency Apps\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1155,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1153,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83C\\uDFE2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1158,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Government Emergency Services\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1159,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1157,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83C\\uDF10\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1162,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"IoT Sensor Networks\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1163,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1161,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"integration-item\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"integration-icon\",\n                      children: \"\\uD83D\\uDCCA\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"Analytics & BI Platforms\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1167,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1165,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1142,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1043,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 793,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"analytics-sidebar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel ai-panel\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83E\\uDD16 AI Dispatch System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `ai-status ${aiSystem.status}`,\n              children: aiSystem.status.toUpperCase()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1184,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1182,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-metrics\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [(aiSystem.accuracy * 100).toFixed(1), \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1190,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"AI Accuracy\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1191,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1189,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: aiSystem.processedCalls\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1194,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Calls Processed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1195,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [aiSystem.avgResponseTime, \"s\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1198,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Avg Response\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1199,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1197,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ai-metric\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: aiSystem.falsePositives\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1202,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"False Positives\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1201,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1188,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ai-features\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-icon\",\n                children: \"\\uD83E\\uDDE0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-title\",\n                  children: \"Emergency Classification\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-desc\",\n                  children: \"Real-time symptom analysis\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDFAF\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-title\",\n                  children: \"Smart Dispatch\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1217,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-desc\",\n                  children: \"Optimal ambulance selection\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1216,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"feature-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"feature-icon\",\n                children: \"\\uD83C\\uDFE5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1222,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-title\",\n                  children: \"Hospital Prediction\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"feature-desc\",\n                  children: \"LSTM-based routing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1225,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1223,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1221,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1181,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel analytics-panel\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDCCA Traffic Conditions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1234,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1233,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"analytics-grid\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [trafficData.congestion, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1238,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Traffic Congestion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1239,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`,\n                children: trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: trafficData.incidents\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1245,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Active Incidents\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`,\n                children: trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1247,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-card\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-value\",\n                children: [trafficData.avgSpeed, \" km/h\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1252,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"metric-label\",\n                children: \"Avg Speed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1253,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`,\n                children: trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1254,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1236,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1232,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel emergency-panel compact\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDEA8 Active Emergency Calls\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1264,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                gap: '8px',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"emergency-count\",\n                children: activeEmergencies.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: simulateEmergencyCall,\n                style: {\n                  background: '#dc2626',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px',\n                  padding: '4px 8px',\n                  fontSize: '11px',\n                  cursor: 'pointer',\n                  fontWeight: '600'\n                },\n                children: \"+ Demo Call\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1265,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1263,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-list\",\n            children: activeEmergencies.map(emergency => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `emergency-card priority-${emergency.priority}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"emergency-id\",\n                  children: emergency.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1288,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `priority-badge ${emergency.priority}`,\n                  children: emergency.priority.toUpperCase()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1289,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-details\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-type\",\n                  children: emergency.type\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1294,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-location\",\n                  children: [\"\\uD83D\\uDCCD \", emergency.location]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1295,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-time\",\n                  children: [\"\\u23F1\\uFE0F \", Math.floor((currentTime - emergency.reportedAt) / 60000), \" min ago\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ai-classification\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"ai-confidence\",\n                    children: [\"\\uD83E\\uDD16 AI: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: '#3b82f6',\n                        fontWeight: '600'\n                      },\n                      children: [(emergency.aiClassification.confidence * 100).toFixed(0), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1303,\n                      columnNumber: 32\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1302,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"risk-score\",\n                    children: [\"\\u26A0\\uFE0F Risk: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        color: emergency.aiClassification.riskScore > 0.8 ? '#dc2626' : '#f59e0b',\n                        fontWeight: '600'\n                      },\n                      children: [(emergency.aiClassification.riskScore * 100).toFixed(0), \"%\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1308,\n                      columnNumber: 34\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1307,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1301,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-status\",\n                  children: [\"Status: \", /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      color: emergency.status === 'dispatched' ? '#f59e0b' : emergency.status === 'en-route' ? '#ef4444' : '#64748b',\n                      fontWeight: '600'\n                    },\n                    children: emergency.status.charAt(0).toUpperCase() + emergency.status.slice(1)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1318,\n                    columnNumber: 31\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1317,\n                  columnNumber: 21\n                }, this), emergency.assignedAmbulance && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"assigned-ambulance\",\n                  children: [\"\\uD83D\\uDE91 \", emergency.assignedAmbulance, emergency.assignedHospital && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '11px',\n                      marginTop: '2px'\n                    },\n                    children: [\"\\uD83C\\uDFE5 \\u2192 \", emergency.assignedHospital]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1331,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1328,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1293,\n                columnNumber: 19\n              }, this)]\n            }, emergency.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1286,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1284,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel fleet-panel\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"panel-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              children: \"\\uD83D\\uDE91 Fleet Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1346,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1345,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ambulance-grid\",\n            children: ambulances.map(ambulance => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `ambulance-card ${ambulance.status} ${(selectedAmbulance === null || selectedAmbulance === void 0 ? void 0 : selectedAmbulance.id) === ambulance.id ? 'selected' : ''}`,\n              onClick: () => setSelectedAmbulance(ambulance),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ambulance-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ambulance-id\",\n                  children: ambulance.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1356,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-dot\",\n                  style: {\n                    backgroundColor: getStatusColor(ambulance.status)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1357,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1355,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ambulance-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"crew-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83D\\uDC68\\u200D\\u2695\\uFE0F \", ambulance.driver]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1364,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83E\\uDE7A \", ambulance.medic]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1365,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1363,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"vehicle-stats\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fuel-level\",\n                    children: [\"\\u26FD \", ambulance.fuel, \"%\", /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"fuel-bar\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"fuel-fill\",\n                        style: {\n                          width: `${ambulance.fuel}%`,\n                          backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1371,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1370,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1368,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1367,\n                  columnNumber: 21\n                }, this), ambulance.emergency && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-assignment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"assignment-type\",\n                    children: ambulance.emergency.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1383,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"assignment-eta\",\n                    children: [\"ETA: \", ambulance.emergency.eta]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1384,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1382,\n                  columnNumber: 23\n                }, this), ambulance.status === 'available' && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    const pendingEmergency = activeEmergencies.find(e => !e.assignedAmbulance);\n                    if (pendingEmergency) {\n                      dispatchAmbulance(pendingEmergency.id, ambulance.id);\n                    }\n                  },\n                  style: {\n                    background: '#059669',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '4px',\n                    padding: '6px 12px',\n                    fontSize: '11px',\n                    cursor: 'pointer',\n                    fontWeight: '600',\n                    marginTop: '8px',\n                    width: '100%'\n                  },\n                  disabled: !activeEmergencies.some(e => !e.assignedAmbulance),\n                  children: activeEmergencies.some(e => !e.assignedAmbulance) ? 'Quick Dispatch' : 'Standby'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1388,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1362,\n                columnNumber: 19\n              }, this)]\n            }, ambulance.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1350,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1344,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 790,\n      columnNumber: 9\n    }, this), currentPage === 'transport-optimizer' && /*#__PURE__*/_jsxDEV(PatientTransportOptimizer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1424,\n      columnNumber: 9\n    }, this), currentPage === 'traffic-hijacking' && /*#__PURE__*/_jsxDEV(TrafficSignalHijacking, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1429,\n      columnNumber: 9\n    }, this), currentPage === 'mobile-app' && /*#__PURE__*/_jsxDEV(MobileApp, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1434,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 733,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"d6nt77KiQkL2/zXsRj+xZGenGuY=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "PatientTransportOptimizer", "TrafficSignalHijacking", "MobileApp", "jsxDEV", "_jsxDEV", "emergencyLocations", "id", "name", "coords", "lat", "lng", "type", "hospitals", "capacity", "emergency", "initialAmbulances", "status", "location", "driver", "medic", "fuel", "lastMaintenance", "equipment", "specialization", "aiScore", "responseTime", "priority", "eta", "aiConfidence", "App", "_s", "currentPage", "setCurrentPage", "darkMode", "setDarkMode", "map", "setMap", "ambulances", "setAmbulances", "selectedAmbulance", "setSelectedAmbulance", "emergencyAlerts", "setEmergencyAlerts", "trafficData", "setTrafficData", "congestion", "incidents", "avgSpeed", "currentTime", "setCurrentTime", "Date", "systemStatus", "setSystemStatus", "mapLayers", "setMapLayers", "traffic", "routes", "mapRef", "mapMarkers", "setMapMarkers", "emergencies", "activeEmergencies", "setActiveEmergencies", "reportedAt", "now", "assignedAmbulance", "aiClassification", "confidence", "symptoms", "riskScore", "recommendedHospital", "estimatedSeverity", "source", "patientInfo", "age", "gender", "vitals", "heartRate", "bp", "aiSystem", "setAiSystem", "modelsLoaded", "lastUpdate", "accuracy", "processedCalls", "falsePositives", "avgResponseTime", "voiceSystem", "setVoiceSystem", "isListening", "supportedLanguages", "currentLanguage", "whisperModel", "processedVoiceCalls", "heatmapData", "setHeatmapData", "emergencyHotspots", "area", "risk", "ambulancesDeployed", "lastUpdated", "predictionAccuracy", "systemArchitecture", "setSystemArchitecture", "microservices", "uptime", "apiCalls", "regions", "scalingStatus", "timer", "setInterval", "clearInterval", "toggleDarkMode", "document", "documentElement", "setAttribute", "interval", "prev", "Math", "max", "min", "random", "amb", "initMap", "newMap", "window", "google", "maps", "Map", "getElementById", "zoom", "center", "mapTypeControl", "streetViewControl", "fullscreenControl", "styles", "elementType", "stylers", "color", "featureType", "trafficLayer", "TrafficLayer", "for<PERSON>ach", "hospital", "hospital<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "position", "title", "icon", "url", "encodeURIComponent", "scaledSize", "Size", "infoWindow", "InfoWindow", "content", "addListener", "open", "ambulanceMarkers", "ambulance", "ambulance<PERSON><PERSON><PERSON>", "getStatusColor", "ambulanceInfo", "toUpperCase", "push", "marker", "<PERSON><PERSON><PERSON><PERSON>", "getPriorityColor", "animation", "Animation", "BOUNCE", "emergencyInfo", "floor", "assignedAmb", "find", "routePath", "Polyline", "path", "geodesic", "strokeColor", "strokeOpacity", "strokeWeight", "icons", "SymbolPath", "FORWARD_CLOSED_ARROW", "scale", "fillColor", "fillOpacity", "offset", "currentPos", "getPosition", "e", "targetLat", "targetLng", "currentLat", "currentLng", "newLat", "newLng", "setPosition", "trafficAreas", "radius", "level", "trafficCircle", "Circle", "current", "loadGoogleMapsScript", "script", "createElement", "src", "async", "defer", "body", "append<PERSON><PERSON><PERSON>", "onload", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "calculateDistance", "lat1", "lng1", "lat2", "lng2", "R", "dLat", "PI", "dLng", "a", "sin", "cos", "c", "atan2", "sqrt", "classifyEmergency", "context", "emergencyTypes", "keywords", "severity", "bestMatch", "data", "Object", "entries", "matches", "filter", "keyword", "some", "symptom", "toLowerCase", "includes", "length", "findOptimalAmbulance", "availableAmbulances", "_emergency$aiClassifi", "distance", "distanceScore", "specializationScore", "split", "equipmentScore", "responseScore", "fuelScore", "totalScore", "score", "sort", "b", "selectOptimalHospital", "hospitalScores", "capacityScore", "trafficFactor", "timeOfDay", "getHours", "historicalScore", "dispatchAmbulance", "emergencyId", "ambulanceId", "optimal", "optimalHospital", "assignedHospital", "round", "estimatedArrival", "to<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "layerName", "simulateEmergencyCall", "emergencyScenarios", "locations", "sources", "scenario", "newEmergency", "String", "slice", "setTimeout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "char<PERSON>t", "onClick", "toLocaleDateString", "style", "marginLeft", "backgroundColor", "border", "height", "fontWeight", "toFixed", "join", "lang", "hotspot", "index", "toLocaleString", "service", "display", "gap", "alignItems", "background", "borderRadius", "padding", "fontSize", "cursor", "marginTop", "width", "pendingEmergency", "disabled", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/App.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\nimport \"./App.css\";\nimport PatientTransportOptimizer from \"./components/PatientTransportOptimizer\";\nimport TrafficSignalHijacking from \"./components/TrafficSignalHijacking\";\nimport MobileApp from \"./components/MobileApp\";\n\n// Emergency locations and hospitals in Chennai\nconst emergencyLocations = [\n  { id: 1, name: \"Chennai Central Railway Station\", coords: { lat: 13.0827, lng: 80.2707 }, type: \"transport\" },\n  { id: 2, name: \"Marina Beach\", coords: { lat: 13.0500, lng: 80.2824 }, type: \"public\" },\n  { id: 3, name: \"T Nagar Commercial Complex\", coords: { lat: 13.0418, lng: 80.2341 }, type: \"commercial\" },\n  { id: 4, name: \"Velachery IT Corridor\", coords: { lat: 12.9756, lng: 80.2207 }, type: \"business\" },\n  { id: 5, name: \"Adyar Residential Area\", coords: { lat: 13.0067, lng: 80.2206 }, type: \"residential\" },\n  { id: 6, name: \"Anna Nagar Metro Station\", coords: { lat: 13.0850, lng: 80.2101 }, type: \"transport\" },\n  { id: 7, name: \"Guindy Industrial Estate\", coords: { lat: 13.0067, lng: 80.2206 }, type: \"industrial\" },\n];\n\nconst hospitals = [\n  { id: 1, name: \"Apollo Hospital\", coords: { lat: 13.0358, lng: 80.2297 }, capacity: 85, emergency: true },\n  { id: 2, name: \"Fortis Malar Hospital\", coords: { lat: 13.0067, lng: 80.2206 }, capacity: 92, emergency: true },\n  { id: 3, name: \"MIOT International\", coords: { lat: 12.9756, lng: 80.2207 }, capacity: 78, emergency: true },\n  { id: 4, name: \"Stanley Medical College\", coords: { lat: 13.0827, lng: 80.2707 }, capacity: 65, emergency: true },\n  { id: 5, name: \"Rajiv Gandhi Govt Hospital\", coords: { lat: 13.1358, lng: 80.2297 }, capacity: 45, emergency: true },\n];\n\n// Enhanced ambulance data with AI capabilities\nconst initialAmbulances = [\n  {\n    id: \"AMB001\",\n    status: \"available\",\n    location: { lat: 13.0827, lng: 80.2707 },\n    driver: \"Raj Kumar\",\n    medic: \"Dr. Priya\",\n    fuel: 85,\n    lastMaintenance: \"2024-01-10\",\n    equipment: [\"AED\", \"Ventilator\", \"Cardiac Monitor\"],\n    specialization: \"cardiac\",\n    aiScore: 0.92,\n    responseTime: 3.2\n  },\n  {\n    id: \"AMB002\",\n    status: \"dispatched\",\n    location: { lat: 13.0500, lng: 80.2824 },\n    driver: \"Suresh M\",\n    medic: \"Dr. Arun\",\n    fuel: 92,\n    lastMaintenance: \"2024-01-08\",\n    equipment: [\"Trauma Kit\", \"Spinal Board\", \"IV Fluids\"],\n    specialization: \"trauma\",\n    aiScore: 0.88,\n    responseTime: 2.8,\n    emergency: { type: \"cardiac\", priority: \"high\", eta: \"4 min\", aiConfidence: 0.94 }\n  },\n  {\n    id: \"AMB003\",\n    status: \"en-route\",\n    location: { lat: 13.0418, lng: 80.2341 },\n    driver: \"Karthik S\",\n    medic: \"Dr. Meera\",\n    fuel: 67,\n    lastMaintenance: \"2024-01-12\",\n    equipment: [\"Pediatric Kit\", \"Oxygen\", \"Defibrillator\"],\n    specialization: \"pediatric\",\n    aiScore: 0.91,\n    responseTime: 3.5,\n    emergency: { type: \"accident\", priority: \"critical\", eta: \"7 min\", aiConfidence: 0.97 }\n  },\n  {\n    id: \"AMB004\",\n    status: \"maintenance\",\n    location: { lat: 12.9756, lng: 80.2207 },\n    driver: \"Venkat R\",\n    medic: \"Dr. Lakshmi\",\n    fuel: 23,\n    lastMaintenance: \"2024-01-15\",\n    equipment: [\"Basic Life Support\"],\n    specialization: \"general\",\n    aiScore: 0.85,\n    responseTime: 4.1\n  },\n];\n\nconst App = () => {\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [darkMode, setDarkMode] = useState(false);\n  const [map, setMap] = useState(null);\n  const [ambulances, setAmbulances] = useState(initialAmbulances);\n  const [selectedAmbulance, setSelectedAmbulance] = useState(null);\n  const [emergencyAlerts, setEmergencyAlerts] = useState([]);\n  const [trafficData, setTrafficData] = useState({ congestion: 65, incidents: 3, avgSpeed: 28 });\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [systemStatus, setSystemStatus] = useState(\"operational\");\n  const [mapLayers, setMapLayers] = useState({\n    traffic: true,\n    hospitals: true,\n    routes: true\n  });\n  const mapRef = useRef(null);\n  const [mapMarkers, setMapMarkers] = useState({\n    hospitals: [],\n    ambulances: [],\n    emergencies: [],\n    routes: []\n  });\n  const [activeEmergencies, setActiveEmergencies] = useState([\n    {\n      id: \"EMG001\",\n      type: \"cardiac arrest\",\n      priority: \"critical\",\n      location: \"T Nagar Commercial Complex\",\n      coords: { lat: 13.0418, lng: 80.2341 },\n      reportedAt: new Date(Date.now() - 300000), // 5 minutes ago\n      assignedAmbulance: \"AMB002\",\n      status: \"dispatched\",\n      aiClassification: {\n        confidence: 0.94,\n        symptoms: [\"chest pain\", \"shortness of breath\", \"unconscious\"],\n        riskScore: 0.89,\n        recommendedHospital: \"Apollo Hospital\",\n        estimatedSeverity: \"high\"\n      },\n      source: \"mobile_app\",\n      patientInfo: { age: 65, gender: \"male\", vitals: { heartRate: 45, bp: \"80/40\" } }\n    },\n    {\n      id: \"EMG002\",\n      type: \"road accident\",\n      priority: \"high\",\n      location: \"Anna Nagar Metro Station\",\n      coords: { lat: 13.0850, lng: 80.2101 },\n      reportedAt: new Date(Date.now() - 180000), // 3 minutes ago\n      assignedAmbulance: \"AMB003\",\n      status: \"en-route\",\n      aiClassification: {\n        confidence: 0.97,\n        symptoms: [\"multiple injuries\", \"bleeding\", \"trauma\"],\n        riskScore: 0.85,\n        recommendedHospital: \"Stanley Medical College\",\n        estimatedSeverity: \"moderate\"\n      },\n      source: \"iot_sensor\",\n      patientInfo: { age: 28, gender: \"female\", vitals: { heartRate: 110, bp: \"90/60\" } }\n    }\n  ]);\n\n  // AI System State\n  const [aiSystem, setAiSystem] = useState({\n    status: \"active\",\n    modelsLoaded: true,\n    lastUpdate: new Date(),\n    accuracy: 0.94,\n    processedCalls: 1247,\n    falsePositives: 23,\n    avgResponseTime: 2.8\n  });\n\n  // Voice Interface State\n  const [voiceSystem, setVoiceSystem] = useState({\n    isListening: false,\n    supportedLanguages: ['English', 'Tamil', 'Hindi', 'Telugu', 'Malayalam', 'Kannada'],\n    currentLanguage: 'English',\n    whisperModel: 'whisper-large-v3',\n    accuracy: 0.96,\n    processedVoiceCalls: 342\n  });\n\n  // Heatmap Data\n  const [heatmapData, setHeatmapData] = useState({\n    emergencyHotspots: [\n      { area: 'T Nagar', risk: 0.89, incidents: 45, ambulancesDeployed: 3 },\n      { area: 'Anna Nagar', risk: 0.76, incidents: 32, ambulancesDeployed: 2 },\n      { area: 'Velachery', risk: 0.82, incidents: 38, ambulancesDeployed: 2 },\n      { area: 'Adyar', risk: 0.65, incidents: 28, ambulancesDeployed: 2 },\n      { area: 'Guindy', risk: 0.71, incidents: 31, ambulancesDeployed: 1 }\n    ],\n    lastUpdated: new Date(),\n    predictionAccuracy: 0.87\n  });\n\n  // System Architecture Status\n  const [systemArchitecture, setSystemArchitecture] = useState({\n    microservices: [\n      { name: 'Emergency Classification Service', status: 'active', uptime: '99.9%' },\n      { name: 'Dispatch Optimization Service', status: 'active', uptime: '99.8%' },\n      { name: 'Voice Processing Service', status: 'active', uptime: '99.7%' },\n      { name: 'Heatmap Analytics Service', status: 'active', uptime: '99.9%' },\n      { name: 'Hospital Integration API', status: 'active', uptime: '99.6%' },\n      { name: 'Smart City Gateway', status: 'active', uptime: '99.8%' }\n    ],\n    apiCalls: 15420,\n    regions: ['Chennai', 'Bangalore', 'Hyderabad'],\n    scalingStatus: 'auto-scaling enabled'\n  });\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Dark mode toggle functionality\n  const toggleDarkMode = () => {\n    setDarkMode(!darkMode);\n  };\n\n  // Apply theme to document\n  useEffect(() => {\n    document.documentElement.setAttribute('data-theme', darkMode ? 'dark' : 'light');\n  }, [darkMode]);\n\n  // Simulate real-time data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Update traffic data\n      setTrafficData(prev => ({\n        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),\n        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),\n        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))\n      }));\n\n      // Simulate ambulance position updates\n      setAmbulances(prev => prev.map(amb => ({\n        ...amb,\n        location: {\n          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,\n          lng: amb.location.lng + (Math.random() - 0.5) * 0.001\n        }\n      })));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    const initMap = () => {\n      const newMap = new window.google.maps.Map(document.getElementById(\"emergency-map\"), {\n        zoom: 12,\n        center: { lat: 13.0827, lng: 80.2707 },\n        mapTypeControl: false,\n        streetViewControl: false,\n        fullscreenControl: false,\n        styles: [\n          { elementType: \"geometry\", stylers: [{ color: \"#f5f5f5\" }] },\n          { elementType: \"labels.text.fill\", stylers: [{ color: \"#616161\" }] },\n          { elementType: \"labels.text.stroke\", stylers: [{ color: \"#f5f5f5\" }] },\n          { featureType: \"road\", elementType: \"geometry\", stylers: [{ color: \"#ffffff\" }] },\n          { featureType: \"road\", elementType: \"geometry.stroke\", stylers: [{ color: \"#e0e0e0\" }] },\n          { featureType: \"road.highway\", elementType: \"geometry\", stylers: [{ color: \"#ffd54f\" }] },\n          { featureType: \"water\", elementType: \"geometry\", stylers: [{ color: \"#c9c9c9\" }] },\n          { featureType: \"poi\", elementType: \"geometry\", stylers: [{ color: \"#eeeeee\" }] }\n        ]\n      });\n\n      const trafficLayer = new window.google.maps.TrafficLayer();\n      trafficLayer.setMap(newMap);\n\n      // Add hospital markers\n      hospitals.forEach(hospital => {\n        const hospitalMarker = new window.google.maps.Marker({\n          position: hospital.coords,\n          map: newMap,\n          title: hospital.name,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <circle cx=\"12\" cy=\"12\" r=\"10\" fill=\"#dc2626\"/>\n                <path d=\"M12 6v12M6 12h12\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(24, 24)\n          }\n        });\n\n        const infoWindow = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${hospital.name}</h4>\n              <p style=\"margin: 0; color: #64748b; font-size: 13px;\">Capacity: ${hospital.capacity}%</p>\n              <p style=\"margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;\">✓ Emergency Services Available</p>\n            </div>\n          `\n        });\n\n        hospitalMarker.addListener('click', () => {\n          infoWindow.open(newMap, hospitalMarker);\n        });\n      });\n\n      // Add ambulance markers with animation\n      const ambulanceMarkers = [];\n      ambulances.forEach(ambulance => {\n        const ambulanceMarker = new window.google.maps.Marker({\n          position: ambulance.location,\n          map: newMap,\n          title: `${ambulance.id} - ${ambulance.status}`,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"32\" height=\"32\" viewBox=\"0 0 32 32\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <rect x=\"2\" y=\"12\" width=\"28\" height=\"12\" rx=\"2\" fill=\"${getStatusColor(ambulance.status)}\"/>\n                <rect x=\"6\" y=\"8\" width=\"12\" height=\"8\" rx=\"1\" fill=\"${getStatusColor(ambulance.status)}\"/>\n                <circle cx=\"8\" cy=\"26\" r=\"3\" fill=\"#374151\"/>\n                <circle cx=\"24\" cy=\"26\" r=\"3\" fill=\"#374151\"/>\n                <path d=\"M14 16h4M16 14v4\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\"/>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(32, 32)\n          }\n        });\n\n        const ambulanceInfo = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${ambulance.id}</h4>\n              <p style=\"margin: 0; color: #64748b; font-size: 13px;\">Status: <span style=\"color: ${getStatusColor(ambulance.status)}; font-weight: 600;\">${ambulance.status.toUpperCase()}</span></p>\n              <p style=\"margin: 4px 0 0 0; color: #64748b; font-size: 13px;\">Driver: ${ambulance.driver}</p>\n              <p style=\"margin: 2px 0 0 0; color: #64748b; font-size: 13px;\">Medic: ${ambulance.medic}</p>\n              <p style=\"margin: 4px 0 0 0; color: #64748b; font-size: 13px;\">Fuel: ${ambulance.fuel}%</p>\n              ${ambulance.emergency ? `<p style=\"margin: 4px 0 0 0; color: #dc2626; font-size: 12px; font-weight: 600;\">🚨 ${ambulance.emergency.type} - ETA: ${ambulance.emergency.eta}</p>` : ''}\n            </div>\n          `\n        });\n\n        ambulanceMarker.addListener('click', () => {\n          ambulanceInfo.open(newMap, ambulanceMarker);\n        });\n\n        ambulanceMarkers.push({ marker: ambulanceMarker, ambulance: ambulance });\n      });\n\n      // Add emergency incident markers\n      activeEmergencies.forEach(emergency => {\n        const emergencyMarker = new window.google.maps.Marker({\n          position: emergency.coords,\n          map: newMap,\n          title: `Emergency: ${emergency.type}`,\n          icon: {\n            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`\n              <svg width=\"28\" height=\"28\" viewBox=\"0 0 28 28\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                <circle cx=\"14\" cy=\"14\" r=\"12\" fill=\"${getPriorityColor(emergency.priority)}\" opacity=\"0.8\"/>\n                <circle cx=\"14\" cy=\"14\" r=\"8\" fill=\"${getPriorityColor(emergency.priority)}\"/>\n                <text x=\"14\" y=\"18\" text-anchor=\"middle\" fill=\"white\" font-size=\"12\" font-weight=\"bold\">!</text>\n              </svg>\n            `),\n            scaledSize: new window.google.maps.Size(28, 28)\n          },\n          animation: window.google.maps.Animation.BOUNCE\n        });\n\n        const emergencyInfo = new window.google.maps.InfoWindow({\n          content: `\n            <div style=\"padding: 8px; font-family: Inter, sans-serif;\">\n              <h4 style=\"margin: 0 0 8px 0; color: #1e293b;\">${emergency.id}</h4>\n              <p style=\"margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;\">${emergency.type.toUpperCase()}</p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Priority: <span style=\"color: ${getPriorityColor(emergency.priority)}; font-weight: 600;\">${emergency.priority.toUpperCase()}</span></p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Location: ${emergency.location}</p>\n              <p style=\"margin: 4px 0; color: #64748b; font-size: 13px;\">Reported: ${Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago</p>\n              ${emergency.assignedAmbulance ? `<p style=\"margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;\">🚑 ${emergency.assignedAmbulance} dispatched</p>` : ''}\n            </div>\n          `\n        });\n\n        emergencyMarker.addListener('click', () => {\n          emergencyInfo.open(newMap, emergencyMarker);\n        });\n\n        // Draw route from assigned ambulance to emergency if available\n        if (emergency.assignedAmbulance) {\n          const assignedAmb = ambulances.find(amb => amb.id === emergency.assignedAmbulance);\n          if (assignedAmb) {\n            const routePath = new window.google.maps.Polyline({\n              path: [assignedAmb.location, emergency.coords],\n              geodesic: true,\n              strokeColor: getPriorityColor(emergency.priority),\n              strokeOpacity: 0.8,\n              strokeWeight: 4,\n              icons: [{\n                icon: {\n                  path: window.google.maps.SymbolPath.FORWARD_CLOSED_ARROW,\n                  scale: 3,\n                  fillColor: getPriorityColor(emergency.priority),\n                  fillOpacity: 1,\n                  strokeWeight: 1,\n                  strokeColor: '#ffffff'\n                },\n                offset: '50%'\n              }]\n            });\n            routePath.setMap(newMap);\n          }\n        }\n      });\n\n      // Animate ambulances with realistic movement\n      setInterval(() => {\n        ambulanceMarkers.forEach(({ marker, ambulance }) => {\n          if (ambulance.status === 'dispatched' || ambulance.status === 'en-route') {\n            const currentPos = marker.getPosition();\n            // Simulate movement towards emergency location\n            const emergency = activeEmergencies.find(e => e.assignedAmbulance === ambulance.id);\n            if (emergency) {\n              const targetLat = emergency.coords.lat;\n              const targetLng = emergency.coords.lng;\n              const currentLat = currentPos.lat();\n              const currentLng = currentPos.lng();\n\n              // Move 10% closer to target each update\n              const newLat = currentLat + (targetLat - currentLat) * 0.1;\n              const newLng = currentLng + (targetLng - currentLng) * 0.1;\n\n              marker.setPosition({ lat: newLat, lng: newLng });\n            } else {\n              // Random patrol movement for available ambulances\n              const newLat = currentPos.lat() + (Math.random() - 0.5) * 0.001;\n              const newLng = currentPos.lng() + (Math.random() - 0.5) * 0.001;\n              marker.setPosition({ lat: newLat, lng: newLng });\n            }\n          }\n        });\n      }, 2000);\n\n      // Add traffic congestion simulation circles\n      const trafficAreas = [\n        { center: { lat: 13.0827, lng: 80.2707 }, radius: 500, level: 'high' },\n        { center: { lat: 13.0500, lng: 80.2824 }, radius: 300, level: 'medium' },\n        { center: { lat: 13.0418, lng: 80.2341 }, radius: 400, level: 'high' },\n        { center: { lat: 12.9756, lng: 80.2207 }, radius: 200, level: 'low' }\n      ];\n\n      trafficAreas.forEach(area => {\n        const trafficCircle = new window.google.maps.Circle({\n          strokeColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',\n          strokeOpacity: 0.6,\n          strokeWeight: 2,\n          fillColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',\n          fillOpacity: 0.15,\n          map: newMap,\n          center: area.center,\n          radius: area.radius\n        });\n      });\n\n      setMap(newMap);\n      mapRef.current = newMap;\n    };\n\n    const loadGoogleMapsScript = () => {\n      if (!window.google || !window.google.maps) {\n        const script = document.createElement(\"script\");\n        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;\n        script.async = true;\n        script.defer = true;\n        document.body.appendChild(script);\n        script.onload = initMap;\n      } else {\n        initMap();\n      }\n    };\n\n    loadGoogleMapsScript();\n  }, []);\n\n  // Utility functions for ambulance management\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'available': return '#00ff88';\n      case 'dispatched': return '#ffaa00';\n      case 'en-route': return '#ff4444';\n      case 'maintenance': return '#666666';\n      default: return '#00e5ff';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'critical': return '#ff0044';\n      case 'high': return '#ff6600';\n      case 'medium': return '#ffaa00';\n      case 'low': return '#00ff88';\n      default: return '#00e5ff';\n    }\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const calculateDistance = (lat1, lng1, lat2, lng2) => {\n    const R = 6371; // Earth's radius in km\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLng = (lng2 - lng1) * Math.PI / 180;\n    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +\n              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n              Math.sin(dLng/2) * Math.sin(dLng/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  };\n\n  // AI-Enhanced Emergency Classification\n  const classifyEmergency = (symptoms, vitals, context) => {\n    // Simulate AI classification algorithm\n    const emergencyTypes = {\n      'cardiac arrest': { keywords: ['chest pain', 'heart', 'cardiac', 'unconscious'], severity: 0.95 },\n      'stroke': { keywords: ['speech', 'paralysis', 'face', 'weakness'], severity: 0.90 },\n      'road accident': { keywords: ['accident', 'collision', 'trauma', 'bleeding'], severity: 0.85 },\n      'respiratory distress': { keywords: ['breathing', 'asthma', 'choking'], severity: 0.80 },\n      'overdose': { keywords: ['drugs', 'poison', 'overdose', 'unconscious'], severity: 0.88 }\n    };\n\n    let bestMatch = { type: 'general emergency', confidence: 0.5, severity: 0.6 };\n\n    for (const [type, data] of Object.entries(emergencyTypes)) {\n      const matches = data.keywords.filter(keyword =>\n        symptoms.some(symptom => symptom.toLowerCase().includes(keyword))\n      ).length;\n\n      const confidence = (matches / data.keywords.length) * 0.7 + Math.random() * 0.3;\n\n      if (confidence > bestMatch.confidence) {\n        bestMatch = { type, confidence, severity: data.severity };\n      }\n    }\n\n    return bestMatch;\n  };\n\n  // AI-Driven Smart Ambulance Selection\n  const findOptimalAmbulance = (emergency) => {\n    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');\n    if (availableAmbulances.length === 0) return null;\n\n    return availableAmbulances.map(ambulance => {\n      const distance = calculateDistance(\n        emergency.coords.lat, emergency.coords.lng,\n        ambulance.location.lat, ambulance.location.lng\n      );\n\n      // AI scoring factors\n      const distanceScore = Math.max(0, 1 - (distance / 10)); // Normalize to 0-1\n      const specializationScore = ambulance.specialization === emergency.type.split(' ')[0] ? 1 : 0.7;\n      const equipmentScore = emergency.aiClassification?.riskScore > 0.8 ?\n        (ambulance.equipment.length / 5) : 0.8;\n      const responseScore = Math.max(0, 1 - (ambulance.responseTime / 5));\n      const fuelScore = ambulance.fuel > 30 ? 1 : 0.5;\n\n      // Weighted AI score\n      const totalScore = (\n        distanceScore * 0.3 +\n        specializationScore * 0.25 +\n        equipmentScore * 0.2 +\n        responseScore * 0.15 +\n        fuelScore * 0.1\n      ) * ambulance.aiScore;\n\n      return { ambulance, score: totalScore, distance, eta: distance * 2 };\n    }).sort((a, b) => b.score - a.score)[0];\n  };\n\n  // Predictive Hospital Assignment using LSTM-like logic\n  const selectOptimalHospital = (emergency, ambulance) => {\n    const hospitalScores = hospitals.map(hospital => {\n      const distance = calculateDistance(\n        emergency.coords.lat, emergency.coords.lng,\n        hospital.coords.lat, hospital.coords.lng\n      );\n\n      // Simulate LSTM prediction factors\n      const capacityScore = hospital.capacity / 100;\n      const distanceScore = Math.max(0, 1 - (distance / 15));\n      const specializationScore = hospital.emergency ? 1 : 0.6;\n      const trafficFactor = trafficData.congestion > 70 ? 0.8 : 1;\n\n      // Historical pattern simulation (LSTM would use real historical data)\n      const timeOfDay = new Date().getHours();\n      const historicalScore = timeOfDay >= 8 && timeOfDay <= 18 ? 0.9 : 1; // Rush hour penalty\n\n      const totalScore = (\n        capacityScore * 0.4 +\n        distanceScore * 0.3 +\n        specializationScore * 0.2 +\n        historicalScore * 0.1\n      ) * trafficFactor;\n\n      return { hospital, score: totalScore, distance, eta: distance * 2.5 };\n    }).sort((a, b) => b.score - a.score)[0];\n\n    return hospitalScores;\n  };\n\n  // Enhanced AI-Driven Dispatch\n  const dispatchAmbulance = (emergencyId, ambulanceId = null) => {\n    const emergency = activeEmergencies.find(e => e.id === emergencyId);\n    if (!emergency) return;\n\n    let selectedAmbulance;\n\n    if (ambulanceId) {\n      // Manual dispatch\n      selectedAmbulance = ambulances.find(a => a.id === ambulanceId);\n    } else {\n      // AI-driven automatic dispatch\n      const optimal = findOptimalAmbulance(emergency);\n      selectedAmbulance = optimal?.ambulance;\n    }\n\n    if (!selectedAmbulance) return;\n\n    // Get optimal hospital using AI\n    const optimalHospital = selectOptimalHospital(emergency, selectedAmbulance);\n\n    setAmbulances(prev => prev.map(amb =>\n      amb.id === selectedAmbulance.id\n        ? {\n            ...amb,\n            status: 'dispatched',\n            emergency: {\n              ...emergency,\n              assignedHospital: optimalHospital.hospital.name,\n              eta: `${Math.round(optimalHospital.eta)} min`,\n              aiConfidence: emergency.aiClassification.confidence\n            }\n          }\n        : amb\n    ));\n\n    setActiveEmergencies(prev => prev.map(e =>\n      e.id === emergencyId\n        ? {\n            ...e,\n            assignedAmbulance: selectedAmbulance.id,\n            status: 'dispatched',\n            assignedHospital: optimalHospital.hospital.name,\n            estimatedArrival: new Date(Date.now() + optimalHospital.eta * 60000)\n          }\n        : e\n    ));\n\n    // Update AI system stats\n    setAiSystem(prev => ({\n      ...prev,\n      processedCalls: prev.processedCalls + 1,\n      lastUpdate: new Date()\n    }));\n  };\n\n  // Map control functions\n  const toggleMapLayer = (layerName) => {\n    setMapLayers(prev => ({\n      ...prev,\n      [layerName]: !prev[layerName]\n    }));\n\n    // Here you would typically show/hide the actual map layers\n    // For demo purposes, we'll just update the button states\n  };\n\n  // AI-Enhanced Emergency Call Simulation\n  const simulateEmergencyCall = () => {\n    const emergencyScenarios = [\n      {\n        symptoms: ['chest pain', 'shortness of breath', 'sweating'],\n        vitals: { heartRate: 45 + Math.random() * 20, bp: '80/40' },\n        context: 'home',\n        age: 55 + Math.random() * 20\n      },\n      {\n        symptoms: ['bleeding', 'trauma', 'unconscious'],\n        vitals: { heartRate: 100 + Math.random() * 30, bp: '90/60' },\n        context: 'road',\n        age: 25 + Math.random() * 30\n      },\n      {\n        symptoms: ['difficulty speaking', 'weakness', 'confusion'],\n        vitals: { heartRate: 80 + Math.random() * 20, bp: '160/90' },\n        context: 'office',\n        age: 60 + Math.random() * 15\n      }\n    ];\n\n    const locations = ['Anna Nagar', 'T Nagar', 'Velachery', 'Adyar', 'Guindy'];\n    const sources = ['mobile_app', 'phone_call', 'iot_sensor', 'smart_watch'];\n\n    const scenario = emergencyScenarios[Math.floor(Math.random() * emergencyScenarios.length)];\n    const aiClassification = classifyEmergency(scenario.symptoms, scenario.vitals, scenario.context);\n\n    const newEmergency = {\n      id: `EMG${String(Date.now()).slice(-3)}`,\n      type: aiClassification.type,\n      priority: aiClassification.severity > 0.9 ? 'critical' :\n                aiClassification.severity > 0.8 ? 'high' : 'medium',\n      location: locations[Math.floor(Math.random() * locations.length)],\n      coords: {\n        lat: 13.0827 + (Math.random() - 0.5) * 0.1,\n        lng: 80.2707 + (Math.random() - 0.5) * 0.1\n      },\n      reportedAt: new Date(),\n      assignedAmbulance: null,\n      status: 'pending',\n      aiClassification: {\n        confidence: aiClassification.confidence,\n        symptoms: scenario.symptoms,\n        riskScore: aiClassification.severity,\n        recommendedHospital: hospitals[Math.floor(Math.random() * hospitals.length)].name,\n        estimatedSeverity: aiClassification.severity > 0.9 ? 'critical' : 'moderate'\n      },\n      source: sources[Math.floor(Math.random() * sources.length)],\n      patientInfo: {\n        age: Math.round(scenario.age),\n        gender: Math.random() > 0.5 ? 'male' : 'female',\n        vitals: scenario.vitals\n      }\n    };\n\n    setActiveEmergencies(prev => [...prev, newEmergency]);\n\n    // Auto-dispatch if AI confidence is high\n    if (aiClassification.confidence > 0.85) {\n      setTimeout(() => {\n        dispatchAmbulance(newEmergency.id);\n      }, 2000);\n    }\n  };\n\n\n  return (\n    <div className=\"dispatch-center\">\n      {/* Header with system status and navigation */}\n      <header className=\"dispatch-header\">\n        <div className=\"header-left\">\n          <h1 className=\"system-title\">\n            <span className=\"pulse-dot\"></span>\n            Emergency Medical Services - Dispatch Center\n          </h1>\n          <div className=\"system-status\">\n            <span className={`status-indicator ${systemStatus}`}></span>\n            System Status: {systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)}\n          </div>\n        </div>\n\n        <div className=\"header-center\">\n          <nav className=\"main-navigation\">\n            <button\n              className={`nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`}\n              onClick={() => setCurrentPage('dashboard')}\n            >\n              🏠 Dashboard\n            </button>\n            <button\n              className={`nav-btn ${currentPage === 'transport-optimizer' ? 'active' : ''}`}\n              onClick={() => setCurrentPage('transport-optimizer')}\n            >\n              🚑 Transport Optimizer\n            </button>\n            <button\n              className={`nav-btn ${currentPage === 'traffic-hijacking' ? 'active' : ''}`}\n              onClick={() => setCurrentPage('traffic-hijacking')}\n            >\n              🚦 Traffic Signal AI\n            </button>\n            <button\n              className={`nav-btn ${currentPage === 'mobile-app' ? 'active' : ''}`}\n              onClick={() => setCurrentPage('mobile-app')}\n            >\n              📱 Mobile App\n            </button>\n          </nav>\n        </div>\n\n        <div className=\"header-right\">\n          <div className=\"current-time\">\n            <div className=\"time-display\">{formatTime(currentTime)}</div>\n            <div className=\"date-display\">{currentTime.toLocaleDateString()}</div>\n          </div>\n          <button className=\"theme-toggle\" onClick={toggleDarkMode}>\n            <span className=\"theme-toggle-icon sun\">☀️</span>\n            <span className=\"theme-toggle-icon moon\">🌙</span>\n          </button>\n        </div>\n      </header>\n\n      {/* Conditional Page Rendering */}\n      {currentPage === 'dashboard' && (\n        <div className=\"dashboard-grid\">\n\n        {/* Main Map Section - 75% width */}\n        <div className=\"main-map-section\">\n          <div className=\"panel main-map-panel\">\n            <div className=\"panel-header\">\n              <h3>🗺️ Live Emergency Dispatch Map</h3>\n              <div className=\"map-controls\">\n                <button\n                  className={`map-btn ${mapLayers.traffic ? 'active' : ''}`}\n                  onClick={() => toggleMapLayer('traffic')}\n                >\n                  Traffic Layer\n                </button>\n                <button\n                  className={`map-btn ${mapLayers.hospitals ? 'active' : ''}`}\n                  onClick={() => toggleMapLayer('hospitals')}\n                >\n                  Hospitals\n                </button>\n                <button\n                  className={`map-btn ${mapLayers.routes ? 'active' : ''}`}\n                  onClick={() => toggleMapLayer('routes')}\n                >\n                  Active Routes\n                </button>\n                <button\n                  className=\"map-btn\"\n                  onClick={simulateEmergencyCall}\n                  style={{ marginLeft: '12px', backgroundColor: '#dc2626', color: 'white', border: '1px solid #dc2626' }}\n                >\n                  + Simulate Emergency\n                </button>\n              </div>\n            </div>\n\n            <div style={{ position: 'relative', height: 'calc(100% - 80px)' }}>\n              <div id=\"emergency-map\" className=\"emergency-map\"></div>\n\n              {/* Fleet Status Overlay on Map */}\n              <div className=\"fleet-status-overlay\">\n                <div className=\"fleet-summary-overlay\">\n                  <span className=\"available-count\">\n                    {ambulances.filter(a => a.status === 'available').length} Available\n                  </span>\n                  <span className=\"active-count\">\n                    {ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length} Deployed\n                  </span>\n                  <span style={{ color: '#dc2626', fontWeight: '600' }}>\n                    {activeEmergencies.length} Active Emergencies\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Map Legend */}\n            <div className=\"map-legend\">\n              <div className=\"legend-item\">\n                <span className=\"legend-icon\" style={{ backgroundColor: '#10b981' }}>🚑</span>\n                <span>Available</span>\n              </div>\n              <div className=\"legend-item\">\n                <span className=\"legend-icon\" style={{ backgroundColor: '#f59e0b' }}>🚑</span>\n                <span>Dispatched</span>\n              </div>\n              <div className=\"legend-item\">\n                <span className=\"legend-icon\" style={{ backgroundColor: '#ef4444' }}>🚑</span>\n                <span>En Route</span>\n              </div>\n              <div className=\"legend-item\">\n                <span className=\"legend-icon\" style={{ backgroundColor: '#dc2626' }}>🏥</span>\n                <span>Hospital</span>\n              </div>\n              <div className=\"legend-item\">\n                <span className=\"legend-icon\" style={{ backgroundColor: '#dc2626' }}>!</span>\n                <span>Emergency</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Advanced Features Section - Below Map */}\n          <div className=\"advanced-features-section\">\n\n            {/* Multilingual Voice Interface */}\n            <div className=\"panel voice-interface-panel\">\n              <div className=\"panel-header\">\n                <h3>🎤 Multilingual Voice Interface</h3>\n                <div className=\"voice-status\">\n                  <span className={`status-indicator ${voiceSystem.isListening ? 'listening' : 'ready'}`}></span>\n                  <span>{voiceSystem.isListening ? 'Listening...' : 'Ready'}</span>\n                </div>\n              </div>\n\n              <div className=\"voice-interface-content\">\n                <div className=\"voice-stats\">\n                  <div className=\"voice-stat\">\n                    <div className=\"stat-value\">{voiceSystem.supportedLanguages.length}</div>\n                    <div className=\"stat-label\">Languages Supported</div>\n                  </div>\n                  <div className=\"voice-stat\">\n                    <div className=\"stat-value\">{(voiceSystem.accuracy * 100).toFixed(1)}%</div>\n                    <div className=\"stat-label\">Voice Recognition Accuracy</div>\n                  </div>\n                  <div className=\"voice-stat\">\n                    <div className=\"stat-value\">{voiceSystem.processedVoiceCalls}</div>\n                    <div className=\"stat-label\">Voice Calls Processed</div>\n                  </div>\n                </div>\n\n                <div className=\"voice-features\">\n                  <div className=\"feature-grid\">\n                    <div className=\"voice-feature\">\n                      <div className=\"feature-icon\">🗣️</div>\n                      <div className=\"feature-content\">\n                        <h4>Whisper ASR Integration</h4>\n                        <p>Advanced speech recognition using OpenAI Whisper model for accurate transcription across multiple languages and accents.</p>\n                        <div className=\"tech-badge\">Model: {voiceSystem.whisperModel}</div>\n                      </div>\n                    </div>\n\n                    <div className=\"voice-feature\">\n                      <div className=\"feature-icon\">🌐</div>\n                      <div className=\"feature-content\">\n                        <h4>Multi-Language Support</h4>\n                        <p>Supports {voiceSystem.supportedLanguages.join(', ')} with real-time language detection and emergency classification.</p>\n                        <div className=\"language-pills\">\n                          {voiceSystem.supportedLanguages.slice(0, 4).map(lang => (\n                            <span key={lang} className=\"language-pill\">{lang}</span>\n                          ))}\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"voice-feature\">\n                      <div className=\"feature-icon\">🧠</div>\n                      <div className=\"feature-content\">\n                        <h4>NLP Emergency Classification</h4>\n                        <p>Advanced natural language processing to understand emergency context, severity, and location from voice descriptions.</p>\n                        <div className=\"tech-badge\">Accuracy: {(voiceSystem.accuracy * 100).toFixed(1)}%</div>\n                      </div>\n                    </div>\n\n                    <div className=\"voice-feature\">\n                      <div className=\"feature-icon\">♿</div>\n                      <div className=\"feature-content\">\n                        <h4>Accessibility & Inclusion</h4>\n                        <p>Removes literacy and language barriers, enabling emergency access for all community members regardless of education level.</p>\n                        <div className=\"accessibility-badge\">Universal Access</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"voice-demo\">\n                  <button\n                    className=\"voice-demo-btn\"\n                    onClick={() => setVoiceSystem(prev => ({ ...prev, isListening: !prev.isListening }))}\n                  >\n                    {voiceSystem.isListening ? '🛑 Stop Listening' : '🎤 Start Voice Demo'}\n                  </button>\n                  <div className=\"voice-demo-text\">\n                    {voiceSystem.isListening ?\n                      'Listening for emergency... Say \"Help, I need an ambulance at Marina Beach\"' :\n                      'Click to test multilingual voice emergency reporting'\n                    }\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Heatmap-Based Resource Placement */}\n            <div className=\"panel heatmap-panel\">\n              <div className=\"panel-header\">\n                <h3>🔥 Predictive Heatmap Analytics</h3>\n                <div className=\"heatmap-accuracy\">\n                  Prediction Accuracy: <span className=\"accuracy-value\">{(heatmapData.predictionAccuracy * 100).toFixed(1)}%</span>\n                </div>\n              </div>\n\n              <div className=\"heatmap-content\">\n                <div className=\"heatmap-overview\">\n                  <div className=\"heatmap-description\">\n                    <h4>🎯 Smart Resource Deployment</h4>\n                    <p>AI-powered predictive analytics analyze historical emergency data, traffic patterns, population density, and temporal factors to optimize ambulance placement across the city.</p>\n                  </div>\n                </div>\n\n                <div className=\"hotspots-grid\">\n                  <h4>📍 Current Emergency Hotspots</h4>\n                  <div className=\"hotspots-list\">\n                    {heatmapData.emergencyHotspots.map((hotspot, index) => (\n                      <div key={index} className=\"hotspot-card\">\n                        <div className=\"hotspot-header\">\n                          <span className=\"hotspot-name\">{hotspot.area}</span>\n                          <span className={`risk-level ${hotspot.risk > 0.8 ? 'high' : hotspot.risk > 0.7 ? 'medium' : 'low'}`}>\n                            {hotspot.risk > 0.8 ? 'HIGH RISK' : hotspot.risk > 0.7 ? 'MEDIUM RISK' : 'LOW RISK'}\n                          </span>\n                        </div>\n                        <div className=\"hotspot-stats\">\n                          <div className=\"hotspot-stat\">\n                            <span className=\"stat-label\">Risk Score:</span>\n                            <span className=\"stat-value\">{(hotspot.risk * 100).toFixed(0)}%</span>\n                          </div>\n                          <div className=\"hotspot-stat\">\n                            <span className=\"stat-label\">Incidents (30d):</span>\n                            <span className=\"stat-value\">{hotspot.incidents}</span>\n                          </div>\n                          <div className=\"hotspot-stat\">\n                            <span className=\"stat-label\">Deployed Units:</span>\n                            <span className=\"stat-value\">{hotspot.ambulancesDeployed} 🚑</span>\n                          </div>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"heatmap-features\">\n                  <div className=\"heatmap-feature-grid\">\n                    <div className=\"heatmap-feature\">\n                      <div className=\"feature-icon\">📊</div>\n                      <div>\n                        <h5>Historical Data Analysis</h5>\n                        <p>Analyzes 5+ years of emergency data to identify patterns and trends</p>\n                      </div>\n                    </div>\n                    <div className=\"heatmap-feature\">\n                      <div className=\"feature-icon\">⏰</div>\n                      <div>\n                        <h5>Temporal Predictions</h5>\n                        <p>Considers time of day, day of week, and seasonal variations</p>\n                      </div>\n                    </div>\n                    <div className=\"heatmap-feature\">\n                      <div className=\"feature-icon\">🏙️</div>\n                      <div>\n                        <h5>Urban Analytics</h5>\n                        <p>Factors in population density, traffic patterns, and event schedules</p>\n                      </div>\n                    </div>\n                    <div className=\"heatmap-feature\">\n                      <div className=\"feature-icon\">🎯</div>\n                      <div>\n                        <h5>Dynamic Redeployment</h5>\n                        <p>Real-time ambulance repositioning based on changing risk patterns</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Modular and Scalable Architecture */}\n            <div className=\"panel architecture-panel\">\n              <div className=\"panel-header\">\n                <h3>🏗️ Modular & Scalable Architecture</h3>\n                <div className=\"architecture-status\">\n                  <span className=\"status-indicator operational\"></span>\n                  <span>{systemArchitecture.scalingStatus}</span>\n                </div>\n              </div>\n\n              <div className=\"architecture-content\">\n                <div className=\"architecture-overview\">\n                  <div className=\"architecture-description\">\n                    <h4>🔧 Microservices Architecture</h4>\n                    <p>Built on a distributed microservices architecture with open APIs, enabling seamless integration into smart city ecosystems and horizontal scaling across multiple regions.</p>\n                  </div>\n\n                  <div className=\"architecture-stats\">\n                    <div className=\"arch-stat\">\n                      <div className=\"stat-value\">{systemArchitecture.apiCalls.toLocaleString()}</div>\n                      <div className=\"stat-label\">API Calls Today</div>\n                    </div>\n                    <div className=\"arch-stat\">\n                      <div className=\"stat-value\">{systemArchitecture.regions.length}</div>\n                      <div className=\"stat-label\">Active Regions</div>\n                    </div>\n                    <div className=\"arch-stat\">\n                      <div className=\"stat-value\">{systemArchitecture.microservices.length}</div>\n                      <div className=\"stat-label\">Microservices</div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"microservices-grid\">\n                  <h4>⚙️ System Components</h4>\n                  <div className=\"microservices-list\">\n                    {systemArchitecture.microservices.map((service, index) => (\n                      <div key={index} className=\"microservice-card\">\n                        <div className=\"service-header\">\n                          <span className=\"service-name\">{service.name}</span>\n                          <span className={`service-status ${service.status}`}>\n                            <span className=\"status-dot\"></span>\n                            {service.status.toUpperCase()}\n                          </span>\n                        </div>\n                        <div className=\"service-uptime\">\n                          Uptime: <span className=\"uptime-value\">{service.uptime}</span>\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"architecture-features\">\n                  <div className=\"arch-feature-grid\">\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">🔗</div>\n                      <div>\n                        <h5>Open API Integration</h5>\n                        <p>RESTful APIs and GraphQL endpoints for seamless third-party integration</p>\n                      </div>\n                    </div>\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">🌐</div>\n                      <div>\n                        <h5>Smart City Ready</h5>\n                        <p>Compatible with smart city platforms and IoT infrastructure</p>\n                      </div>\n                    </div>\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">📈</div>\n                      <div>\n                        <h5>Auto-Scaling</h5>\n                        <p>Kubernetes-based auto-scaling for handling varying emergency loads</p>\n                      </div>\n                    </div>\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">🔒</div>\n                      <div>\n                        <h5>Enterprise Security</h5>\n                        <p>End-to-end encryption, HIPAA compliance, and audit trails</p>\n                      </div>\n                    </div>\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">🌍</div>\n                      <div>\n                        <h5>Multi-Region Support</h5>\n                        <p>Currently deployed in {systemArchitecture.regions.join(', ')} with expansion capabilities</p>\n                      </div>\n                    </div>\n                    <div className=\"arch-feature\">\n                      <div className=\"feature-icon\">⚡</div>\n                      <div>\n                        <h5>Real-Time Processing</h5>\n                        <p>Event-driven architecture with sub-second response times</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"integration-showcase\">\n                  <h4>🔌 Integration Capabilities</h4>\n                  <div className=\"integration-grid\">\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">🏥</span>\n                      <span>Hospital Management Systems</span>\n                    </div>\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">🚦</span>\n                      <span>Traffic Management Systems</span>\n                    </div>\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">📱</span>\n                      <span>Mobile Emergency Apps</span>\n                    </div>\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">🏢</span>\n                      <span>Government Emergency Services</span>\n                    </div>\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">🌐</span>\n                      <span>IoT Sensor Networks</span>\n                    </div>\n                    <div className=\"integration-item\">\n                      <span className=\"integration-icon\">📊</span>\n                      <span>Analytics & BI Platforms</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n          </div>\n        </div>\n\n        {/* Analytics Sidebar - 25% width */}\n        <div className=\"analytics-sidebar\">\n\n          {/* AI System Dashboard */}\n          <div className=\"panel ai-panel\">\n            <div className=\"panel-header\">\n              <h3>🤖 AI Dispatch System</h3>\n              <span className={`ai-status ${aiSystem.status}`}>\n                {aiSystem.status.toUpperCase()}\n              </span>\n            </div>\n            <div className=\"ai-metrics\">\n              <div className=\"ai-metric\">\n                <div className=\"metric-value\">{(aiSystem.accuracy * 100).toFixed(1)}%</div>\n                <div className=\"metric-label\">AI Accuracy</div>\n              </div>\n              <div className=\"ai-metric\">\n                <div className=\"metric-value\">{aiSystem.processedCalls}</div>\n                <div className=\"metric-label\">Calls Processed</div>\n              </div>\n              <div className=\"ai-metric\">\n                <div className=\"metric-value\">{aiSystem.avgResponseTime}s</div>\n                <div className=\"metric-label\">Avg Response</div>\n              </div>\n              <div className=\"ai-metric\">\n                <div className=\"metric-value\">{aiSystem.falsePositives}</div>\n                <div className=\"metric-label\">False Positives</div>\n              </div>\n            </div>\n            <div className=\"ai-features\">\n              <div className=\"feature-item\">\n                <span className=\"feature-icon\">🧠</span>\n                <div>\n                  <div className=\"feature-title\">Emergency Classification</div>\n                  <div className=\"feature-desc\">Real-time symptom analysis</div>\n                </div>\n              </div>\n              <div className=\"feature-item\">\n                <span className=\"feature-icon\">🎯</span>\n                <div>\n                  <div className=\"feature-title\">Smart Dispatch</div>\n                  <div className=\"feature-desc\">Optimal ambulance selection</div>\n                </div>\n              </div>\n              <div className=\"feature-item\">\n                <span className=\"feature-icon\">🏥</span>\n                <div>\n                  <div className=\"feature-title\">Hospital Prediction</div>\n                  <div className=\"feature-desc\">LSTM-based routing</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Traffic Analytics Panel */}\n          <div className=\"panel analytics-panel\">\n            <div className=\"panel-header\">\n              <h3>📊 Traffic Conditions</h3>\n            </div>\n            <div className=\"analytics-grid\">\n              <div className=\"metric-card\">\n                <div className=\"metric-value\">{trafficData.congestion}%</div>\n                <div className=\"metric-label\">Traffic Congestion</div>\n                <div className={`metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`}>\n                  {trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'}\n                </div>\n              </div>\n              <div className=\"metric-card\">\n                <div className=\"metric-value\">{trafficData.incidents}</div>\n                <div className=\"metric-label\">Active Incidents</div>\n                <div className={`metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`}>\n                  {trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'}\n                </div>\n              </div>\n              <div className=\"metric-card\">\n                <div className=\"metric-value\">{trafficData.avgSpeed} km/h</div>\n                <div className=\"metric-label\">Avg Speed</div>\n                <div className={`metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`}>\n                  {trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Emergency Alerts Panel */}\n          <div className=\"panel emergency-panel compact\">\n            <div className=\"panel-header\">\n              <h3>🚨 Active Emergency Calls</h3>\n              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>\n                <span className=\"emergency-count\">{activeEmergencies.length}</span>\n                <button\n                  onClick={simulateEmergencyCall}\n                  style={{\n                    background: '#dc2626',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '4px',\n                    padding: '4px 8px',\n                    fontSize: '11px',\n                    cursor: 'pointer',\n                    fontWeight: '600'\n                  }}\n                >\n                  + Demo Call\n                </button>\n              </div>\n            </div>\n            <div className=\"emergency-list\">\n              {activeEmergencies.map(emergency => (\n                <div key={emergency.id} className={`emergency-card priority-${emergency.priority}`}>\n                  <div className=\"emergency-header\">\n                    <span className=\"emergency-id\">{emergency.id}</span>\n                    <span className={`priority-badge ${emergency.priority}`}>\n                      {emergency.priority.toUpperCase()}\n                    </span>\n                  </div>\n                  <div className=\"emergency-details\">\n                    <div className=\"emergency-type\">{emergency.type}</div>\n                    <div className=\"emergency-location\">📍 {emergency.location}</div>\n                    <div className=\"emergency-time\">\n                      ⏱️ {Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago\n                    </div>\n\n                    {/* AI Classification Info */}\n                    <div className=\"ai-classification\">\n                      <div className=\"ai-confidence\">\n                        🤖 AI: <span style={{ color: '#3b82f6', fontWeight: '600' }}>\n                          {(emergency.aiClassification.confidence * 100).toFixed(0)}%\n                        </span>\n                      </div>\n                      <div className=\"risk-score\">\n                        ⚠️ Risk: <span style={{\n                          color: emergency.aiClassification.riskScore > 0.8 ? '#dc2626' : '#f59e0b',\n                          fontWeight: '600'\n                        }}>\n                          {(emergency.aiClassification.riskScore * 100).toFixed(0)}%\n                        </span>\n                      </div>\n                    </div>\n\n                    <div className=\"emergency-status\">\n                      Status: <span style={{\n                        color: emergency.status === 'dispatched' ? '#f59e0b' :\n                               emergency.status === 'en-route' ? '#ef4444' : '#64748b',\n                        fontWeight: '600'\n                      }}>\n                        {emergency.status.charAt(0).toUpperCase() + emergency.status.slice(1)}\n                      </span>\n                    </div>\n\n                    {emergency.assignedAmbulance && (\n                      <div className=\"assigned-ambulance\">\n                        🚑 {emergency.assignedAmbulance}\n                        {emergency.assignedHospital && (\n                          <div style={{ fontSize: '11px', marginTop: '2px' }}>\n                            🏥 → {emergency.assignedHospital}\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* Ambulance Fleet Panel */}\n          <div className=\"panel fleet-panel\">\n            <div className=\"panel-header\">\n              <h3>🚑 Fleet Status</h3>\n            </div>\n            <div className=\"ambulance-grid\">\n              {ambulances.map(ambulance => (\n                <div\n                  key={ambulance.id}\n                  className={`ambulance-card ${ambulance.status} ${selectedAmbulance?.id === ambulance.id ? 'selected' : ''}`}\n                  onClick={() => setSelectedAmbulance(ambulance)}\n                >\n                  <div className=\"ambulance-header\">\n                    <span className=\"ambulance-id\">{ambulance.id}</span>\n                    <span\n                      className=\"status-dot\"\n                      style={{ backgroundColor: getStatusColor(ambulance.status) }}\n                    ></span>\n                  </div>\n                  <div className=\"ambulance-info\">\n                    <div className=\"crew-info\">\n                      <div>👨‍⚕️ {ambulance.driver}</div>\n                      <div>🩺 {ambulance.medic}</div>\n                    </div>\n                    <div className=\"vehicle-stats\">\n                      <div className=\"fuel-level\">\n                        ⛽ {ambulance.fuel}%\n                        <div className=\"fuel-bar\">\n                          <div\n                            className=\"fuel-fill\"\n                            style={{\n                              width: `${ambulance.fuel}%`,\n                              backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'\n                            }}\n                          ></div>\n                        </div>\n                      </div>\n                    </div>\n                    {ambulance.emergency && (\n                      <div className=\"emergency-assignment\">\n                        <div className=\"assignment-type\">{ambulance.emergency.type}</div>\n                        <div className=\"assignment-eta\">ETA: {ambulance.emergency.eta}</div>\n                      </div>\n                    )}\n                    {ambulance.status === 'available' && (\n                      <button\n                        onClick={() => {\n                          const pendingEmergency = activeEmergencies.find(e => !e.assignedAmbulance);\n                          if (pendingEmergency) {\n                            dispatchAmbulance(pendingEmergency.id, ambulance.id);\n                          }\n                        }}\n                        style={{\n                          background: '#059669',\n                          color: 'white',\n                          border: 'none',\n                          borderRadius: '4px',\n                          padding: '6px 12px',\n                          fontSize: '11px',\n                          cursor: 'pointer',\n                          fontWeight: '600',\n                          marginTop: '8px',\n                          width: '100%'\n                        }}\n                        disabled={!activeEmergencies.some(e => !e.assignedAmbulance)}\n                      >\n                        {activeEmergencies.some(e => !e.assignedAmbulance) ? 'Quick Dispatch' : 'Standby'}\n                      </button>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n        </div>\n        </div>\n      )}\n\n      {/* Patient Transport Optimizer Page */}\n      {currentPage === 'transport-optimizer' && (\n        <PatientTransportOptimizer />\n      )}\n\n      {/* Traffic Signal Hijacking Page */}\n      {currentPage === 'traffic-hijacking' && (\n        <TrafficSignalHijacking />\n      )}\n\n      {/* Mobile App Page */}\n      {currentPage === 'mobile-app' && (\n        <MobileApp />\n      )}\n\n    </div>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,OAAOC,yBAAyB,MAAM,wCAAwC;AAC9E,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,SAAS,MAAM,wBAAwB;;AAE9C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAG,CACzB;EAAEC,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,iCAAiC;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAY,CAAC,EAC7G;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAS,CAAC,EACvF;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,4BAA4B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAa,CAAC,EACzG;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,uBAAuB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAW,CAAC,EAClG;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,wBAAwB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAc,CAAC,EACtG;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,0BAA0B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAY,CAAC,EACtG;EAAEL,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,0BAA0B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE;AAAa,CAAC,CACxG;AAED,MAAMC,SAAS,GAAG,CAChB;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEG,QAAQ,EAAE,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,EACzG;EAAER,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,uBAAuB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEG,QAAQ,EAAE,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,EAC/G;EAAER,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,oBAAoB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEG,QAAQ,EAAE,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,EAC5G;EAAER,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,yBAAyB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEG,QAAQ,EAAE,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,EACjH;EAAER,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,4BAA4B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEG,QAAQ,EAAE,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,CACrH;;AAED;AACA,MAAMC,iBAAiB,GAAG,CACxB;EACET,EAAE,EAAE,QAAQ;EACZU,MAAM,EAAE,WAAW;EACnBC,QAAQ,EAAE;IAAER,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxCQ,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAE,EAAE;EACRC,eAAe,EAAE,YAAY;EAC7BC,SAAS,EAAE,CAAC,KAAK,EAAE,YAAY,EAAE,iBAAiB,CAAC;EACnDC,cAAc,EAAE,SAAS;EACzBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE;AAChB,CAAC,EACD;EACEnB,EAAE,EAAE,QAAQ;EACZU,MAAM,EAAE,YAAY;EACpBC,QAAQ,EAAE;IAAER,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxCQ,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAE,EAAE;EACRC,eAAe,EAAE,YAAY;EAC7BC,SAAS,EAAE,CAAC,YAAY,EAAE,cAAc,EAAE,WAAW,CAAC;EACtDC,cAAc,EAAE,QAAQ;EACxBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,GAAG;EACjBX,SAAS,EAAE;IAAEH,IAAI,EAAE,SAAS;IAAEe,QAAQ,EAAE,MAAM;IAAEC,GAAG,EAAE,OAAO;IAAEC,YAAY,EAAE;EAAK;AACnF,CAAC,EACD;EACEtB,EAAE,EAAE,QAAQ;EACZU,MAAM,EAAE,UAAU;EAClBC,QAAQ,EAAE;IAAER,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxCQ,MAAM,EAAE,WAAW;EACnBC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAE,EAAE;EACRC,eAAe,EAAE,YAAY;EAC7BC,SAAS,EAAE,CAAC,eAAe,EAAE,QAAQ,EAAE,eAAe,CAAC;EACvDC,cAAc,EAAE,WAAW;EAC3BC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE,GAAG;EACjBX,SAAS,EAAE;IAAEH,IAAI,EAAE,UAAU;IAAEe,QAAQ,EAAE,UAAU;IAAEC,GAAG,EAAE,OAAO;IAAEC,YAAY,EAAE;EAAK;AACxF,CAAC,EACD;EACEtB,EAAE,EAAE,QAAQ;EACZU,MAAM,EAAE,aAAa;EACrBC,QAAQ,EAAE;IAAER,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EACxCQ,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE,aAAa;EACpBC,IAAI,EAAE,EAAE;EACRC,eAAe,EAAE,YAAY;EAC7BC,SAAS,EAAE,CAAC,oBAAoB,CAAC;EACjCC,cAAc,EAAE,SAAS;EACzBC,OAAO,EAAE,IAAI;EACbC,YAAY,EAAE;AAChB,CAAC,CACF;AAED,MAAMI,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,WAAW,CAAC;EAC3D,MAAM,CAACmC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqC,GAAG,EAAEC,MAAM,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAM,CAACuC,UAAU,EAAEC,aAAa,CAAC,GAAGxC,QAAQ,CAACiB,iBAAiB,CAAC;EAC/D,MAAM,CAACwB,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAAC2C,eAAe,EAAEC,kBAAkB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAAC6C,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAC;IAAE+C,UAAU,EAAE,EAAE;IAAEC,SAAS,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAG,CAAC,CAAC;EAC9F,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC,IAAIoD,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,aAAa,CAAC;EAC/D,MAAM,CAACuD,SAAS,EAAEC,YAAY,CAAC,GAAGxD,QAAQ,CAAC;IACzCyD,OAAO,EAAE,IAAI;IACb3C,SAAS,EAAE,IAAI;IACf4C,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAMC,MAAM,GAAG1D,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAAC2D,UAAU,EAAEC,aAAa,CAAC,GAAG7D,QAAQ,CAAC;IAC3Cc,SAAS,EAAE,EAAE;IACbyB,UAAU,EAAE,EAAE;IACduB,WAAW,EAAE,EAAE;IACfJ,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhE,QAAQ,CAAC,CACzD;IACEQ,EAAE,EAAE,QAAQ;IACZK,IAAI,EAAE,gBAAgB;IACtBe,QAAQ,EAAE,UAAU;IACpBT,QAAQ,EAAE,4BAA4B;IACtCT,MAAM,EAAE;MAAEC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IACtCqD,UAAU,EAAE,IAAIb,IAAI,CAACA,IAAI,CAACc,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;IAAE;IAC3CC,iBAAiB,EAAE,QAAQ;IAC3BjD,MAAM,EAAE,YAAY;IACpBkD,gBAAgB,EAAE;MAChBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,CAAC,YAAY,EAAE,qBAAqB,EAAE,aAAa,CAAC;MAC9DC,SAAS,EAAE,IAAI;MACfC,mBAAmB,EAAE,iBAAiB;MACtCC,iBAAiB,EAAE;IACrB,CAAC;IACDC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,MAAM,EAAE;QAAEC,SAAS,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAQ;IAAE;EACjF,CAAC,EACD;IACExE,EAAE,EAAE,QAAQ;IACZK,IAAI,EAAE,eAAe;IACrBe,QAAQ,EAAE,MAAM;IAChBT,QAAQ,EAAE,0BAA0B;IACpCT,MAAM,EAAE;MAAEC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IACtCqD,UAAU,EAAE,IAAIb,IAAI,CAACA,IAAI,CAACc,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;IAAE;IAC3CC,iBAAiB,EAAE,QAAQ;IAC3BjD,MAAM,EAAE,UAAU;IAClBkD,gBAAgB,EAAE;MAChBC,UAAU,EAAE,IAAI;MAChBC,QAAQ,EAAE,CAAC,mBAAmB,EAAE,UAAU,EAAE,QAAQ,CAAC;MACrDC,SAAS,EAAE,IAAI;MACfC,mBAAmB,EAAE,yBAAyB;MAC9CC,iBAAiB,EAAE;IACrB,CAAC;IACDC,MAAM,EAAE,YAAY;IACpBC,WAAW,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,QAAQ;MAAEC,MAAM,EAAE;QAAEC,SAAS,EAAE,GAAG;QAAEC,EAAE,EAAE;MAAQ;IAAE;EACpF,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlF,QAAQ,CAAC;IACvCkB,MAAM,EAAE,QAAQ;IAChBiE,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,IAAIhC,IAAI,CAAC,CAAC;IACtBiC,QAAQ,EAAE,IAAI;IACdC,cAAc,EAAE,IAAI;IACpBC,cAAc,EAAE,EAAE;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1F,QAAQ,CAAC;IAC7C2F,WAAW,EAAE,KAAK;IAClBC,kBAAkB,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC;IACnFC,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,kBAAkB;IAChCT,QAAQ,EAAE,IAAI;IACdU,mBAAmB,EAAE;EACvB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjG,QAAQ,CAAC;IAC7CkG,iBAAiB,EAAE,CACjB;MAAEC,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE,IAAI;MAAEpD,SAAS,EAAE,EAAE;MAAEqD,kBAAkB,EAAE;IAAE,CAAC,EACrE;MAAEF,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,IAAI;MAAEpD,SAAS,EAAE,EAAE;MAAEqD,kBAAkB,EAAE;IAAE,CAAC,EACxE;MAAEF,IAAI,EAAE,WAAW;MAAEC,IAAI,EAAE,IAAI;MAAEpD,SAAS,EAAE,EAAE;MAAEqD,kBAAkB,EAAE;IAAE,CAAC,EACvE;MAAEF,IAAI,EAAE,OAAO;MAAEC,IAAI,EAAE,IAAI;MAAEpD,SAAS,EAAE,EAAE;MAAEqD,kBAAkB,EAAE;IAAE,CAAC,EACnE;MAAEF,IAAI,EAAE,QAAQ;MAAEC,IAAI,EAAE,IAAI;MAAEpD,SAAS,EAAE,EAAE;MAAEqD,kBAAkB,EAAE;IAAE,CAAC,CACrE;IACDC,WAAW,EAAE,IAAIlD,IAAI,CAAC,CAAC;IACvBmD,kBAAkB,EAAE;EACtB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzG,QAAQ,CAAC;IAC3D0G,aAAa,EAAE,CACb;MAAEjG,IAAI,EAAE,kCAAkC;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,EAC/E;MAAElG,IAAI,EAAE,+BAA+B;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,EAC5E;MAAElG,IAAI,EAAE,0BAA0B;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,EACvE;MAAElG,IAAI,EAAE,2BAA2B;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,EACxE;MAAElG,IAAI,EAAE,0BAA0B;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,EACvE;MAAElG,IAAI,EAAE,oBAAoB;MAAES,MAAM,EAAE,QAAQ;MAAEyF,MAAM,EAAE;IAAQ,CAAC,CAClE;IACDC,QAAQ,EAAE,KAAK;IACfC,OAAO,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,WAAW,CAAC;IAC9CC,aAAa,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA/G,SAAS,CAAC,MAAM;IACd,MAAMgH,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B7D,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM6D,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,cAAc,GAAGA,CAAA,KAAM;IAC3B9E,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;;EAED;EACApC,SAAS,CAAC,MAAM;IACdoH,QAAQ,CAACC,eAAe,CAACC,YAAY,CAAC,YAAY,EAAElF,QAAQ,GAAG,MAAM,GAAG,OAAO,CAAC;EAClF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACApC,SAAS,CAAC,MAAM;IACd,MAAMuH,QAAQ,GAAGN,WAAW,CAAC,MAAM;MACjC;MACAlE,cAAc,CAACyE,IAAI,KAAK;QACtBxE,UAAU,EAAEyE,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACxE,UAAU,GAAG,CAACyE,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACpF3E,SAAS,EAAEwE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACvE,SAAS,IAAIwE,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGH,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjG1E,QAAQ,EAAEuE,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACtE,QAAQ,GAAG,CAACuE,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;MAChF,CAAC,CAAC,CAAC;;MAEH;MACAnF,aAAa,CAAC+E,IAAI,IAAIA,IAAI,CAAClF,GAAG,CAACuF,GAAG,KAAK;QACrC,GAAGA,GAAG;QACNzG,QAAQ,EAAE;UACRR,GAAG,EAAEiH,GAAG,CAACzG,QAAQ,CAACR,GAAG,GAAG,CAAC6G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;UACrD/G,GAAG,EAAEgH,GAAG,CAACzG,QAAQ,CAACP,GAAG,GAAG,CAAC4G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QAClD;MACF,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMV,aAAa,CAACK,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAENvH,SAAS,CAAC,MAAM;IACd,MAAM8H,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACf,QAAQ,CAACgB,cAAc,CAAC,eAAe,CAAC,EAAE;QAClFC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE;UAAE1H,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QACtC0H,cAAc,EAAE,KAAK;QACrBC,iBAAiB,EAAE,KAAK;QACxBC,iBAAiB,EAAE,KAAK;QACxBC,MAAM,EAAE,CACN;UAAEC,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EAC5D;UAAEF,WAAW,EAAE,kBAAkB;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACpE;UAAEF,WAAW,EAAE,oBAAoB;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACtE;UAAEC,WAAW,EAAE,MAAM;UAAEH,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACjF;UAAEC,WAAW,EAAE,MAAM;UAAEH,WAAW,EAAE,iBAAiB;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACxF;UAAEC,WAAW,EAAE,cAAc;UAAEH,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACzF;UAAEC,WAAW,EAAE,OAAO;UAAEH,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EAClF;UAAEC,WAAW,EAAE,KAAK;UAAEH,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEC,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC;MAEpF,CAAC,CAAC;MAEF,MAAME,YAAY,GAAG,IAAIf,MAAM,CAACC,MAAM,CAACC,IAAI,CAACc,YAAY,CAAC,CAAC;MAC1DD,YAAY,CAACxG,MAAM,CAACwF,MAAM,CAAC;;MAE3B;MACAhH,SAAS,CAACkI,OAAO,CAACC,QAAQ,IAAI;QAC5B,MAAMC,cAAc,GAAG,IAAInB,MAAM,CAACC,MAAM,CAACC,IAAI,CAACkB,MAAM,CAAC;UACnDC,QAAQ,EAAEH,QAAQ,CAACvI,MAAM;UACzB2B,GAAG,EAAEyF,MAAM;UACXuB,KAAK,EAAEJ,QAAQ,CAACxI,IAAI;UACpB6I,IAAI,EAAE;YACJC,GAAG,EAAE,mCAAmC,GAAGC,kBAAkB,CAAC;AAC1E;AACA;AACA;AACA;AACA,aAAa,CAAC;YACFC,UAAU,EAAE,IAAI1B,MAAM,CAACC,MAAM,CAACC,IAAI,CAACyB,IAAI,CAAC,EAAE,EAAE,EAAE;UAChD;QACF,CAAC,CAAC;QAEF,MAAMC,UAAU,GAAG,IAAI5B,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,UAAU,CAAC;UACnDC,OAAO,EAAE;AACnB;AACA,+DAA+DZ,QAAQ,CAACxI,IAAI;AAC5E,iFAAiFwI,QAAQ,CAAClI,QAAQ;AAClG;AACA;AACA;QACQ,CAAC,CAAC;QAEFmI,cAAc,CAACY,WAAW,CAAC,OAAO,EAAE,MAAM;UACxCH,UAAU,CAACI,IAAI,CAACjC,MAAM,EAAEoB,cAAc,CAAC;QACzC,CAAC,CAAC;MACJ,CAAC,CAAC;;MAEF;MACA,MAAMc,gBAAgB,GAAG,EAAE;MAC3BzH,UAAU,CAACyG,OAAO,CAACiB,SAAS,IAAI;QAC9B,MAAMC,eAAe,GAAG,IAAInC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACkB,MAAM,CAAC;UACpDC,QAAQ,EAAEa,SAAS,CAAC9I,QAAQ;UAC5BkB,GAAG,EAAEyF,MAAM;UACXuB,KAAK,EAAE,GAAGY,SAAS,CAACzJ,EAAE,MAAMyJ,SAAS,CAAC/I,MAAM,EAAE;UAC9CoI,IAAI,EAAE;YACJC,GAAG,EAAE,mCAAmC,GAAGC,kBAAkB,CAAC;AAC1E;AACA,yEAAyEW,cAAc,CAACF,SAAS,CAAC/I,MAAM,CAAC;AACzG,uEAAuEiJ,cAAc,CAACF,SAAS,CAAC/I,MAAM,CAAC;AACvG;AACA;AACA;AACA;AACA,aAAa,CAAC;YACFuI,UAAU,EAAE,IAAI1B,MAAM,CAACC,MAAM,CAACC,IAAI,CAACyB,IAAI,CAAC,EAAE,EAAE,EAAE;UAChD;QACF,CAAC,CAAC;QAEF,MAAMU,aAAa,GAAG,IAAIrC,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,UAAU,CAAC;UACtDC,OAAO,EAAE;AACnB;AACA,+DAA+DI,SAAS,CAACzJ,EAAE;AAC3E,mGAAmG2J,cAAc,CAACF,SAAS,CAAC/I,MAAM,CAAC,wBAAwB+I,SAAS,CAAC/I,MAAM,CAACmJ,WAAW,CAAC,CAAC;AACzL,uFAAuFJ,SAAS,CAAC7I,MAAM;AACvG,sFAAsF6I,SAAS,CAAC5I,KAAK;AACrG,qFAAqF4I,SAAS,CAAC3I,IAAI;AACnG,gBAAgB2I,SAAS,CAACjJ,SAAS,GAAG,uFAAuFiJ,SAAS,CAACjJ,SAAS,CAACH,IAAI,WAAWoJ,SAAS,CAACjJ,SAAS,CAACa,GAAG,MAAM,GAAG,EAAE;AAClM;AACA;QACQ,CAAC,CAAC;QAEFqI,eAAe,CAACJ,WAAW,CAAC,OAAO,EAAE,MAAM;UACzCM,aAAa,CAACL,IAAI,CAACjC,MAAM,EAAEoC,eAAe,CAAC;QAC7C,CAAC,CAAC;QAEFF,gBAAgB,CAACM,IAAI,CAAC;UAAEC,MAAM,EAAEL,eAAe;UAAED,SAAS,EAAEA;QAAU,CAAC,CAAC;MAC1E,CAAC,CAAC;;MAEF;MACAlG,iBAAiB,CAACiF,OAAO,CAAChI,SAAS,IAAI;QACrC,MAAMwJ,eAAe,GAAG,IAAIzC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACkB,MAAM,CAAC;UACpDC,QAAQ,EAAEpI,SAAS,CAACN,MAAM;UAC1B2B,GAAG,EAAEyF,MAAM;UACXuB,KAAK,EAAE,cAAcrI,SAAS,CAACH,IAAI,EAAE;UACrCyI,IAAI,EAAE;YACJC,GAAG,EAAE,mCAAmC,GAAGC,kBAAkB,CAAC;AAC1E;AACA,uDAAuDiB,gBAAgB,CAACzJ,SAAS,CAACY,QAAQ,CAAC;AAC3F,sDAAsD6I,gBAAgB,CAACzJ,SAAS,CAACY,QAAQ,CAAC;AAC1F;AACA;AACA,aAAa,CAAC;YACF6H,UAAU,EAAE,IAAI1B,MAAM,CAACC,MAAM,CAACC,IAAI,CAACyB,IAAI,CAAC,EAAE,EAAE,EAAE;UAChD,CAAC;UACDgB,SAAS,EAAE3C,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC0C,SAAS,CAACC;QAC1C,CAAC,CAAC;QAEF,MAAMC,aAAa,GAAG,IAAI9C,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC2B,UAAU,CAAC;UACtDC,OAAO,EAAE;AACnB;AACA,+DAA+D7I,SAAS,CAACR,EAAE;AAC3E,yFAAyFQ,SAAS,CAACH,IAAI,CAACwJ,WAAW,CAAC,CAAC;AACrH,yGAAyGI,gBAAgB,CAACzJ,SAAS,CAACY,QAAQ,CAAC,wBAAwBZ,SAAS,CAACY,QAAQ,CAACyI,WAAW,CAAC,CAAC;AACrM,qFAAqFrJ,SAAS,CAACG,QAAQ;AACvG,qFAAqFqG,IAAI,CAACsD,KAAK,CAAC,CAAC5H,WAAW,GAAGlC,SAAS,CAACiD,UAAU,IAAI,KAAK,CAAC;AAC7I,gBAAgBjD,SAAS,CAACmD,iBAAiB,GAAG,uFAAuFnD,SAAS,CAACmD,iBAAiB,iBAAiB,GAAG,EAAE;AACtL;AACA;QACQ,CAAC,CAAC;QAEFqG,eAAe,CAACV,WAAW,CAAC,OAAO,EAAE,MAAM;UACzCe,aAAa,CAACd,IAAI,CAACjC,MAAM,EAAE0C,eAAe,CAAC;QAC7C,CAAC,CAAC;;QAEF;QACA,IAAIxJ,SAAS,CAACmD,iBAAiB,EAAE;UAC/B,MAAM4G,WAAW,GAAGxI,UAAU,CAACyI,IAAI,CAACpD,GAAG,IAAIA,GAAG,CAACpH,EAAE,KAAKQ,SAAS,CAACmD,iBAAiB,CAAC;UAClF,IAAI4G,WAAW,EAAE;YACf,MAAME,SAAS,GAAG,IAAIlD,MAAM,CAACC,MAAM,CAACC,IAAI,CAACiD,QAAQ,CAAC;cAChDC,IAAI,EAAE,CAACJ,WAAW,CAAC5J,QAAQ,EAAEH,SAAS,CAACN,MAAM,CAAC;cAC9C0K,QAAQ,EAAE,IAAI;cACdC,WAAW,EAAEZ,gBAAgB,CAACzJ,SAAS,CAACY,QAAQ,CAAC;cACjD0J,aAAa,EAAE,GAAG;cAClBC,YAAY,EAAE,CAAC;cACfC,KAAK,EAAE,CAAC;gBACNlC,IAAI,EAAE;kBACJ6B,IAAI,EAAEpD,MAAM,CAACC,MAAM,CAACC,IAAI,CAACwD,UAAU,CAACC,oBAAoB;kBACxDC,KAAK,EAAE,CAAC;kBACRC,SAAS,EAAEnB,gBAAgB,CAACzJ,SAAS,CAACY,QAAQ,CAAC;kBAC/CiK,WAAW,EAAE,CAAC;kBACdN,YAAY,EAAE,CAAC;kBACfF,WAAW,EAAE;gBACf,CAAC;gBACDS,MAAM,EAAE;cACV,CAAC;YACH,CAAC,CAAC;YACFb,SAAS,CAAC3I,MAAM,CAACwF,MAAM,CAAC;UAC1B;QACF;MACF,CAAC,CAAC;;MAEF;MACAd,WAAW,CAAC,MAAM;QAChBgD,gBAAgB,CAAChB,OAAO,CAAC,CAAC;UAAEuB,MAAM;UAAEN;QAAU,CAAC,KAAK;UAClD,IAAIA,SAAS,CAAC/I,MAAM,KAAK,YAAY,IAAI+I,SAAS,CAAC/I,MAAM,KAAK,UAAU,EAAE;YACxE,MAAM6K,UAAU,GAAGxB,MAAM,CAACyB,WAAW,CAAC,CAAC;YACvC;YACA,MAAMhL,SAAS,GAAG+C,iBAAiB,CAACiH,IAAI,CAACiB,CAAC,IAAIA,CAAC,CAAC9H,iBAAiB,KAAK8F,SAAS,CAACzJ,EAAE,CAAC;YACnF,IAAIQ,SAAS,EAAE;cACb,MAAMkL,SAAS,GAAGlL,SAAS,CAACN,MAAM,CAACC,GAAG;cACtC,MAAMwL,SAAS,GAAGnL,SAAS,CAACN,MAAM,CAACE,GAAG;cACtC,MAAMwL,UAAU,GAAGL,UAAU,CAACpL,GAAG,CAAC,CAAC;cACnC,MAAM0L,UAAU,GAAGN,UAAU,CAACnL,GAAG,CAAC,CAAC;;cAEnC;cACA,MAAM0L,MAAM,GAAGF,UAAU,GAAG,CAACF,SAAS,GAAGE,UAAU,IAAI,GAAG;cAC1D,MAAMG,MAAM,GAAGF,UAAU,GAAG,CAACF,SAAS,GAAGE,UAAU,IAAI,GAAG;cAE1D9B,MAAM,CAACiC,WAAW,CAAC;gBAAE7L,GAAG,EAAE2L,MAAM;gBAAE1L,GAAG,EAAE2L;cAAO,CAAC,CAAC;YAClD,CAAC,MAAM;cACL;cACA,MAAMD,MAAM,GAAGP,UAAU,CAACpL,GAAG,CAAC,CAAC,GAAG,CAAC6G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;cAC/D,MAAM4E,MAAM,GAAGR,UAAU,CAACnL,GAAG,CAAC,CAAC,GAAG,CAAC4G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;cAC/D4C,MAAM,CAACiC,WAAW,CAAC;gBAAE7L,GAAG,EAAE2L,MAAM;gBAAE1L,GAAG,EAAE2L;cAAO,CAAC,CAAC;YAClD;UACF;QACF,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;;MAER;MACA,MAAME,YAAY,GAAG,CACnB;QAAEpE,MAAM,EAAE;UAAE1H,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QAAE8L,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAC,EACtE;QAAEtE,MAAM,EAAE;UAAE1H,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QAAE8L,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAS,CAAC,EACxE;QAAEtE,MAAM,EAAE;UAAE1H,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QAAE8L,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAO,CAAC,EACtE;QAAEtE,MAAM,EAAE;UAAE1H,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QAAE8L,MAAM,EAAE,GAAG;QAAEC,KAAK,EAAE;MAAM,CAAC,CACtE;MAEDF,YAAY,CAACzD,OAAO,CAAC7C,IAAI,IAAI;QAC3B,MAAMyG,aAAa,GAAG,IAAI7E,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC4E,MAAM,CAAC;UAClDxB,WAAW,EAAElF,IAAI,CAACwG,KAAK,KAAK,MAAM,GAAG,SAAS,GAAGxG,IAAI,CAACwG,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;UAChGrB,aAAa,EAAE,GAAG;UAClBC,YAAY,EAAE,CAAC;UACfK,SAAS,EAAEzF,IAAI,CAACwG,KAAK,KAAK,MAAM,GAAG,SAAS,GAAGxG,IAAI,CAACwG,KAAK,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;UAC9Fd,WAAW,EAAE,IAAI;UACjBxJ,GAAG,EAAEyF,MAAM;UACXO,MAAM,EAAElC,IAAI,CAACkC,MAAM;UACnBqE,MAAM,EAAEvG,IAAI,CAACuG;QACf,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFpK,MAAM,CAACwF,MAAM,CAAC;MACdnE,MAAM,CAACmJ,OAAO,GAAGhF,MAAM;IACzB,CAAC;IAED,MAAMiF,oBAAoB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAAChF,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;QACzC,MAAM+E,MAAM,GAAG7F,QAAQ,CAAC8F,aAAa,CAAC,QAAQ,CAAC;QAC/CD,MAAM,CAACE,GAAG,GAAG,sGAAsG;QACnHF,MAAM,CAACG,KAAK,GAAG,IAAI;QACnBH,MAAM,CAACI,KAAK,GAAG,IAAI;QACnBjG,QAAQ,CAACkG,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;QACjCA,MAAM,CAACO,MAAM,GAAG1F,OAAO;MACzB,CAAC,MAAM;QACLA,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDkF,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM5C,cAAc,GAAIjJ,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMuJ,gBAAgB,GAAI7I,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAM4L,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IACpD,MAAMC,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAIxG,IAAI,CAAC8G,EAAE,GAAG,GAAG;IAC1C,MAAMC,IAAI,GAAG,CAACJ,IAAI,GAAGF,IAAI,IAAIzG,IAAI,CAAC8G,EAAE,GAAG,GAAG;IAC1C,MAAME,CAAC,GAAGhH,IAAI,CAACiH,GAAG,CAACJ,IAAI,GAAC,CAAC,CAAC,GAAG7G,IAAI,CAACiH,GAAG,CAACJ,IAAI,GAAC,CAAC,CAAC,GACnC7G,IAAI,CAACkH,GAAG,CAACV,IAAI,GAAGxG,IAAI,CAAC8G,EAAE,GAAG,GAAG,CAAC,GAAG9G,IAAI,CAACkH,GAAG,CAACR,IAAI,GAAG1G,IAAI,CAAC8G,EAAE,GAAG,GAAG,CAAC,GAC/D9G,IAAI,CAACiH,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC,GAAG/G,IAAI,CAACiH,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC;IAC7C,MAAMI,CAAC,GAAG,CAAC,GAAGnH,IAAI,CAACoH,KAAK,CAACpH,IAAI,CAACqH,IAAI,CAACL,CAAC,CAAC,EAAEhH,IAAI,CAACqH,IAAI,CAAC,CAAC,GAACL,CAAC,CAAC,CAAC;IACtD,OAAOJ,CAAC,GAAGO,CAAC;EACd,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAGA,CAACxK,QAAQ,EAAEQ,MAAM,EAAEiK,OAAO,KAAK;IACvD;IACA,MAAMC,cAAc,GAAG;MACrB,gBAAgB,EAAE;QAAEC,QAAQ,EAAE,CAAC,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,aAAa,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC;MACjG,QAAQ,EAAE;QAAED,QAAQ,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC;MACnF,eAAe,EAAE;QAAED,QAAQ,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,UAAU,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC;MAC9F,sBAAsB,EAAE;QAAED,QAAQ,EAAE,CAAC,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC;MACxF,UAAU,EAAE;QAAED,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,aAAa,CAAC;QAAEC,QAAQ,EAAE;MAAK;IACzF,CAAC;IAED,IAAIC,SAAS,GAAG;MAAEtO,IAAI,EAAE,mBAAmB;MAAEwD,UAAU,EAAE,GAAG;MAAE6K,QAAQ,EAAE;IAAI,CAAC;IAE7E,KAAK,MAAM,CAACrO,IAAI,EAAEuO,IAAI,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,cAAc,CAAC,EAAE;MACzD,MAAMO,OAAO,GAAGH,IAAI,CAACH,QAAQ,CAACO,MAAM,CAACC,OAAO,IAC1CnL,QAAQ,CAACoL,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACJ,OAAO,CAAC,CAClE,CAAC,CAACK,MAAM;MAER,MAAMzL,UAAU,GAAIkL,OAAO,GAAGH,IAAI,CAACH,QAAQ,CAACa,MAAM,GAAI,GAAG,GAAGtI,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG;MAE/E,IAAItD,UAAU,GAAG8K,SAAS,CAAC9K,UAAU,EAAE;QACrC8K,SAAS,GAAG;UAAEtO,IAAI;UAAEwD,UAAU;UAAE6K,QAAQ,EAAEE,IAAI,CAACF;QAAS,CAAC;MAC3D;IACF;IAEA,OAAOC,SAAS;EAClB,CAAC;;EAED;EACA,MAAMY,oBAAoB,GAAI/O,SAAS,IAAK;IAC1C,MAAMgP,mBAAmB,GAAGzN,UAAU,CAACiN,MAAM,CAAC5H,GAAG,IAAIA,GAAG,CAAC1G,MAAM,KAAK,WAAW,CAAC;IAChF,IAAI8O,mBAAmB,CAACF,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEjD,OAAOE,mBAAmB,CAAC3N,GAAG,CAAC4H,SAAS,IAAI;MAAA,IAAAgG,qBAAA;MAC1C,MAAMC,QAAQ,GAAGnC,iBAAiB,CAChC/M,SAAS,CAACN,MAAM,CAACC,GAAG,EAAEK,SAAS,CAACN,MAAM,CAACE,GAAG,EAC1CqJ,SAAS,CAAC9I,QAAQ,CAACR,GAAG,EAAEsJ,SAAS,CAAC9I,QAAQ,CAACP,GAC7C,CAAC;;MAED;MACA,MAAMuP,aAAa,GAAG3I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIyI,QAAQ,GAAG,EAAG,CAAC,CAAC,CAAC;MACxD,MAAME,mBAAmB,GAAGnG,SAAS,CAACxI,cAAc,KAAKT,SAAS,CAACH,IAAI,CAACwP,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG;MAC/F,MAAMC,cAAc,GAAG,EAAAL,qBAAA,GAAAjP,SAAS,CAACoD,gBAAgB,cAAA6L,qBAAA,uBAA1BA,qBAAA,CAA4B1L,SAAS,IAAG,GAAG,GAC/D0F,SAAS,CAACzI,SAAS,CAACsO,MAAM,GAAG,CAAC,GAAI,GAAG;MACxC,MAAMS,aAAa,GAAG/I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIwC,SAAS,CAACtI,YAAY,GAAG,CAAE,CAAC;MACnE,MAAM6O,SAAS,GAAGvG,SAAS,CAAC3I,IAAI,GAAG,EAAE,GAAG,CAAC,GAAG,GAAG;;MAE/C;MACA,MAAMmP,UAAU,GAAG,CACjBN,aAAa,GAAG,GAAG,GACnBC,mBAAmB,GAAG,IAAI,GAC1BE,cAAc,GAAG,GAAG,GACpBC,aAAa,GAAG,IAAI,GACpBC,SAAS,GAAG,GAAG,IACbvG,SAAS,CAACvI,OAAO;MAErB,OAAO;QAAEuI,SAAS;QAAEyG,KAAK,EAAED,UAAU;QAAEP,QAAQ;QAAErO,GAAG,EAAEqO,QAAQ,GAAG;MAAE,CAAC;IACtE,CAAC,CAAC,CAACS,IAAI,CAAC,CAACnC,CAAC,EAAEoC,CAAC,KAAKA,CAAC,CAACF,KAAK,GAAGlC,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC;EACzC,CAAC;;EAED;EACA,MAAMG,qBAAqB,GAAGA,CAAC7P,SAAS,EAAEiJ,SAAS,KAAK;IACtD,MAAM6G,cAAc,GAAGhQ,SAAS,CAACuB,GAAG,CAAC4G,QAAQ,IAAI;MAC/C,MAAMiH,QAAQ,GAAGnC,iBAAiB,CAChC/M,SAAS,CAACN,MAAM,CAACC,GAAG,EAAEK,SAAS,CAACN,MAAM,CAACE,GAAG,EAC1CqI,QAAQ,CAACvI,MAAM,CAACC,GAAG,EAAEsI,QAAQ,CAACvI,MAAM,CAACE,GACvC,CAAC;;MAED;MACA,MAAMmQ,aAAa,GAAG9H,QAAQ,CAAClI,QAAQ,GAAG,GAAG;MAC7C,MAAMoP,aAAa,GAAG3I,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAIyI,QAAQ,GAAG,EAAG,CAAC;MACtD,MAAME,mBAAmB,GAAGnH,QAAQ,CAACjI,SAAS,GAAG,CAAC,GAAG,GAAG;MACxD,MAAMgQ,aAAa,GAAGnO,WAAW,CAACE,UAAU,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC;;MAE3D;MACA,MAAMkO,SAAS,GAAG,IAAI7N,IAAI,CAAC,CAAC,CAAC8N,QAAQ,CAAC,CAAC;MACvC,MAAMC,eAAe,GAAGF,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;;MAErE,MAAMR,UAAU,GAAG,CACjBM,aAAa,GAAG,GAAG,GACnBZ,aAAa,GAAG,GAAG,GACnBC,mBAAmB,GAAG,GAAG,GACzBe,eAAe,GAAG,GAAG,IACnBH,aAAa;MAEjB,OAAO;QAAE/H,QAAQ;QAAEyH,KAAK,EAAED,UAAU;QAAEP,QAAQ;QAAErO,GAAG,EAAEqO,QAAQ,GAAG;MAAI,CAAC;IACvE,CAAC,CAAC,CAACS,IAAI,CAAC,CAACnC,CAAC,EAAEoC,CAAC,KAAKA,CAAC,CAACF,KAAK,GAAGlC,CAAC,CAACkC,KAAK,CAAC,CAAC,CAAC,CAAC;IAEvC,OAAOI,cAAc;EACvB,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAGA,CAACC,WAAW,EAAEC,WAAW,GAAG,IAAI,KAAK;IAC7D,MAAMtQ,SAAS,GAAG+C,iBAAiB,CAACiH,IAAI,CAACiB,CAAC,IAAIA,CAAC,CAACzL,EAAE,KAAK6Q,WAAW,CAAC;IACnE,IAAI,CAACrQ,SAAS,EAAE;IAEhB,IAAIyB,iBAAiB;IAErB,IAAI6O,WAAW,EAAE;MACf;MACA7O,iBAAiB,GAAGF,UAAU,CAACyI,IAAI,CAACwD,CAAC,IAAIA,CAAC,CAAChO,EAAE,KAAK8Q,WAAW,CAAC;IAChE,CAAC,MAAM;MACL;MACA,MAAMC,OAAO,GAAGxB,oBAAoB,CAAC/O,SAAS,CAAC;MAC/CyB,iBAAiB,GAAG8O,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEtH,SAAS;IACxC;IAEA,IAAI,CAACxH,iBAAiB,EAAE;;IAExB;IACA,MAAM+O,eAAe,GAAGX,qBAAqB,CAAC7P,SAAS,EAAEyB,iBAAiB,CAAC;IAE3ED,aAAa,CAAC+E,IAAI,IAAIA,IAAI,CAAClF,GAAG,CAACuF,GAAG,IAChCA,GAAG,CAACpH,EAAE,KAAKiC,iBAAiB,CAACjC,EAAE,GAC3B;MACE,GAAGoH,GAAG;MACN1G,MAAM,EAAE,YAAY;MACpBF,SAAS,EAAE;QACT,GAAGA,SAAS;QACZyQ,gBAAgB,EAAED,eAAe,CAACvI,QAAQ,CAACxI,IAAI;QAC/CoB,GAAG,EAAE,GAAG2F,IAAI,CAACkK,KAAK,CAACF,eAAe,CAAC3P,GAAG,CAAC,MAAM;QAC7CC,YAAY,EAAEd,SAAS,CAACoD,gBAAgB,CAACC;MAC3C;IACF,CAAC,GACDuD,GACN,CAAC,CAAC;IAEF5D,oBAAoB,CAACuD,IAAI,IAAIA,IAAI,CAAClF,GAAG,CAAC4J,CAAC,IACrCA,CAAC,CAACzL,EAAE,KAAK6Q,WAAW,GAChB;MACE,GAAGpF,CAAC;MACJ9H,iBAAiB,EAAE1B,iBAAiB,CAACjC,EAAE;MACvCU,MAAM,EAAE,YAAY;MACpBuQ,gBAAgB,EAAED,eAAe,CAACvI,QAAQ,CAACxI,IAAI;MAC/CkR,gBAAgB,EAAE,IAAIvO,IAAI,CAACA,IAAI,CAACc,GAAG,CAAC,CAAC,GAAGsN,eAAe,CAAC3P,GAAG,GAAG,KAAK;IACrE,CAAC,GACDoK,CACN,CAAC,CAAC;;IAEF;IACA/G,WAAW,CAACqC,IAAI,KAAK;MACnB,GAAGA,IAAI;MACPjC,cAAc,EAAEiC,IAAI,CAACjC,cAAc,GAAG,CAAC;MACvCF,UAAU,EAAE,IAAIhC,IAAI,CAAC;IACvB,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMwO,cAAc,GAAIC,SAAS,IAAK;IACpCrO,YAAY,CAAC+D,IAAI,KAAK;MACpB,GAAGA,IAAI;MACP,CAACsK,SAAS,GAAG,CAACtK,IAAI,CAACsK,SAAS;IAC9B,CAAC,CAAC,CAAC;;IAEH;IACA;EACF,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,kBAAkB,GAAG,CACzB;MACEzN,QAAQ,EAAE,CAAC,YAAY,EAAE,qBAAqB,EAAE,UAAU,CAAC;MAC3DQ,MAAM,EAAE;QAAEC,SAAS,EAAE,EAAE,GAAGyC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE;QAAE3C,EAAE,EAAE;MAAQ,CAAC;MAC3D+J,OAAO,EAAE,MAAM;MACfnK,GAAG,EAAE,EAAE,GAAG4C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG;IAC5B,CAAC,EACD;MACErD,QAAQ,EAAE,CAAC,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC;MAC/CQ,MAAM,EAAE;QAAEC,SAAS,EAAE,GAAG,GAAGyC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE;QAAE3C,EAAE,EAAE;MAAQ,CAAC;MAC5D+J,OAAO,EAAE,MAAM;MACfnK,GAAG,EAAE,EAAE,GAAG4C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG;IAC5B,CAAC,EACD;MACErD,QAAQ,EAAE,CAAC,qBAAqB,EAAE,UAAU,EAAE,WAAW,CAAC;MAC1DQ,MAAM,EAAE;QAAEC,SAAS,EAAE,EAAE,GAAGyC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,EAAE;QAAE3C,EAAE,EAAE;MAAS,CAAC;MAC5D+J,OAAO,EAAE,QAAQ;MACjBnK,GAAG,EAAE,EAAE,GAAG4C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG;IAC5B,CAAC,CACF;IAED,MAAMqK,SAAS,GAAG,CAAC,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,QAAQ,CAAC;IAC3E,MAAMC,OAAO,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,CAAC;IAEzE,MAAMC,QAAQ,GAAGH,kBAAkB,CAACvK,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGoK,kBAAkB,CAACjC,MAAM,CAAC,CAAC;IAC1F,MAAM1L,gBAAgB,GAAG0K,iBAAiB,CAACoD,QAAQ,CAAC5N,QAAQ,EAAE4N,QAAQ,CAACpN,MAAM,EAAEoN,QAAQ,CAACnD,OAAO,CAAC;IAEhG,MAAMoD,YAAY,GAAG;MACnB3R,EAAE,EAAE,MAAM4R,MAAM,CAAChP,IAAI,CAACc,GAAG,CAAC,CAAC,CAAC,CAACmO,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MACxCxR,IAAI,EAAEuD,gBAAgB,CAACvD,IAAI;MAC3Be,QAAQ,EAAEwC,gBAAgB,CAAC8K,QAAQ,GAAG,GAAG,GAAG,UAAU,GAC5C9K,gBAAgB,CAAC8K,QAAQ,GAAG,GAAG,GAAG,MAAM,GAAG,QAAQ;MAC7D/N,QAAQ,EAAE6Q,SAAS,CAACxK,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGqK,SAAS,CAAClC,MAAM,CAAC,CAAC;MACjEpP,MAAM,EAAE;QACNC,GAAG,EAAE,OAAO,GAAG,CAAC6G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG;QAC1C/G,GAAG,EAAE,OAAO,GAAG,CAAC4G,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;MACzC,CAAC;MACD1D,UAAU,EAAE,IAAIb,IAAI,CAAC,CAAC;MACtBe,iBAAiB,EAAE,IAAI;MACvBjD,MAAM,EAAE,SAAS;MACjBkD,gBAAgB,EAAE;QAChBC,UAAU,EAAED,gBAAgB,CAACC,UAAU;QACvCC,QAAQ,EAAE4N,QAAQ,CAAC5N,QAAQ;QAC3BC,SAAS,EAAEH,gBAAgB,CAAC8K,QAAQ;QACpC1K,mBAAmB,EAAE1D,SAAS,CAAC0G,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG7G,SAAS,CAACgP,MAAM,CAAC,CAAC,CAACrP,IAAI;QACjFgE,iBAAiB,EAAEL,gBAAgB,CAAC8K,QAAQ,GAAG,GAAG,GAAG,UAAU,GAAG;MACpE,CAAC;MACDxK,MAAM,EAAEuN,OAAO,CAACzK,IAAI,CAACsD,KAAK,CAACtD,IAAI,CAACG,MAAM,CAAC,CAAC,GAAGsK,OAAO,CAACnC,MAAM,CAAC,CAAC;MAC3DnL,WAAW,EAAE;QACXC,GAAG,EAAE4C,IAAI,CAACkK,KAAK,CAACQ,QAAQ,CAACtN,GAAG,CAAC;QAC7BC,MAAM,EAAE2C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,MAAM,GAAG,QAAQ;QAC/C7C,MAAM,EAAEoN,QAAQ,CAACpN;MACnB;IACF,CAAC;IAEDd,oBAAoB,CAACuD,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE4K,YAAY,CAAC,CAAC;;IAErD;IACA,IAAI/N,gBAAgB,CAACC,UAAU,GAAG,IAAI,EAAE;MACtCiO,UAAU,CAAC,MAAM;QACflB,iBAAiB,CAACe,YAAY,CAAC3R,EAAE,CAAC;MACpC,CAAC,EAAE,IAAI,CAAC;IACV;EACF,CAAC;EAGD,oBACEF,OAAA;IAAKiS,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAE9BlS,OAAA;MAAQiS,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBACjClS,OAAA;QAAKiS,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BlS,OAAA;UAAIiS,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC1BlS,OAAA;YAAMiS,SAAS,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gDAErC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtS,OAAA;UAAKiS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BlS,OAAA;YAAMiS,SAAS,EAAE,oBAAoBlP,YAAY;UAAG;YAAAoP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,mBAC7C,EAACvP,YAAY,CAACwP,MAAM,CAAC,CAAC,CAAC,CAACxI,WAAW,CAAC,CAAC,GAAGhH,YAAY,CAACgP,KAAK,CAAC,CAAC,CAAC;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtS,OAAA;QAAKiS,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BlS,OAAA;UAAKiS,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BlS,OAAA;YACEiS,SAAS,EAAE,WAAWtQ,WAAW,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;YACpE6Q,OAAO,EAAEA,CAAA,KAAM5Q,cAAc,CAAC,WAAW,CAAE;YAAAsQ,QAAA,EAC5C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtS,OAAA;YACEiS,SAAS,EAAE,WAAWtQ,WAAW,KAAK,qBAAqB,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC9E6Q,OAAO,EAAEA,CAAA,KAAM5Q,cAAc,CAAC,qBAAqB,CAAE;YAAAsQ,QAAA,EACtD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtS,OAAA;YACEiS,SAAS,EAAE,WAAWtQ,WAAW,KAAK,mBAAmB,GAAG,QAAQ,GAAG,EAAE,EAAG;YAC5E6Q,OAAO,EAAEA,CAAA,KAAM5Q,cAAc,CAAC,mBAAmB,CAAE;YAAAsQ,QAAA,EACpD;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtS,OAAA;YACEiS,SAAS,EAAE,WAAWtQ,WAAW,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;YACrE6Q,OAAO,EAAEA,CAAA,KAAM5Q,cAAc,CAAC,YAAY,CAAE;YAAAsQ,QAAA,EAC7C;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtS,OAAA;QAAKiS,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BlS,OAAA;UAAKiS,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEhF,UAAU,CAACtK,WAAW;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC7DtS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,EAAEtP,WAAW,CAAC6P,kBAAkB,CAAC;UAAC;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC,eACNtS,OAAA;UAAQiS,SAAS,EAAC,cAAc;UAACO,OAAO,EAAE5L,cAAe;UAAAsL,QAAA,gBACvDlS,OAAA;YAAMiS,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjDtS,OAAA;YAAMiS,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGR3Q,WAAW,KAAK,WAAW,iBAC1B3B,OAAA;MAAKiS,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAG/BlS,OAAA;QAAKiS,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/BlS,OAAA;UAAKiS,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnClS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlS,OAAA;cAAAkS,QAAA,EAAI;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxCtS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBACEiS,SAAS,EAAE,WAAWhP,SAAS,CAACE,OAAO,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC1DqP,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC,SAAS,CAAE;gBAAAY,QAAA,EAC1C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtS,OAAA;gBACEiS,SAAS,EAAE,WAAWhP,SAAS,CAACzC,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;gBAC5DgS,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC,WAAW,CAAE;gBAAAY,QAAA,EAC5C;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtS,OAAA;gBACEiS,SAAS,EAAE,WAAWhP,SAAS,CAACG,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;gBACzDoP,OAAO,EAAEA,CAAA,KAAMlB,cAAc,CAAC,QAAQ,CAAE;gBAAAY,QAAA,EACzC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACTtS,OAAA;gBACEiS,SAAS,EAAC,SAAS;gBACnBO,OAAO,EAAEhB,qBAAsB;gBAC/BkB,KAAK,EAAE;kBAAEC,UAAU,EAAE,MAAM;kBAAEC,eAAe,EAAE,SAAS;kBAAEtK,KAAK,EAAE,OAAO;kBAAEuK,MAAM,EAAE;gBAAoB,CAAE;gBAAAX,QAAA,EACxG;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENtS,OAAA;YAAK0S,KAAK,EAAE;cAAE5J,QAAQ,EAAE,UAAU;cAAEgK,MAAM,EAAE;YAAoB,CAAE;YAAAZ,QAAA,gBAChElS,OAAA;cAAKE,EAAE,EAAC,eAAe;cAAC+R,SAAS,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxDtS,OAAA;cAAKiS,SAAS,EAAC,sBAAsB;cAAAC,QAAA,eACnClS,OAAA;gBAAKiS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClS,OAAA;kBAAMiS,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,GAC9BjQ,UAAU,CAACiN,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAACtN,MAAM,KAAK,WAAW,CAAC,CAAC4O,MAAM,EAAC,YAC3D;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPtS,OAAA;kBAAMiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,GAC3BjQ,UAAU,CAACiN,MAAM,CAAChB,CAAC,IAAIA,CAAC,CAACtN,MAAM,KAAK,YAAY,IAAIsN,CAAC,CAACtN,MAAM,KAAK,UAAU,CAAC,CAAC4O,MAAM,EAAC,WACvF;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACPtS,OAAA;kBAAM0S,KAAK,EAAE;oBAAEpK,KAAK,EAAE,SAAS;oBAAEyK,UAAU,EAAE;kBAAM,CAAE;kBAAAb,QAAA,GAClDzO,iBAAiB,CAAC+L,MAAM,EAAC,qBAC5B;gBAAA;kBAAA2C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtS,OAAA;YAAKiS,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAMiS,SAAS,EAAC,aAAa;gBAACS,KAAK,EAAE;kBAAEE,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9EtS,OAAA;gBAAAkS,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAMiS,SAAS,EAAC,aAAa;gBAACS,KAAK,EAAE;kBAAEE,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9EtS,OAAA;gBAAAkS,QAAA,EAAM;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAMiS,SAAS,EAAC,aAAa;gBAACS,KAAK,EAAE;kBAAEE,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9EtS,OAAA;gBAAAkS,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAMiS,SAAS,EAAC,aAAa;gBAACS,KAAK,EAAE;kBAAEE,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC9EtS,OAAA;gBAAAkS,QAAA,EAAM;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClB,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAMiS,SAAS,EAAC,aAAa;gBAACS,KAAK,EAAE;kBAAEE,eAAe,EAAE;gBAAU,CAAE;gBAAAV,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7EtS,OAAA;gBAAAkS,QAAA,EAAM;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtS,OAAA;UAAKiS,SAAS,EAAC,2BAA2B;UAAAC,QAAA,gBAGxClS,OAAA;YAAKiS,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1ClS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAAkS,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBAC3BlS,OAAA;kBAAMiS,SAAS,EAAE,oBAAoB9M,WAAW,CAACE,WAAW,GAAG,WAAW,GAAG,OAAO;gBAAG;kBAAA8M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC/FtS,OAAA;kBAAAkS,QAAA,EAAO/M,WAAW,CAACE,WAAW,GAAG,cAAc,GAAG;gBAAO;kBAAA8M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtS,OAAA;cAAKiS,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtClS,OAAA;gBAAKiS,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlS,OAAA;kBAAKiS,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAE/M,WAAW,CAACG,kBAAkB,CAACkK;kBAAM;oBAAA2C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzEtS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAmB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClD,CAAC,eACNtS,OAAA;kBAAKiS,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAE,CAAC/M,WAAW,CAACJ,QAAQ,GAAG,GAAG,EAAEiO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;kBAAA;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC5EtS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAA0B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzD,CAAC,eACNtS,OAAA;kBAAKiS,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzBlS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAE/M,WAAW,CAACM;kBAAmB;oBAAA0M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnEtS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAqB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,eAC7BlS,OAAA;kBAAKiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBAC3BlS,OAAA;oBAAKiS,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvCtS,OAAA;sBAAKiS,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BlS,OAAA;wBAAAkS,QAAA,EAAI;sBAAuB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAChCtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAwH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC/HtS,OAAA;wBAAKiS,SAAS,EAAC,YAAY;wBAAAC,QAAA,GAAC,SAAO,EAAC/M,WAAW,CAACK,YAAY;sBAAA;wBAAA2M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtS,OAAA;oBAAKiS,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAKiS,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BlS,OAAA;wBAAAkS,QAAA,EAAI;sBAAsB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC/BtS,OAAA;wBAAAkS,QAAA,GAAG,WAAS,EAAC/M,WAAW,CAACG,kBAAkB,CAAC2N,IAAI,CAAC,IAAI,CAAC,EAAC,kEAAgE;sBAAA;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC3HtS,OAAA;wBAAKiS,SAAS,EAAC,gBAAgB;wBAAAC,QAAA,EAC5B/M,WAAW,CAACG,kBAAkB,CAACyM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChQ,GAAG,CAACmR,IAAI,iBAClDlT,OAAA;0BAAiBiS,SAAS,EAAC,eAAe;0BAAAC,QAAA,EAAEgB;wBAAI,GAArCA,IAAI;0BAAAf,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAwC,CACxD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtS,OAAA;oBAAKiS,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAKiS,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BlS,OAAA;wBAAAkS,QAAA,EAAI;sBAA4B;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrCtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAqH;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eAC5HtS,OAAA;wBAAKiS,SAAS,EAAC,YAAY;wBAAAC,QAAA,GAAC,YAAU,EAAC,CAAC/M,WAAW,CAACJ,QAAQ,GAAG,GAAG,EAAEiO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAENtS,OAAA;oBAAKiS,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAC5BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrCtS,OAAA;sBAAKiS,SAAS,EAAC,iBAAiB;sBAAAC,QAAA,gBAC9BlS,OAAA;wBAAAkS,QAAA,EAAI;sBAAyB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAClCtS,OAAA;wBAAAkS,QAAA,EAAG;sBAA0H;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACjItS,OAAA;wBAAKiS,SAAS,EAAC,qBAAqB;wBAAAC,QAAA,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxD,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzBlS,OAAA;kBACEiS,SAAS,EAAC,gBAAgB;kBAC1BO,OAAO,EAAEA,CAAA,KAAMpN,cAAc,CAAC6B,IAAI,KAAK;oBAAE,GAAGA,IAAI;oBAAE5B,WAAW,EAAE,CAAC4B,IAAI,CAAC5B;kBAAY,CAAC,CAAC,CAAE;kBAAA6M,QAAA,EAEpF/M,WAAW,CAACE,WAAW,GAAG,mBAAmB,GAAG;gBAAqB;kBAAA8M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACTtS,OAAA;kBAAKiS,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,EAC7B/M,WAAW,CAACE,WAAW,GACtB,4EAA4E,GAC5E;gBAAsD;kBAAA8M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAErD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtS,OAAA;YAAKiS,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClClS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAAkS,QAAA,EAAI;cAA+B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxCtS,OAAA;gBAAKiS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,GAAC,uBACX,eAAAlS,OAAA;kBAAMiS,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAE,CAACxM,WAAW,CAACO,kBAAkB,GAAG,GAAG,EAAE+M,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;gBAAA;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9G,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtS,OAAA;cAAKiS,SAAS,EAAC,iBAAiB;cAAAC,QAAA,gBAC9BlS,OAAA;gBAAKiS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BlS,OAAA;kBAAKiS,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,gBAClClS,OAAA;oBAAAkS,QAAA,EAAI;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACrCtS,OAAA;oBAAAkS,QAAA,EAAG;kBAA8K;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,eAAe;gBAAAC,QAAA,gBAC5BlS,OAAA;kBAAAkS,QAAA,EAAI;gBAA6B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtCtS,OAAA;kBAAKiS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC3BxM,WAAW,CAACE,iBAAiB,CAAC7D,GAAG,CAAC,CAACoR,OAAO,EAAEC,KAAK,kBAChDpT,OAAA;oBAAiBiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBACvClS,OAAA;sBAAKiS,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BlS,OAAA;wBAAMiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAEiB,OAAO,CAACtN;sBAAI;wBAAAsM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACpDtS,OAAA;wBAAMiS,SAAS,EAAE,cAAckB,OAAO,CAACrN,IAAI,GAAG,GAAG,GAAG,MAAM,GAAGqN,OAAO,CAACrN,IAAI,GAAG,GAAG,GAAG,QAAQ,GAAG,KAAK,EAAG;wBAAAoM,QAAA,EAClGiB,OAAO,CAACrN,IAAI,GAAG,GAAG,GAAG,WAAW,GAAGqN,OAAO,CAACrN,IAAI,GAAG,GAAG,GAAG,aAAa,GAAG;sBAAU;wBAAAqM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNtS,OAAA;sBAAKiS,SAAS,EAAC,eAAe;sBAAAC,QAAA,gBAC5BlS,OAAA;wBAAKiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBAC3BlS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAW;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eAC/CtS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAE,CAACiB,OAAO,CAACrN,IAAI,GAAG,GAAG,EAAEkN,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;wBAAA;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnE,CAAC,eACNtS,OAAA;wBAAKiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBAC3BlS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACpDtS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAEiB,OAAO,CAACzQ;wBAAS;0BAAAyP,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpD,CAAC,eACNtS,OAAA;wBAAKiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,gBAC3BlS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,EAAC;wBAAe;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACnDtS,OAAA;0BAAMiS,SAAS,EAAC,YAAY;0BAAAC,QAAA,GAAEiB,OAAO,CAACpN,kBAAkB,EAAC,eAAG;wBAAA;0BAAAoM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChE,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GApBEc,KAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,eAC/BlS,OAAA;kBAAKiS,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnClS,OAAA;oBAAKiS,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAwB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACjCtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAmE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACvE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7BtS,OAAA;wBAAAkS,QAAA,EAAG;sBAA2D;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACvCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAe;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACxBtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAoE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,gBAC9BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7BtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAiE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNtS,OAAA;YAAKiS,SAAS,EAAC,0BAA0B;YAAAC,QAAA,gBACvClS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAAkS,QAAA,EAAI;cAAmC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5CtS,OAAA;gBAAKiS,SAAS,EAAC,qBAAqB;gBAAAC,QAAA,gBAClClS,OAAA;kBAAMiS,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACtDtS,OAAA;kBAAAkS,QAAA,EAAOhM,kBAAkB,CAACM;gBAAa;kBAAA2L,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENtS,OAAA;cAAKiS,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnClS,OAAA;gBAAKiS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpClS,OAAA;kBAAKiS,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvClS,OAAA;oBAAAkS,QAAA,EAAI;kBAA6B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACtCtS,OAAA;oBAAAkS,QAAA,EAAG;kBAA0K;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9K,CAAC,eAENtS,OAAA;kBAAKiS,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,gBACjClS,OAAA;oBAAKiS,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBlS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEhM,kBAAkB,CAACI,QAAQ,CAAC+M,cAAc,CAAC;oBAAC;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFtS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAe;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBlS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEhM,kBAAkB,CAACK,OAAO,CAACiJ;oBAAM;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrEtS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAc;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACxBlS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAEhM,kBAAkB,CAACE,aAAa,CAACoJ;oBAAM;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC3EtS,OAAA;sBAAKiS,SAAS,EAAC,YAAY;sBAAAC,QAAA,EAAC;oBAAa;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,gBACjClS,OAAA;kBAAAkS,QAAA,EAAI;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC7BtS,OAAA;kBAAKiS,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAChChM,kBAAkB,CAACE,aAAa,CAACrE,GAAG,CAAC,CAACuR,OAAO,EAAEF,KAAK,kBACnDpT,OAAA;oBAAiBiS,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC5ClS,OAAA;sBAAKiS,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,gBAC7BlS,OAAA;wBAAMiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAEoB,OAAO,CAACnT;sBAAI;wBAAAgS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC,eACpDtS,OAAA;wBAAMiS,SAAS,EAAE,kBAAkBqB,OAAO,CAAC1S,MAAM,EAAG;wBAAAsR,QAAA,gBAClDlS,OAAA;0BAAMiS,SAAS,EAAC;wBAAY;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC,EACnCgB,OAAO,CAAC1S,MAAM,CAACmJ,WAAW,CAAC,CAAC;sBAAA;wBAAAoI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNtS,OAAA;sBAAKiS,SAAS,EAAC,gBAAgB;sBAAAC,QAAA,GAAC,UACtB,eAAAlS,OAAA;wBAAMiS,SAAS,EAAC,cAAc;wBAAAC,QAAA,EAAEoB,OAAO,CAACjN;sBAAM;wBAAA8L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA,GAVEc,KAAK;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWV,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpClS,OAAA;kBAAKiS,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7BtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAuE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3E,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACzBtS,OAAA;wBAAAkS,QAAA,EAAG;sBAA2D;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAY;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACrBtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAkE;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAmB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC5BtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAyD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACtCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7BtS,OAAA;wBAAAkS,QAAA,GAAG,wBAAsB,EAAChM,kBAAkB,CAACK,OAAO,CAAC0M,IAAI,CAAC,IAAI,CAAC,EAAC,8BAA4B;sBAAA;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,cAAc;oBAAAC,QAAA,gBAC3BlS,OAAA;sBAAKiS,SAAS,EAAC,cAAc;sBAAAC,QAAA,EAAC;oBAAC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACrCtS,OAAA;sBAAAkS,QAAA,gBACElS,OAAA;wBAAAkS,QAAA,EAAI;sBAAoB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eAC7BtS,OAAA;wBAAAkS,QAAA,EAAG;sBAAwD;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENtS,OAAA;gBAAKiS,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnClS,OAAA;kBAAAkS,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpCtS,OAAA;kBAAKiS,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,gBAC/BlS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAA2B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAA0B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAA6B;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAAmB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,kBAAkB;oBAAAC,QAAA,gBAC/BlS,OAAA;sBAAMiS,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC5CtS,OAAA;sBAAAkS,QAAA,EAAM;oBAAwB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtS,OAAA;QAAKiS,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAGhClS,OAAA;UAAKiS,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlS,OAAA;cAAAkS,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9BtS,OAAA;cAAMiS,SAAS,EAAE,aAAatN,QAAQ,CAAC/D,MAAM,EAAG;cAAAsR,QAAA,EAC7CvN,QAAQ,CAAC/D,MAAM,CAACmJ,WAAW,CAAC;YAAC;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNtS,OAAA;YAAKiS,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBlS,OAAA;cAAKiS,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE,CAACvN,QAAQ,CAACI,QAAQ,GAAG,GAAG,EAAEiO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC;cAAA;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC3EtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEvN,QAAQ,CAACK;cAAc;gBAAAmN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAEvN,QAAQ,CAACO,eAAe,EAAC,GAAC;cAAA;gBAAAiN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxBlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAEvN,QAAQ,CAACM;cAAc;gBAAAkN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC7DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtS,OAAA;YAAKiS,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAMiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtS,OAAA;gBAAAkS,QAAA,gBACElS,OAAA;kBAAKiS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAwB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7DtS,OAAA;kBAAKiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAMiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtS,OAAA;gBAAAkS,QAAA,gBACElS,OAAA;kBAAKiS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnDtS,OAAA;kBAAKiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BlS,OAAA;gBAAMiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxCtS,OAAA;gBAAAkS,QAAA,gBACElS,OAAA;kBAAKiS,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxDtS,OAAA;kBAAKiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtS,OAAA;UAAKiS,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBACpClS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BlS,OAAA;cAAAkS,QAAA,EAAI;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eACNtS,OAAA;YAAKiS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BlS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE3P,WAAW,CAACE,UAAU,EAAC,GAAC;cAAA;gBAAA0P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtDtS,OAAA;gBAAKiS,SAAS,EAAE,gBAAgB1P,WAAW,CAACE,UAAU,GAAG,EAAE,GAAG,MAAM,GAAG,QAAQ,EAAG;gBAAAyP,QAAA,EAC/E3P,WAAW,CAACE,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG;cAAU;gBAAA0P,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAE3P,WAAW,CAACG;cAAS;gBAAAyP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpDtS,OAAA;gBAAKiS,SAAS,EAAE,gBAAgB1P,WAAW,CAACG,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,EAAG;gBAAAwP,QAAA,EAC7E3P,WAAW,CAACG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;cAAU;gBAAAyP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNtS,OAAA;cAAKiS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1BlS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,GAAE3P,WAAW,CAACI,QAAQ,EAAC,OAAK;cAAA;gBAAAwP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/DtS,OAAA;gBAAKiS,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC7CtS,OAAA;gBAAKiS,SAAS,EAAE,gBAAgB1P,WAAW,CAACI,QAAQ,GAAG,EAAE,GAAG,KAAK,GAAG,QAAQ,EAAG;gBAAAuP,QAAA,EAC5E3P,WAAW,CAACI,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG;cAAW;gBAAAwP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtS,OAAA;UAAKiS,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAC5ClS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlS,OAAA;cAAAkS,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClCtS,OAAA;cAAK0S,KAAK,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEC,GAAG,EAAE,KAAK;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAvB,QAAA,gBAChElS,OAAA;gBAAMiS,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEzO,iBAAiB,CAAC+L;cAAM;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnEtS,OAAA;gBACEwS,OAAO,EAAEhB,qBAAsB;gBAC/BkB,KAAK,EAAE;kBACLgB,UAAU,EAAE,SAAS;kBACrBpL,KAAK,EAAE,OAAO;kBACduK,MAAM,EAAE,MAAM;kBACdc,YAAY,EAAE,KAAK;kBACnBC,OAAO,EAAE,SAAS;kBAClBC,QAAQ,EAAE,MAAM;kBAChBC,MAAM,EAAE,SAAS;kBACjBf,UAAU,EAAE;gBACd,CAAE;gBAAAb,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtS,OAAA;YAAKiS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BzO,iBAAiB,CAAC1B,GAAG,CAACrB,SAAS,iBAC9BV,OAAA;cAAwBiS,SAAS,EAAE,2BAA2BvR,SAAS,CAACY,QAAQ,EAAG;cAAA4Q,QAAA,gBACjFlS,OAAA;gBAAKiS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlS,OAAA;kBAAMiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAExR,SAAS,CAACR;gBAAE;kBAAAiS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDtS,OAAA;kBAAMiS,SAAS,EAAE,kBAAkBvR,SAAS,CAACY,QAAQ,EAAG;kBAAA4Q,QAAA,EACrDxR,SAAS,CAACY,QAAQ,CAACyI,WAAW,CAAC;gBAAC;kBAAAoI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNtS,OAAA;gBAAKiS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClS,OAAA;kBAAKiS,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAExR,SAAS,CAACH;gBAAI;kBAAA4R,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACtDtS,OAAA;kBAAKiS,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,eAAG,EAACxR,SAAS,CAACG,QAAQ;gBAAA;kBAAAsR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjEtS,OAAA;kBAAKiS,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,GAAC,eAC3B,EAAChL,IAAI,CAACsD,KAAK,CAAC,CAAC5H,WAAW,GAAGlC,SAAS,CAACiD,UAAU,IAAI,KAAK,CAAC,EAAC,UAC/D;gBAAA;kBAAAwO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAGNtS,OAAA;kBAAKiS,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChClS,OAAA;oBAAKiS,SAAS,EAAC,eAAe;oBAAAC,QAAA,GAAC,mBACtB,eAAAlS,OAAA;sBAAM0S,KAAK,EAAE;wBAAEpK,KAAK,EAAE,SAAS;wBAAEyK,UAAU,EAAE;sBAAM,CAAE;sBAAAb,QAAA,GACzD,CAACxR,SAAS,CAACoD,gBAAgB,CAACC,UAAU,GAAG,GAAG,EAAEiP,OAAO,CAAC,CAAC,CAAC,EAAC,GAC5D;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACNtS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,qBACjB,eAAAlS,OAAA;sBAAM0S,KAAK,EAAE;wBACpBpK,KAAK,EAAE5H,SAAS,CAACoD,gBAAgB,CAACG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,SAAS;wBACzE8O,UAAU,EAAE;sBACd,CAAE;sBAAAb,QAAA,GACC,CAACxR,SAAS,CAACoD,gBAAgB,CAACG,SAAS,GAAG,GAAG,EAAE+O,OAAO,CAAC,CAAC,CAAC,EAAC,GAC3D;oBAAA;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENtS,OAAA;kBAAKiS,SAAS,EAAC,kBAAkB;kBAAAC,QAAA,GAAC,UACxB,eAAAlS,OAAA;oBAAM0S,KAAK,EAAE;sBACnBpK,KAAK,EAAE5H,SAAS,CAACE,MAAM,KAAK,YAAY,GAAG,SAAS,GAC7CF,SAAS,CAACE,MAAM,KAAK,UAAU,GAAG,SAAS,GAAG,SAAS;sBAC9DmS,UAAU,EAAE;oBACd,CAAE;oBAAAb,QAAA,EACCxR,SAAS,CAACE,MAAM,CAAC2R,MAAM,CAAC,CAAC,CAAC,CAACxI,WAAW,CAAC,CAAC,GAAGrJ,SAAS,CAACE,MAAM,CAACmR,KAAK,CAAC,CAAC;kBAAC;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,EAEL5R,SAAS,CAACmD,iBAAiB,iBAC1B7D,OAAA;kBAAKiS,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,GAAC,eAC/B,EAACxR,SAAS,CAACmD,iBAAiB,EAC9BnD,SAAS,CAACyQ,gBAAgB,iBACzBnR,OAAA;oBAAK0S,KAAK,EAAE;sBAAEmB,QAAQ,EAAE,MAAM;sBAAEE,SAAS,EAAE;oBAAM,CAAE;oBAAA7B,QAAA,GAAC,sBAC7C,EAACxR,SAAS,CAACyQ,gBAAgB;kBAAA;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GAnDE5R,SAAS,CAACR,EAAE;cAAAiS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoDjB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtS,OAAA;UAAKiS,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClS,OAAA;YAAKiS,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BlS,OAAA;cAAAkS,QAAA,EAAI;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACNtS,OAAA;YAAKiS,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAC5BjQ,UAAU,CAACF,GAAG,CAAC4H,SAAS,iBACvB3J,OAAA;cAEEiS,SAAS,EAAE,kBAAkBtI,SAAS,CAAC/I,MAAM,IAAI,CAAAuB,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEjC,EAAE,MAAKyJ,SAAS,CAACzJ,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;cAC5GsS,OAAO,EAAEA,CAAA,KAAMpQ,oBAAoB,CAACuH,SAAS,CAAE;cAAAuI,QAAA,gBAE/ClS,OAAA;gBAAKiS,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlS,OAAA;kBAAMiS,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEvI,SAAS,CAACzJ;gBAAE;kBAAAiS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDtS,OAAA;kBACEiS,SAAS,EAAC,YAAY;kBACtBS,KAAK,EAAE;oBAAEE,eAAe,EAAE/I,cAAc,CAACF,SAAS,CAAC/I,MAAM;kBAAE;gBAAE;kBAAAuR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNtS,OAAA;gBAAKiS,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlS,OAAA;kBAAKiS,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBlS,OAAA;oBAAAkS,QAAA,GAAK,iCAAM,EAACvI,SAAS,CAAC7I,MAAM;kBAAA;oBAAAqR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnCtS,OAAA;oBAAAkS,QAAA,GAAK,eAAG,EAACvI,SAAS,CAAC5I,KAAK;kBAAA;oBAAAoR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACNtS,OAAA;kBAAKiS,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BlS,OAAA;oBAAKiS,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,SACxB,EAACvI,SAAS,CAAC3I,IAAI,EAAC,GAClB,eAAAhB,OAAA;sBAAKiS,SAAS,EAAC,UAAU;sBAAAC,QAAA,eACvBlS,OAAA;wBACEiS,SAAS,EAAC,WAAW;wBACrBS,KAAK,EAAE;0BACLsB,KAAK,EAAE,GAAGrK,SAAS,CAAC3I,IAAI,GAAG;0BAC3B4R,eAAe,EAAEjJ,SAAS,CAAC3I,IAAI,GAAG,EAAE,GAAG,SAAS,GAAG;wBACrD;sBAAE;wBAAAmR,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACL3I,SAAS,CAACjJ,SAAS,iBAClBV,OAAA;kBAAKiS,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnClS,OAAA;oBAAKiS,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEvI,SAAS,CAACjJ,SAAS,CAACH;kBAAI;oBAAA4R,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjEtS,OAAA;oBAAKiS,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,OAAK,EAACvI,SAAS,CAACjJ,SAAS,CAACa,GAAG;kBAAA;oBAAA4Q,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN,EACA3I,SAAS,CAAC/I,MAAM,KAAK,WAAW,iBAC/BZ,OAAA;kBACEwS,OAAO,EAAEA,CAAA,KAAM;oBACb,MAAMyB,gBAAgB,GAAGxQ,iBAAiB,CAACiH,IAAI,CAACiB,CAAC,IAAI,CAACA,CAAC,CAAC9H,iBAAiB,CAAC;oBAC1E,IAAIoQ,gBAAgB,EAAE;sBACpBnD,iBAAiB,CAACmD,gBAAgB,CAAC/T,EAAE,EAAEyJ,SAAS,CAACzJ,EAAE,CAAC;oBACtD;kBACF,CAAE;kBACFwS,KAAK,EAAE;oBACLgB,UAAU,EAAE,SAAS;oBACrBpL,KAAK,EAAE,OAAO;oBACduK,MAAM,EAAE,MAAM;oBACdc,YAAY,EAAE,KAAK;oBACnBC,OAAO,EAAE,UAAU;oBACnBC,QAAQ,EAAE,MAAM;oBAChBC,MAAM,EAAE,SAAS;oBACjBf,UAAU,EAAE,KAAK;oBACjBgB,SAAS,EAAE,KAAK;oBAChBC,KAAK,EAAE;kBACT,CAAE;kBACFE,QAAQ,EAAE,CAACzQ,iBAAiB,CAAC2L,IAAI,CAACzD,CAAC,IAAI,CAACA,CAAC,CAAC9H,iBAAiB,CAAE;kBAAAqO,QAAA,EAE5DzO,iBAAiB,CAAC2L,IAAI,CAACzD,CAAC,IAAI,CAACA,CAAC,CAAC9H,iBAAiB,CAAC,GAAG,gBAAgB,GAAG;gBAAS;kBAAAsO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3E,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GA7DD3I,SAAS,CAACzJ,EAAE;cAAAiS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8Dd,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACN,EAGA3Q,WAAW,KAAK,qBAAqB,iBACpC3B,OAAA,CAACJ,yBAAyB;MAAAuS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC7B,EAGA3Q,WAAW,KAAK,mBAAmB,iBAClC3B,OAAA,CAACH,sBAAsB;MAAAsS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAC1B,EAGA3Q,WAAW,KAAK,YAAY,iBAC3B3B,OAAA,CAACF,SAAS;MAAAqS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACb;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEE,CAAC;AAEV,CAAC;AAAC5Q,EAAA,CA30CID,GAAG;AAAA0S,EAAA,GAAH1S,GAAG;AA60CT,eAAeA,GAAG;AAAC,IAAA0S,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}