.mobile-app-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.mobile-app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-app-header h1 {
  color: var(--text-primary);
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.app-time {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.mobile-demo-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.demo-description {
  flex: 1;
  max-width: 400px;
}

.demo-description h2 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 24px;
  font-weight: 700;
}

.demo-description p {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.mobile-phone-frame {
  flex-shrink: 0;
  width: 420px;
  height: 840px;
  background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
  border-radius: 45px;
  padding: 14px;
  box-shadow:
    0 0 0 3px #333,
    0 12px 50px rgba(0, 0, 0, 0.5),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.mobile-phone-frame::before {
  content: '';
  position: absolute;
  top: 22px;
  left: 50%;
  transform: translateX(-50%);
  width: 90px;
  height: 7px;
  background: linear-gradient(90deg, #444, #333);
  border-radius: 4px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.5);
}

.mobile-phone-frame::after {
  content: '';
  position: absolute;
  right: -4px;
  top: 140px;
  width: 5px;
  height: 70px;
  background: linear-gradient(180deg, #333, #222);
  border-radius: 0 3px 3px 0;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  border-radius: 32px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.phone-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 24px;
  background: var(--panel-bg);
  font-size: 15px;
  font-weight: 600;
  color: var(--text-primary);
  border-bottom: 1px solid var(--border-color);
  min-height: 50px;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.signal-bars {
  font-size: 16px;
}

.carrier {
  font-size: 14px;
  font-weight: 600;
  color: #10b981;
}

.status-center {
  font-family: 'Courier New', monospace;
  font-size: 15px;
  font-weight: 700;
  flex: 1;
  text-align: center;
}

.status-right {
  font-size: 14px;
  flex: 1;
  text-align: right;
}

.phone-content {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  background: var(--bg-primary);
  padding: 0;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.phone-content::-webkit-scrollbar {
  display: none;
}

.phone-home-indicator {
  height: 6px;
  width: 60px;
  background: #888;
  border-radius: 3px;
  margin: 14px auto;
  flex-shrink: 0;
}

.mobile-app-interface {
  padding: 28px 24px;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 2px solid var(--border-color);
}

.app-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 12px;
}

.app-icon {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(220, 38, 38, 0.3));
}

.app-title h3 {
  color: var(--text-primary);
  margin: 0;
  font-size: 24px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.location-indicator {
  color: var(--text-secondary);
  font-size: 14px;
  font-weight: 500;
  background: rgba(16, 185, 129, 0.1);
  padding: 6px 12px;
  border-radius: 12px;
  display: inline-block;
}

.quick-emergency-buttons {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 32px;
}

.quick-btn {
  padding: 20px 18px;
  border: none;
  border-radius: 16px;
  font-size: 17px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.quick-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.quick-btn:hover::before {
  left: 100%;
}

.quick-btn.critical {
  background: linear-gradient(135deg, #dc2626, #ef4444, #f87171);
  color: white;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.4);
}

.quick-btn.high {
  background: linear-gradient(135deg, #f59e0b, #fbbf24, #fcd34d);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
}

.quick-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.quick-btn:active {
  transform: translateY(0);
}

.emergency-form-mobile {
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1;
}

.form-group-mobile {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-group-mobile label {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 6px;
}

.mobile-input, .mobile-select, .mobile-textarea {
  padding: 18px 16px;
  border: 2px solid var(--border-color);
  border-radius: 14px;
  background: var(--panel-bg);
  color: var(--text-primary);
  font-size: 17px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.mobile-input:focus, .mobile-select:focus, .mobile-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.mobile-textarea {
  resize: none;
  min-height: 100px;
  line-height: 1.5;
}

.location-btn {
  margin-top: 10px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  align-self: flex-start;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.location-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.form-row-mobile {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 18px;
}

.voice-section-mobile {
  text-align: center;
  padding: 20px;
  background: rgba(59, 130, 246, 0.08);
  border-radius: 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  margin: 8px 0;
}

.voice-btn-mobile {
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.voice-btn-mobile:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.voice-btn-mobile.recording {
  background: linear-gradient(135deg, #dc2626, #ef4444);
  animation: pulse 1.5s infinite;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
}

.voice-transcript-mobile {
  margin-top: 16px;
  padding: 14px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 12px;
  font-size: 14px;
  color: var(--text-primary);
  font-style: italic;
  line-height: 1.4;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.emergency-call-btn {
  padding: 22px 20px;
  background: linear-gradient(135deg, #dc2626, #ef4444, #f87171);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
  position: relative;
  overflow: hidden;
}

.emergency-call-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.emergency-call-btn:hover:not(:disabled)::before {
  left: 100%;
}

.emergency-call-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626, #ef4444);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(220, 38, 38, 0.5);
}

.emergency-call-btn:active:not(:disabled) {
  transform: translateY(-1px);
}

.emergency-call-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.success-screen-mobile {
  padding: 32px 28px;
  text-align: center;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 28px;
}

.success-animation {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 24px;
}

.checkmark {
  font-size: 64px;
  z-index: 2;
  position: relative;
  filter: drop-shadow(0 4px 8px rgba(16, 185, 129, 0.3));
}

.pulse-ring {
  position: absolute;
  width: 100px;
  height: 100px;
  border: 4px solid #10b981;
  border-radius: 50%;
  animation: pulsate 2s infinite;
}

.pulse-ring::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-radius: 50%;
  animation: pulsate 2s infinite 0.5s;
}

@keyframes pulsate {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.6);
    opacity: 0;
  }
}

.success-screen-mobile h3 {
  color: var(--text-primary);
  margin: 0;
  font-size: 26px;
  font-weight: 700;
  letter-spacing: -0.5px;
}

.success-details-mobile {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 16px;
  padding: 20px;
  margin: 20px 0;
  border: 2px solid rgba(16, 185, 129, 0.2);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  color: var(--text-primary);
  font-size: 16px;
}

.detail-row:not(:last-child) {
  border-bottom: 1px solid rgba(16, 185, 129, 0.2);
}

.detail-row span:first-child {
  font-weight: 600;
  color: #059669;
}

.detail-row span:last-child {
  font-weight: 700;
  color: var(--text-primary);
}

.tracking-section {
  background: var(--panel-bg);
  border-radius: 16px;
  padding: 24px;
  margin: 20px 0;
  border: 2px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ambulance-icon {
  font-size: 40px;
  margin-bottom: 12px;
  animation: bounce 2s infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-12px);
  }
  60% {
    transform: translateY(-6px);
  }
}

.tracking-text {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #10b981, #06d6a0);
  border-radius: 4px;
  animation: progress 4s infinite;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

@keyframes progress {
  0% { width: 0%; }
  25% { width: 30%; }
  50% { width: 65%; }
  75% { width: 85%; }
  100% { width: 100%; }
}

.new-emergency-btn {
  padding: 16px 24px;
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.new-emergency-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4);
}

.desktop-form-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.emergency-request-form {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-section {
  padding: 30px;
}

.form-section h2 {
  color: var(--text-primary);
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 700;
}

.form-description {
  color: var(--text-secondary);
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input, .form-textarea, .form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.emergency-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.emergency-type-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.emergency-type-btn:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.emergency-type-btn.selected {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.emergency-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.emergency-name {
  font-weight: 600;
  font-size: 14px;
}

.voice-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.voice-btn {
  padding: 12px 20px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.voice-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.voice-btn.recording {
  background: #dc2626;
  border-color: #dc2626;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.voice-transcript {
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
}

.location-detected {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
}

.symptoms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.symptom-btn {
  padding: 10px 14px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
}

.symptom-btn:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.symptom-btn.selected {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.urgency-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.urgency-btn {
  flex: 1;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
}

.urgency-btn.low.selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.urgency-btn.medium.selected {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.urgency-btn.high.selected {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.urgency-btn.critical.selected {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
  animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.submit-emergency-btn {
  width: 100%;
  padding: 18px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submit-emergency-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.submit-emergency-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submission-success {
  text-align: center;
  padding: 40px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.success-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.submission-success h2 {
  color: var(--text-primary);
  margin-bottom: 25px;
  font-size: 28px;
  font-weight: 700;
}

.success-details {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: left;
}

.detail-item {
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 16px;
}

.detail-item strong {
  color: #059669;
  font-weight: 600;
}

.success-message {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 30px;
}

.new-request-btn {
  padding: 14px 28px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
}

.new-request-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
}

@media (max-width: 1400px) {
  .mobile-demo-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .demo-description {
    max-width: none;
  }
}

@media (max-width: 768px) {
  .mobile-app-container {
    padding: 15px;
  }

  .mobile-phone-frame {
    width: 380px;
    height: 760px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .emergency-types-grid {
    grid-template-columns: 1fr;
  }

  .symptoms-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }

  .urgency-buttons {
    flex-direction: column;
  }

  .form-row-mobile {
    grid-template-columns: 1fr;
  }

  .mobile-demo-section {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .mobile-phone-frame {
    width: 340px;
    height: 680px;
  }

  .mobile-demo-section {
    padding: 15px;
  }

  .app-title h3 {
    font-size: 22px;
  }

  .quick-btn {
    padding: 18px 16px;
    font-size: 16px;
  }

  .emergency-call-btn {
    padding: 20px 18px;
    font-size: 16px;
  }

  .mobile-app-interface {
    padding: 24px 20px;
  }

  .phone-status-bar {
    padding: 12px 20px;
  }
}
