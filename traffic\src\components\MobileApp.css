.mobile-app-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.mobile-app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-app-header h1 {
  color: var(--text-primary);
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.app-time {
  color: var(--text-secondary);
  font-size: 16px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.emergency-request-form {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.form-section {
  padding: 30px;
}

.form-section h2 {
  color: var(--text-primary);
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 700;
}

.form-description {
  color: var(--text-secondary);
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.5;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.form-input, .form-textarea, .form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.emergency-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  margin-top: 10px;
}

.emergency-type-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.emergency-type-btn:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.emergency-type-btn.selected {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

.emergency-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.emergency-name {
  font-weight: 600;
  font-size: 14px;
}

.voice-input-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.voice-btn {
  padding: 12px 20px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  align-self: flex-start;
}

.voice-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.voice-btn.recording {
  background: #dc2626;
  border-color: #dc2626;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.voice-transcript {
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.4;
}

.location-detected {
  margin-top: 8px;
  padding: 8px 12px;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
}

.symptoms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 8px;
  margin-top: 10px;
}

.symptom-btn {
  padding: 10px 14px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  text-align: center;
}

.symptom-btn:hover {
  border-color: #3b82f6;
  background: rgba(59, 130, 246, 0.05);
}

.symptom-btn.selected {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.urgency-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.urgency-btn {
  flex: 1;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  cursor: pointer;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.2s ease;
}

.urgency-btn.low.selected {
  border-color: #10b981;
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
}

.urgency-btn.medium.selected {
  border-color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

.urgency-btn.high.selected {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.urgency-btn.critical.selected {
  border-color: #dc2626;
  background: rgba(220, 38, 38, 0.2);
  color: #dc2626;
  animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.submit-emergency-btn {
  width: 100%;
  padding: 18px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #dc2626, #ef4444);
  color: white;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 20px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.submit-emergency-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #b91c1c, #dc2626);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.submit-emergency-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.submission-success {
  text-align: center;
  padding: 40px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.success-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.submission-success h2 {
  color: var(--text-primary);
  margin-bottom: 25px;
  font-size: 28px;
  font-weight: 700;
}

.success-details {
  background: rgba(16, 185, 129, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: left;
}

.detail-item {
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 16px;
}

.detail-item strong {
  color: #059669;
  font-weight: 600;
}

.success-message {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 30px;
}

.new-request-btn {
  padding: 14px 28px;
  border: 2px solid #3b82f6;
  border-radius: 8px;
  background: #3b82f6;
  color: white;
  cursor: pointer;
  font-weight: 600;
  font-size: 16px;
  transition: all 0.2s ease;
}

.new-request-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .mobile-app-container {
    padding: 15px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .emergency-types-grid {
    grid-template-columns: 1fr;
  }
  
  .symptoms-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  }
  
  .urgency-buttons {
    flex-direction: column;
  }
}
