import React, { useEffect, useState, useRef } from "react";
import "./App.css";
import PatientTransportOptimizer from "./components/PatientTransportOptimizer";
import TrafficSignalHijacking from "./components/TrafficSignalHijacking";
import MobileApp from "./components/MobileApp";
import Storyboard from "./components/Storyboard";

// Emergency locations and hospitals in Chennai
const emergencyLocations = [
  { id: 1, name: "Chennai Central Railway Station", coords: { lat: 13.0827, lng: 80.2707 }, type: "transport" },
  { id: 2, name: "Marina Beach", coords: { lat: 13.0500, lng: 80.2824 }, type: "public" },
  { id: 3, name: "T Nagar Commercial Complex", coords: { lat: 13.0418, lng: 80.2341 }, type: "commercial" },
  { id: 4, name: "Velachery IT Corridor", coords: { lat: 12.9756, lng: 80.2207 }, type: "business" },
  { id: 5, name: "Adyar Residential Area", coords: { lat: 13.0067, lng: 80.2206 }, type: "residential" },
  { id: 6, name: "Anna Nagar Metro Station", coords: { lat: 13.0850, lng: 80.2101 }, type: "transport" },
  { id: 7, name: "Guindy Industrial Estate", coords: { lat: 13.0067, lng: 80.2206 }, type: "industrial" },
];

const hospitals = [
  { id: 1, name: "Apollo Hospital", coords: { lat: 13.0358, lng: 80.2297 }, capacity: 85, emergency: true },
  { id: 2, name: "Fortis Malar Hospital", coords: { lat: 13.0067, lng: 80.2206 }, capacity: 92, emergency: true },
  { id: 3, name: "MIOT International", coords: { lat: 12.9756, lng: 80.2207 }, capacity: 78, emergency: true },
  { id: 4, name: "Stanley Medical College", coords: { lat: 13.0827, lng: 80.2707 }, capacity: 65, emergency: true },
  { id: 5, name: "Rajiv Gandhi Govt Hospital", coords: { lat: 13.1358, lng: 80.2297 }, capacity: 45, emergency: true },
];

// Enhanced ambulance data with AI capabilities
const initialAmbulances = [
  {
    id: "AMB001",
    status: "available",
    location: { lat: 13.0827, lng: 80.2707 },
    driver: "Raj Kumar",
    medic: "Dr. Priya",
    fuel: 85,
    lastMaintenance: "2024-01-10",
    equipment: ["AED", "Ventilator", "Cardiac Monitor"],
    specialization: "cardiac",
    aiScore: 0.92,
    responseTime: 3.2
  },
  {
    id: "AMB002",
    status: "dispatched",
    location: { lat: 13.0500, lng: 80.2824 },
    driver: "Suresh M",
    medic: "Dr. Arun",
    fuel: 92,
    lastMaintenance: "2024-01-08",
    equipment: ["Trauma Kit", "Spinal Board", "IV Fluids"],
    specialization: "trauma",
    aiScore: 0.88,
    responseTime: 2.8,
    emergency: { type: "cardiac", priority: "high", eta: "4 min", aiConfidence: 0.94 }
  },
  {
    id: "AMB003",
    status: "en-route",
    location: { lat: 13.0418, lng: 80.2341 },
    driver: "Karthik S",
    medic: "Dr. Meera",
    fuel: 67,
    lastMaintenance: "2024-01-12",
    equipment: ["Pediatric Kit", "Oxygen", "Defibrillator"],
    specialization: "pediatric",
    aiScore: 0.91,
    responseTime: 3.5,
    emergency: { type: "accident", priority: "critical", eta: "7 min", aiConfidence: 0.97 }
  },
  {
    id: "AMB004",
    status: "maintenance",
    location: { lat: 12.9756, lng: 80.2207 },
    driver: "Venkat R",
    medic: "Dr. Lakshmi",
    fuel: 23,
    lastMaintenance: "2024-01-15",
    equipment: ["Basic Life Support"],
    specialization: "general",
    aiScore: 0.85,
    responseTime: 4.1
  },
];

const App = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [darkMode, setDarkMode] = useState(false);
  const [map, setMap] = useState(null);
  const [ambulances, setAmbulances] = useState(initialAmbulances);
  const [selectedAmbulance, setSelectedAmbulance] = useState(null);
  const [emergencyAlerts, setEmergencyAlerts] = useState([]);
  const [trafficData, setTrafficData] = useState({ congestion: 65, incidents: 3, avgSpeed: 28 });
  const [currentTime, setCurrentTime] = useState(new Date());
  const [systemStatus, setSystemStatus] = useState("operational");
  const [mapLayers, setMapLayers] = useState({
    traffic: true,
    hospitals: true,
    routes: true
  });
  const mapRef = useRef(null);
  const [mapMarkers, setMapMarkers] = useState({
    hospitals: [],
    ambulances: [],
    emergencies: [],
    routes: []
  });
  const [activeEmergencies, setActiveEmergencies] = useState([
    {
      id: "EMG001",
      type: "cardiac arrest",
      priority: "critical",
      location: "T Nagar Commercial Complex",
      coords: { lat: 13.0418, lng: 80.2341 },
      reportedAt: new Date(Date.now() - 300000), // 5 minutes ago
      assignedAmbulance: "AMB002",
      status: "dispatched",
      aiClassification: {
        confidence: 0.94,
        symptoms: ["chest pain", "shortness of breath", "unconscious"],
        riskScore: 0.89,
        recommendedHospital: "Apollo Hospital",
        estimatedSeverity: "high"
      },
      source: "mobile_app",
      patientInfo: { age: 65, gender: "male", vitals: { heartRate: 45, bp: "80/40" } }
    },
    {
      id: "EMG002",
      type: "road accident",
      priority: "high",
      location: "Anna Nagar Metro Station",
      coords: { lat: 13.0850, lng: 80.2101 },
      reportedAt: new Date(Date.now() - 180000), // 3 minutes ago
      assignedAmbulance: "AMB003",
      status: "en-route",
      aiClassification: {
        confidence: 0.97,
        symptoms: ["multiple injuries", "bleeding", "trauma"],
        riskScore: 0.85,
        recommendedHospital: "Stanley Medical College",
        estimatedSeverity: "moderate"
      },
      source: "iot_sensor",
      patientInfo: { age: 28, gender: "female", vitals: { heartRate: 110, bp: "90/60" } }
    }
  ]);

  // AI System State
  const [aiSystem, setAiSystem] = useState({
    status: "active",
    modelsLoaded: true,
    lastUpdate: new Date(),
    accuracy: 0.94,
    processedCalls: 1247,
    falsePositives: 23,
    avgResponseTime: 2.8
  });

  // Voice Interface State
  const [voiceSystem, setVoiceSystem] = useState({
    isListening: false,
    supportedLanguages: ['English', 'Tamil', 'Hindi', 'Telugu', 'Malayalam', 'Kannada'],
    currentLanguage: 'English',
    whisperModel: 'whisper-large-v3',
    accuracy: 0.96,
    processedVoiceCalls: 342
  });

  // Heatmap Data
  const [heatmapData, setHeatmapData] = useState({
    emergencyHotspots: [
      { area: 'T Nagar', risk: 0.89, incidents: 45, ambulancesDeployed: 3 },
      { area: 'Anna Nagar', risk: 0.76, incidents: 32, ambulancesDeployed: 2 },
      { area: 'Velachery', risk: 0.82, incidents: 38, ambulancesDeployed: 2 },
      { area: 'Adyar', risk: 0.65, incidents: 28, ambulancesDeployed: 2 },
      { area: 'Guindy', risk: 0.71, incidents: 31, ambulancesDeployed: 1 }
    ],
    lastUpdated: new Date(),
    predictionAccuracy: 0.87
  });

  // System Architecture Status
  const [systemArchitecture, setSystemArchitecture] = useState({
    microservices: [
      { name: 'Emergency Classification Service', status: 'active', uptime: '99.9%' },
      { name: 'Dispatch Optimization Service', status: 'active', uptime: '99.8%' },
      { name: 'Voice Processing Service', status: 'active', uptime: '99.7%' },
      { name: 'Heatmap Analytics Service', status: 'active', uptime: '99.9%' },
      { name: 'Hospital Integration API', status: 'active', uptime: '99.6%' },
      { name: 'Smart City Gateway', status: 'active', uptime: '99.8%' }
    ],
    apiCalls: 15420,
    regions: ['Chennai', 'Bangalore', 'Hyderabad'],
    scalingStatus: 'auto-scaling enabled'
  });

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Dark mode toggle functionality
  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
  };

  // Apply theme to document
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', darkMode ? 'dark' : 'light');
  }, [darkMode]);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update traffic data
      setTrafficData(prev => ({
        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),
        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),
        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))
      }));

      // Simulate ambulance position updates
      setAmbulances(prev => prev.map(amb => ({
        ...amb,
        location: {
          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,
          lng: amb.location.lng + (Math.random() - 0.5) * 0.001
        }
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const initMap = () => {
      const newMap = new window.google.maps.Map(document.getElementById("emergency-map"), {
        zoom: 12,
        center: { lat: 13.0827, lng: 80.2707 },
        mapTypeControl: false,
        streetViewControl: false,
        fullscreenControl: false,
        styles: [
          { elementType: "geometry", stylers: [{ color: "#f5f5f5" }] },
          { elementType: "labels.text.fill", stylers: [{ color: "#616161" }] },
          { elementType: "labels.text.stroke", stylers: [{ color: "#f5f5f5" }] },
          { featureType: "road", elementType: "geometry", stylers: [{ color: "#ffffff" }] },
          { featureType: "road", elementType: "geometry.stroke", stylers: [{ color: "#e0e0e0" }] },
          { featureType: "road.highway", elementType: "geometry", stylers: [{ color: "#ffd54f" }] },
          { featureType: "water", elementType: "geometry", stylers: [{ color: "#c9c9c9" }] },
          { featureType: "poi", elementType: "geometry", stylers: [{ color: "#eeeeee" }] }
        ]
      });

      const trafficLayer = new window.google.maps.TrafficLayer();
      trafficLayer.setMap(newMap);

      // Add hospital markers
      hospitals.forEach(hospital => {
        const hospitalMarker = new window.google.maps.Marker({
          position: hospital.coords,
          map: newMap,
          title: hospital.name,
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" fill="#dc2626"/>
                <path d="M12 6v12M6 12h12" stroke="white" stroke-width="2" stroke-linecap="round"/>
              </svg>
            `),
            scaledSize: new window.google.maps.Size(24, 24)
          }
        });

        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 8px; font-family: Inter, sans-serif;">
              <h4 style="margin: 0 0 8px 0; color: #1e293b;">${hospital.name}</h4>
              <p style="margin: 0; color: #64748b; font-size: 13px;">Capacity: ${hospital.capacity}%</p>
              <p style="margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;">✓ Emergency Services Available</p>
            </div>
          `
        });

        hospitalMarker.addListener('click', () => {
          infoWindow.open(newMap, hospitalMarker);
        });
      });

      // Add ambulance markers with animation
      const ambulanceMarkers = [];
      ambulances.forEach(ambulance => {
        const ambulanceMarker = new window.google.maps.Marker({
          position: ambulance.location,
          map: newMap,
          title: `${ambulance.id} - ${ambulance.status}`,
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2" y="12" width="28" height="12" rx="2" fill="${getStatusColor(ambulance.status)}"/>
                <rect x="6" y="8" width="12" height="8" rx="1" fill="${getStatusColor(ambulance.status)}"/>
                <circle cx="8" cy="26" r="3" fill="#374151"/>
                <circle cx="24" cy="26" r="3" fill="#374151"/>
                <path d="M14 16h4M16 14v4" stroke="white" stroke-width="2" stroke-linecap="round"/>
              </svg>
            `),
            scaledSize: new window.google.maps.Size(32, 32)
          }
        });

        const ambulanceInfo = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 8px; font-family: Inter, sans-serif;">
              <h4 style="margin: 0 0 8px 0; color: #1e293b;">${ambulance.id}</h4>
              <p style="margin: 0; color: #64748b; font-size: 13px;">Status: <span style="color: ${getStatusColor(ambulance.status)}; font-weight: 600;">${ambulance.status.toUpperCase()}</span></p>
              <p style="margin: 4px 0 0 0; color: #64748b; font-size: 13px;">Driver: ${ambulance.driver}</p>
              <p style="margin: 2px 0 0 0; color: #64748b; font-size: 13px;">Medic: ${ambulance.medic}</p>
              <p style="margin: 4px 0 0 0; color: #64748b; font-size: 13px;">Fuel: ${ambulance.fuel}%</p>
              ${ambulance.emergency ? `<p style="margin: 4px 0 0 0; color: #dc2626; font-size: 12px; font-weight: 600;">🚨 ${ambulance.emergency.type} - ETA: ${ambulance.emergency.eta}</p>` : ''}
            </div>
          `
        });

        ambulanceMarker.addListener('click', () => {
          ambulanceInfo.open(newMap, ambulanceMarker);
        });

        ambulanceMarkers.push({ marker: ambulanceMarker, ambulance: ambulance });
      });

      // Add emergency incident markers
      activeEmergencies.forEach(emergency => {
        const emergencyMarker = new window.google.maps.Marker({
          position: emergency.coords,
          map: newMap,
          title: `Emergency: ${emergency.type}`,
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="14" cy="14" r="12" fill="${getPriorityColor(emergency.priority)}" opacity="0.8"/>
                <circle cx="14" cy="14" r="8" fill="${getPriorityColor(emergency.priority)}"/>
                <text x="14" y="18" text-anchor="middle" fill="white" font-size="12" font-weight="bold">!</text>
              </svg>
            `),
            scaledSize: new window.google.maps.Size(28, 28)
          },
          animation: window.google.maps.Animation.BOUNCE
        });

        const emergencyInfo = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 8px; font-family: Inter, sans-serif;">
              <h4 style="margin: 0 0 8px 0; color: #1e293b;">${emergency.id}</h4>
              <p style="margin: 0; color: #dc2626; font-size: 14px; font-weight: 600;">${emergency.type.toUpperCase()}</p>
              <p style="margin: 4px 0; color: #64748b; font-size: 13px;">Priority: <span style="color: ${getPriorityColor(emergency.priority)}; font-weight: 600;">${emergency.priority.toUpperCase()}</span></p>
              <p style="margin: 4px 0; color: #64748b; font-size: 13px;">Location: ${emergency.location}</p>
              <p style="margin: 4px 0; color: #64748b; font-size: 13px;">Reported: ${Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago</p>
              ${emergency.assignedAmbulance ? `<p style="margin: 4px 0 0 0; color: #059669; font-size: 12px; font-weight: 600;">🚑 ${emergency.assignedAmbulance} dispatched</p>` : ''}
            </div>
          `
        });

        emergencyMarker.addListener('click', () => {
          emergencyInfo.open(newMap, emergencyMarker);
        });

        // Draw route from assigned ambulance to emergency if available
        if (emergency.assignedAmbulance) {
          const assignedAmb = ambulances.find(amb => amb.id === emergency.assignedAmbulance);
          if (assignedAmb) {
            const routePath = new window.google.maps.Polyline({
              path: [assignedAmb.location, emergency.coords],
              geodesic: true,
              strokeColor: getPriorityColor(emergency.priority),
              strokeOpacity: 0.8,
              strokeWeight: 4,
              icons: [{
                icon: {
                  path: window.google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
                  scale: 3,
                  fillColor: getPriorityColor(emergency.priority),
                  fillOpacity: 1,
                  strokeWeight: 1,
                  strokeColor: '#ffffff'
                },
                offset: '50%'
              }]
            });
            routePath.setMap(newMap);
          }
        }
      });

      // Animate ambulances with realistic movement
      setInterval(() => {
        ambulanceMarkers.forEach(({ marker, ambulance }) => {
          if (ambulance.status === 'dispatched' || ambulance.status === 'en-route') {
            const currentPos = marker.getPosition();
            // Simulate movement towards emergency location
            const emergency = activeEmergencies.find(e => e.assignedAmbulance === ambulance.id);
            if (emergency) {
              const targetLat = emergency.coords.lat;
              const targetLng = emergency.coords.lng;
              const currentLat = currentPos.lat();
              const currentLng = currentPos.lng();

              // Move 10% closer to target each update
              const newLat = currentLat + (targetLat - currentLat) * 0.1;
              const newLng = currentLng + (targetLng - currentLng) * 0.1;

              marker.setPosition({ lat: newLat, lng: newLng });
            } else {
              // Random patrol movement for available ambulances
              const newLat = currentPos.lat() + (Math.random() - 0.5) * 0.001;
              const newLng = currentPos.lng() + (Math.random() - 0.5) * 0.001;
              marker.setPosition({ lat: newLat, lng: newLng });
            }
          }
        });
      }, 2000);

      // Add traffic congestion simulation circles
      const trafficAreas = [
        { center: { lat: 13.0827, lng: 80.2707 }, radius: 500, level: 'high' },
        { center: { lat: 13.0500, lng: 80.2824 }, radius: 300, level: 'medium' },
        { center: { lat: 13.0418, lng: 80.2341 }, radius: 400, level: 'high' },
        { center: { lat: 12.9756, lng: 80.2207 }, radius: 200, level: 'low' }
      ];

      trafficAreas.forEach(area => {
        const trafficCircle = new window.google.maps.Circle({
          strokeColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',
          strokeOpacity: 0.6,
          strokeWeight: 2,
          fillColor: area.level === 'high' ? '#dc2626' : area.level === 'medium' ? '#d97706' : '#059669',
          fillOpacity: 0.15,
          map: newMap,
          center: area.center,
          radius: area.radius
        });
      });

      setMap(newMap);
      mapRef.current = newMap;
    };

    const loadGoogleMapsScript = () => {
      if (!window.google || !window.google.maps) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);
        script.onload = initMap;
      } else {
        initMap();
      }
    };

    loadGoogleMapsScript();
  }, []);

  // Utility functions for ambulance management
  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#00ff88';
      case 'dispatched': return '#ffaa00';
      case 'en-route': return '#ff4444';
      case 'maintenance': return '#666666';
      default: return '#00e5ff';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#ff0044';
      case 'high': return '#ff6600';
      case 'medium': return '#ffaa00';
      case 'low': return '#00ff88';
      default: return '#00e5ff';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  // AI-Enhanced Emergency Classification
  const classifyEmergency = (symptoms, vitals, context) => {
    // Simulate AI classification algorithm
    const emergencyTypes = {
      'cardiac arrest': { keywords: ['chest pain', 'heart', 'cardiac', 'unconscious'], severity: 0.95 },
      'stroke': { keywords: ['speech', 'paralysis', 'face', 'weakness'], severity: 0.90 },
      'road accident': { keywords: ['accident', 'collision', 'trauma', 'bleeding'], severity: 0.85 },
      'respiratory distress': { keywords: ['breathing', 'asthma', 'choking'], severity: 0.80 },
      'overdose': { keywords: ['drugs', 'poison', 'overdose', 'unconscious'], severity: 0.88 }
    };

    let bestMatch = { type: 'general emergency', confidence: 0.5, severity: 0.6 };

    for (const [type, data] of Object.entries(emergencyTypes)) {
      const matches = data.keywords.filter(keyword =>
        symptoms.some(symptom => symptom.toLowerCase().includes(keyword))
      ).length;

      const confidence = (matches / data.keywords.length) * 0.7 + Math.random() * 0.3;

      if (confidence > bestMatch.confidence) {
        bestMatch = { type, confidence, severity: data.severity };
      }
    }

    return bestMatch;
  };

  // AI-Driven Smart Ambulance Selection
  const findOptimalAmbulance = (emergency) => {
    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');
    if (availableAmbulances.length === 0) return null;

    return availableAmbulances.map(ambulance => {
      const distance = calculateDistance(
        emergency.coords.lat, emergency.coords.lng,
        ambulance.location.lat, ambulance.location.lng
      );

      // AI scoring factors
      const distanceScore = Math.max(0, 1 - (distance / 10)); // Normalize to 0-1
      const specializationScore = ambulance.specialization === emergency.type.split(' ')[0] ? 1 : 0.7;
      const equipmentScore = emergency.aiClassification?.riskScore > 0.8 ?
        (ambulance.equipment.length / 5) : 0.8;
      const responseScore = Math.max(0, 1 - (ambulance.responseTime / 5));
      const fuelScore = ambulance.fuel > 30 ? 1 : 0.5;

      // Weighted AI score
      const totalScore = (
        distanceScore * 0.3 +
        specializationScore * 0.25 +
        equipmentScore * 0.2 +
        responseScore * 0.15 +
        fuelScore * 0.1
      ) * ambulance.aiScore;

      return { ambulance, score: totalScore, distance, eta: distance * 2 };
    }).sort((a, b) => b.score - a.score)[0];
  };

  // Predictive Hospital Assignment using LSTM-like logic
  const selectOptimalHospital = (emergency, ambulance) => {
    const hospitalScores = hospitals.map(hospital => {
      const distance = calculateDistance(
        emergency.coords.lat, emergency.coords.lng,
        hospital.coords.lat, hospital.coords.lng
      );

      // Simulate LSTM prediction factors
      const capacityScore = hospital.capacity / 100;
      const distanceScore = Math.max(0, 1 - (distance / 15));
      const specializationScore = hospital.emergency ? 1 : 0.6;
      const trafficFactor = trafficData.congestion > 70 ? 0.8 : 1;

      // Historical pattern simulation (LSTM would use real historical data)
      const timeOfDay = new Date().getHours();
      const historicalScore = timeOfDay >= 8 && timeOfDay <= 18 ? 0.9 : 1; // Rush hour penalty

      const totalScore = (
        capacityScore * 0.4 +
        distanceScore * 0.3 +
        specializationScore * 0.2 +
        historicalScore * 0.1
      ) * trafficFactor;

      return { hospital, score: totalScore, distance, eta: distance * 2.5 };
    }).sort((a, b) => b.score - a.score)[0];

    return hospitalScores;
  };

  // Enhanced AI-Driven Dispatch
  const dispatchAmbulance = (emergencyId, ambulanceId = null) => {
    const emergency = activeEmergencies.find(e => e.id === emergencyId);
    if (!emergency) return;

    let selectedAmbulance;

    if (ambulanceId) {
      // Manual dispatch
      selectedAmbulance = ambulances.find(a => a.id === ambulanceId);
    } else {
      // AI-driven automatic dispatch
      const optimal = findOptimalAmbulance(emergency);
      selectedAmbulance = optimal?.ambulance;
    }

    if (!selectedAmbulance) return;

    // Get optimal hospital using AI
    const optimalHospital = selectOptimalHospital(emergency, selectedAmbulance);

    setAmbulances(prev => prev.map(amb =>
      amb.id === selectedAmbulance.id
        ? {
            ...amb,
            status: 'dispatched',
            emergency: {
              ...emergency,
              assignedHospital: optimalHospital.hospital.name,
              eta: `${Math.round(optimalHospital.eta)} min`,
              aiConfidence: emergency.aiClassification.confidence
            }
          }
        : amb
    ));

    setActiveEmergencies(prev => prev.map(e =>
      e.id === emergencyId
        ? {
            ...e,
            assignedAmbulance: selectedAmbulance.id,
            status: 'dispatched',
            assignedHospital: optimalHospital.hospital.name,
            estimatedArrival: new Date(Date.now() + optimalHospital.eta * 60000)
          }
        : e
    ));

    // Update AI system stats
    setAiSystem(prev => ({
      ...prev,
      processedCalls: prev.processedCalls + 1,
      lastUpdate: new Date()
    }));
  };

  // Map control functions
  const toggleMapLayer = (layerName) => {
    setMapLayers(prev => ({
      ...prev,
      [layerName]: !prev[layerName]
    }));

    // Here you would typically show/hide the actual map layers
    // For demo purposes, we'll just update the button states
  };

  // AI-Enhanced Emergency Call Simulation
  const simulateEmergencyCall = () => {
    const emergencyScenarios = [
      {
        symptoms: ['chest pain', 'shortness of breath', 'sweating'],
        vitals: { heartRate: 45 + Math.random() * 20, bp: '80/40' },
        context: 'home',
        age: 55 + Math.random() * 20
      },
      {
        symptoms: ['bleeding', 'trauma', 'unconscious'],
        vitals: { heartRate: 100 + Math.random() * 30, bp: '90/60' },
        context: 'road',
        age: 25 + Math.random() * 30
      },
      {
        symptoms: ['difficulty speaking', 'weakness', 'confusion'],
        vitals: { heartRate: 80 + Math.random() * 20, bp: '160/90' },
        context: 'office',
        age: 60 + Math.random() * 15
      }
    ];

    const locations = ['Anna Nagar', 'T Nagar', 'Velachery', 'Adyar', 'Guindy'];
    const sources = ['mobile_app', 'phone_call', 'iot_sensor', 'smart_watch'];

    const scenario = emergencyScenarios[Math.floor(Math.random() * emergencyScenarios.length)];
    const aiClassification = classifyEmergency(scenario.symptoms, scenario.vitals, scenario.context);

    const newEmergency = {
      id: `EMG${String(Date.now()).slice(-3)}`,
      type: aiClassification.type,
      priority: aiClassification.severity > 0.9 ? 'critical' :
                aiClassification.severity > 0.8 ? 'high' : 'medium',
      location: locations[Math.floor(Math.random() * locations.length)],
      coords: {
        lat: 13.0827 + (Math.random() - 0.5) * 0.1,
        lng: 80.2707 + (Math.random() - 0.5) * 0.1
      },
      reportedAt: new Date(),
      assignedAmbulance: null,
      status: 'pending',
      aiClassification: {
        confidence: aiClassification.confidence,
        symptoms: scenario.symptoms,
        riskScore: aiClassification.severity,
        recommendedHospital: hospitals[Math.floor(Math.random() * hospitals.length)].name,
        estimatedSeverity: aiClassification.severity > 0.9 ? 'critical' : 'moderate'
      },
      source: sources[Math.floor(Math.random() * sources.length)],
      patientInfo: {
        age: Math.round(scenario.age),
        gender: Math.random() > 0.5 ? 'male' : 'female',
        vitals: scenario.vitals
      }
    };

    setActiveEmergencies(prev => [...prev, newEmergency]);

    // Auto-dispatch if AI confidence is high
    if (aiClassification.confidence > 0.85) {
      setTimeout(() => {
        dispatchAmbulance(newEmergency.id);
      }, 2000);
    }
  };


  return (
    <div className="dispatch-center">
      {/* Header with system status and navigation */}
      <header className="dispatch-header">
        <div className="header-left">
          <h1 className="system-title">
            <span className="pulse-dot"></span>
            Emergency Medical Services - Dispatch Center
          </h1>
          <div className="system-status">
            <span className={`status-indicator ${systemStatus}`}></span>
            System Status: {systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)}
          </div>
        </div>

        <div className="header-center">
          <nav className="main-navigation">
            <button
              className={`nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`}
              onClick={() => setCurrentPage('dashboard')}
            >
              🏠 Dashboard
            </button>
            <button
              className={`nav-btn ${currentPage === 'transport-optimizer' ? 'active' : ''}`}
              onClick={() => setCurrentPage('transport-optimizer')}
            >
              🚑 Transport Optimizer
            </button>
            <button
              className={`nav-btn ${currentPage === 'traffic-hijacking' ? 'active' : ''}`}
              onClick={() => setCurrentPage('traffic-hijacking')}
            >
              🚦 Traffic Signal AI
            </button>
            <button
              className={`nav-btn ${currentPage === 'mobile-app' ? 'active' : ''}`}
              onClick={() => setCurrentPage('mobile-app')}
            >
              📱 Mobile App
            </button>
            <button
              className={`nav-btn ${currentPage === 'storyboard' ? 'active' : ''}`}
              onClick={() => setCurrentPage('storyboard')}
            >
              📋 Storyboard
            </button>
          </nav>
        </div>

        <div className="header-right">
          <div className="current-time">
            <div className="time-display">{formatTime(currentTime)}</div>
            <div className="date-display">{currentTime.toLocaleDateString()}</div>
          </div>
          <button className="theme-toggle" onClick={toggleDarkMode}>
            <span className="theme-toggle-icon sun">☀️</span>
            <span className="theme-toggle-icon moon">🌙</span>
          </button>
        </div>
      </header>

      {/* Conditional Page Rendering */}
      {currentPage === 'dashboard' && (
        <div className="dashboard-grid">

        {/* Main Map Section - 75% width */}
        <div className="main-map-section">
          <div className="panel main-map-panel">
            <div className="panel-header">
              <h3>🗺️ Live Emergency Dispatch Map</h3>
              <div className="map-controls">
                <button
                  className={`map-btn ${mapLayers.traffic ? 'active' : ''}`}
                  onClick={() => toggleMapLayer('traffic')}
                >
                  Traffic Layer
                </button>
                <button
                  className={`map-btn ${mapLayers.hospitals ? 'active' : ''}`}
                  onClick={() => toggleMapLayer('hospitals')}
                >
                  Hospitals
                </button>
                <button
                  className={`map-btn ${mapLayers.routes ? 'active' : ''}`}
                  onClick={() => toggleMapLayer('routes')}
                >
                  Active Routes
                </button>
                <button
                  className="map-btn"
                  onClick={simulateEmergencyCall}
                  style={{ marginLeft: '12px', backgroundColor: '#dc2626', color: 'white', border: '1px solid #dc2626' }}
                >
                  + Simulate Emergency
                </button>
              </div>
            </div>

            <div style={{ position: 'relative', height: 'calc(100% - 80px)' }}>
              <div id="emergency-map" className="emergency-map"></div>

              {/* Fleet Status Overlay on Map */}
              <div className="fleet-status-overlay">
                <div className="fleet-summary-overlay">
                  <span className="available-count">
                    {ambulances.filter(a => a.status === 'available').length} Available
                  </span>
                  <span className="active-count">
                    {ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length} Deployed
                  </span>
                  <span style={{ color: '#dc2626', fontWeight: '600' }}>
                    {activeEmergencies.length} Active Emergencies
                  </span>
                </div>
              </div>
            </div>

            {/* Map Legend */}
            <div className="map-legend">
              <div className="legend-item">
                <span className="legend-icon" style={{ backgroundColor: '#10b981' }}>🚑</span>
                <span>Available</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon" style={{ backgroundColor: '#f59e0b' }}>🚑</span>
                <span>Dispatched</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon" style={{ backgroundColor: '#ef4444' }}>🚑</span>
                <span>En Route</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon" style={{ backgroundColor: '#dc2626' }}>🏥</span>
                <span>Hospital</span>
              </div>
              <div className="legend-item">
                <span className="legend-icon" style={{ backgroundColor: '#dc2626' }}>!</span>
                <span>Emergency</span>
              </div>
            </div>
          </div>

          {/* Advanced Features Section - Below Map */}
          <div className="advanced-features-section">

            {/* Multilingual Voice Interface */}
            <div className="panel voice-interface-panel">
              <div className="panel-header">
                <h3>🎤 Multilingual Voice Interface</h3>
                <div className="voice-status">
                  <span className={`status-indicator ${voiceSystem.isListening ? 'listening' : 'ready'}`}></span>
                  <span>{voiceSystem.isListening ? 'Listening...' : 'Ready'}</span>
                </div>
              </div>

              <div className="voice-interface-content">
                <div className="voice-stats">
                  <div className="voice-stat">
                    <div className="stat-value">{voiceSystem.supportedLanguages.length}</div>
                    <div className="stat-label">Languages Supported</div>
                  </div>
                  <div className="voice-stat">
                    <div className="stat-value">{(voiceSystem.accuracy * 100).toFixed(1)}%</div>
                    <div className="stat-label">Voice Recognition Accuracy</div>
                  </div>
                  <div className="voice-stat">
                    <div className="stat-value">{voiceSystem.processedVoiceCalls}</div>
                    <div className="stat-label">Voice Calls Processed</div>
                  </div>
                </div>

                <div className="voice-features">
                  <div className="feature-grid">
                    <div className="voice-feature">
                      <div className="feature-icon">🗣️</div>
                      <div className="feature-content">
                        <h4>Whisper ASR Integration</h4>
                        <p>Advanced speech recognition using OpenAI Whisper model for accurate transcription across multiple languages and accents.</p>
                        <div className="tech-badge">Model: {voiceSystem.whisperModel}</div>
                      </div>
                    </div>

                    <div className="voice-feature">
                      <div className="feature-icon">🌐</div>
                      <div className="feature-content">
                        <h4>Multi-Language Support</h4>
                        <p>Supports {voiceSystem.supportedLanguages.join(', ')} with real-time language detection and emergency classification.</p>
                        <div className="language-pills">
                          {voiceSystem.supportedLanguages.slice(0, 4).map(lang => (
                            <span key={lang} className="language-pill">{lang}</span>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="voice-feature">
                      <div className="feature-icon">🧠</div>
                      <div className="feature-content">
                        <h4>NLP Emergency Classification</h4>
                        <p>Advanced natural language processing to understand emergency context, severity, and location from voice descriptions.</p>
                        <div className="tech-badge">Accuracy: {(voiceSystem.accuracy * 100).toFixed(1)}%</div>
                      </div>
                    </div>

                    <div className="voice-feature">
                      <div className="feature-icon">♿</div>
                      <div className="feature-content">
                        <h4>Accessibility & Inclusion</h4>
                        <p>Removes literacy and language barriers, enabling emergency access for all community members regardless of education level.</p>
                        <div className="accessibility-badge">Universal Access</div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="voice-demo">
                  <button
                    className="voice-demo-btn"
                    onClick={() => setVoiceSystem(prev => ({ ...prev, isListening: !prev.isListening }))}
                  >
                    {voiceSystem.isListening ? '🛑 Stop Listening' : '🎤 Start Voice Demo'}
                  </button>
                  <div className="voice-demo-text">
                    {voiceSystem.isListening ?
                      'Listening for emergency... Say "Help, I need an ambulance at Marina Beach"' :
                      'Click to test multilingual voice emergency reporting'
                    }
                  </div>
                </div>
              </div>
            </div>

            {/* Heatmap-Based Resource Placement */}
            <div className="panel heatmap-panel">
              <div className="panel-header">
                <h3>🔥 Predictive Heatmap Analytics</h3>
                <div className="heatmap-accuracy">
                  Prediction Accuracy: <span className="accuracy-value">{(heatmapData.predictionAccuracy * 100).toFixed(1)}%</span>
                </div>
              </div>

              <div className="heatmap-content">
                <div className="heatmap-overview">
                  <div className="heatmap-description">
                    <h4>🎯 Smart Resource Deployment</h4>
                    <p>AI-powered predictive analytics analyze historical emergency data, traffic patterns, population density, and temporal factors to optimize ambulance placement across the city.</p>
                  </div>
                </div>

                <div className="hotspots-grid">
                  <h4>📍 Current Emergency Hotspots</h4>
                  <div className="hotspots-list">
                    {heatmapData.emergencyHotspots.map((hotspot, index) => (
                      <div key={index} className="hotspot-card">
                        <div className="hotspot-header">
                          <span className="hotspot-name">{hotspot.area}</span>
                          <span className={`risk-level ${hotspot.risk > 0.8 ? 'high' : hotspot.risk > 0.7 ? 'medium' : 'low'}`}>
                            {hotspot.risk > 0.8 ? 'HIGH RISK' : hotspot.risk > 0.7 ? 'MEDIUM RISK' : 'LOW RISK'}
                          </span>
                        </div>
                        <div className="hotspot-stats">
                          <div className="hotspot-stat">
                            <span className="stat-label">Risk Score:</span>
                            <span className="stat-value">{(hotspot.risk * 100).toFixed(0)}%</span>
                          </div>
                          <div className="hotspot-stat">
                            <span className="stat-label">Incidents (30d):</span>
                            <span className="stat-value">{hotspot.incidents}</span>
                          </div>
                          <div className="hotspot-stat">
                            <span className="stat-label">Deployed Units:</span>
                            <span className="stat-value">{hotspot.ambulancesDeployed} 🚑</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="heatmap-features">
                  <div className="heatmap-feature-grid">
                    <div className="heatmap-feature">
                      <div className="feature-icon">📊</div>
                      <div>
                        <h5>Historical Data Analysis</h5>
                        <p>Analyzes 5+ years of emergency data to identify patterns and trends</p>
                      </div>
                    </div>
                    <div className="heatmap-feature">
                      <div className="feature-icon">⏰</div>
                      <div>
                        <h5>Temporal Predictions</h5>
                        <p>Considers time of day, day of week, and seasonal variations</p>
                      </div>
                    </div>
                    <div className="heatmap-feature">
                      <div className="feature-icon">🏙️</div>
                      <div>
                        <h5>Urban Analytics</h5>
                        <p>Factors in population density, traffic patterns, and event schedules</p>
                      </div>
                    </div>
                    <div className="heatmap-feature">
                      <div className="feature-icon">🎯</div>
                      <div>
                        <h5>Dynamic Redeployment</h5>
                        <p>Real-time ambulance repositioning based on changing risk patterns</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modular and Scalable Architecture */}
            <div className="panel architecture-panel">
              <div className="panel-header">
                <h3>🏗️ Modular & Scalable Architecture</h3>
                <div className="architecture-status">
                  <span className="status-indicator operational"></span>
                  <span>{systemArchitecture.scalingStatus}</span>
                </div>
              </div>

              <div className="architecture-content">
                <div className="architecture-overview">
                  <div className="architecture-description">
                    <h4>🔧 Microservices Architecture</h4>
                    <p>Built on a distributed microservices architecture with open APIs, enabling seamless integration into smart city ecosystems and horizontal scaling across multiple regions.</p>
                  </div>

                  <div className="architecture-stats">
                    <div className="arch-stat">
                      <div className="stat-value">{systemArchitecture.apiCalls.toLocaleString()}</div>
                      <div className="stat-label">API Calls Today</div>
                    </div>
                    <div className="arch-stat">
                      <div className="stat-value">{systemArchitecture.regions.length}</div>
                      <div className="stat-label">Active Regions</div>
                    </div>
                    <div className="arch-stat">
                      <div className="stat-value">{systemArchitecture.microservices.length}</div>
                      <div className="stat-label">Microservices</div>
                    </div>
                  </div>
                </div>

                <div className="microservices-grid">
                  <h4>⚙️ System Components</h4>
                  <div className="microservices-list">
                    {systemArchitecture.microservices.map((service, index) => (
                      <div key={index} className="microservice-card">
                        <div className="service-header">
                          <span className="service-name">{service.name}</span>
                          <span className={`service-status ${service.status}`}>
                            <span className="status-dot"></span>
                            {service.status.toUpperCase()}
                          </span>
                        </div>
                        <div className="service-uptime">
                          Uptime: <span className="uptime-value">{service.uptime}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="architecture-features">
                  <div className="arch-feature-grid">
                    <div className="arch-feature">
                      <div className="feature-icon">🔗</div>
                      <div>
                        <h5>Open API Integration</h5>
                        <p>RESTful APIs and GraphQL endpoints for seamless third-party integration</p>
                      </div>
                    </div>
                    <div className="arch-feature">
                      <div className="feature-icon">🌐</div>
                      <div>
                        <h5>Smart City Ready</h5>
                        <p>Compatible with smart city platforms and IoT infrastructure</p>
                      </div>
                    </div>
                    <div className="arch-feature">
                      <div className="feature-icon">📈</div>
                      <div>
                        <h5>Auto-Scaling</h5>
                        <p>Kubernetes-based auto-scaling for handling varying emergency loads</p>
                      </div>
                    </div>
                    <div className="arch-feature">
                      <div className="feature-icon">🔒</div>
                      <div>
                        <h5>Enterprise Security</h5>
                        <p>End-to-end encryption, HIPAA compliance, and audit trails</p>
                      </div>
                    </div>
                    <div className="arch-feature">
                      <div className="feature-icon">🌍</div>
                      <div>
                        <h5>Multi-Region Support</h5>
                        <p>Currently deployed in {systemArchitecture.regions.join(', ')} with expansion capabilities</p>
                      </div>
                    </div>
                    <div className="arch-feature">
                      <div className="feature-icon">⚡</div>
                      <div>
                        <h5>Real-Time Processing</h5>
                        <p>Event-driven architecture with sub-second response times</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="integration-showcase">
                  <h4>🔌 Integration Capabilities</h4>
                  <div className="integration-grid">
                    <div className="integration-item">
                      <span className="integration-icon">🏥</span>
                      <span>Hospital Management Systems</span>
                    </div>
                    <div className="integration-item">
                      <span className="integration-icon">🚦</span>
                      <span>Traffic Management Systems</span>
                    </div>
                    <div className="integration-item">
                      <span className="integration-icon">📱</span>
                      <span>Mobile Emergency Apps</span>
                    </div>
                    <div className="integration-item">
                      <span className="integration-icon">🏢</span>
                      <span>Government Emergency Services</span>
                    </div>
                    <div className="integration-item">
                      <span className="integration-icon">🌐</span>
                      <span>IoT Sensor Networks</span>
                    </div>
                    <div className="integration-item">
                      <span className="integration-icon">📊</span>
                      <span>Analytics & BI Platforms</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>

        {/* Analytics Sidebar - 25% width */}
        <div className="analytics-sidebar">

          {/* AI System Dashboard */}
          <div className="panel ai-panel">
            <div className="panel-header">
              <h3>🤖 AI Dispatch System</h3>
              <span className={`ai-status ${aiSystem.status}`}>
                {aiSystem.status.toUpperCase()}
              </span>
            </div>
            <div className="ai-metrics">
              <div className="ai-metric">
                <div className="metric-value">{(aiSystem.accuracy * 100).toFixed(1)}%</div>
                <div className="metric-label">AI Accuracy</div>
              </div>
              <div className="ai-metric">
                <div className="metric-value">{aiSystem.processedCalls}</div>
                <div className="metric-label">Calls Processed</div>
              </div>
              <div className="ai-metric">
                <div className="metric-value">{aiSystem.avgResponseTime}s</div>
                <div className="metric-label">Avg Response</div>
              </div>
              <div className="ai-metric">
                <div className="metric-value">{aiSystem.falsePositives}</div>
                <div className="metric-label">False Positives</div>
              </div>
            </div>
            <div className="ai-features">
              <div className="feature-item">
                <span className="feature-icon">🧠</span>
                <div>
                  <div className="feature-title">Emergency Classification</div>
                  <div className="feature-desc">Real-time symptom analysis</div>
                </div>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🎯</span>
                <div>
                  <div className="feature-title">Smart Dispatch</div>
                  <div className="feature-desc">Optimal ambulance selection</div>
                </div>
              </div>
              <div className="feature-item">
                <span className="feature-icon">🏥</span>
                <div>
                  <div className="feature-title">Hospital Prediction</div>
                  <div className="feature-desc">LSTM-based routing</div>
                </div>
              </div>
            </div>
          </div>

          {/* Traffic Analytics Panel */}
          <div className="panel analytics-panel">
            <div className="panel-header">
              <h3>📊 Traffic Conditions</h3>
            </div>
            <div className="analytics-grid">
              <div className="metric-card">
                <div className="metric-value">{trafficData.congestion}%</div>
                <div className="metric-label">Traffic Congestion</div>
                <div className={`metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`}>
                  {trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'}
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{trafficData.incidents}</div>
                <div className="metric-label">Active Incidents</div>
                <div className={`metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`}>
                  {trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'}
                </div>
              </div>
              <div className="metric-card">
                <div className="metric-value">{trafficData.avgSpeed} km/h</div>
                <div className="metric-label">Avg Speed</div>
                <div className={`metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`}>
                  {trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'}
                </div>
              </div>
            </div>
          </div>

          {/* Emergency Alerts Panel */}
          <div className="panel emergency-panel compact">
            <div className="panel-header">
              <h3>🚨 Active Emergency Calls</h3>
              <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
                <span className="emergency-count">{activeEmergencies.length}</span>
                <button
                  onClick={simulateEmergencyCall}
                  style={{
                    background: '#dc2626',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    padding: '4px 8px',
                    fontSize: '11px',
                    cursor: 'pointer',
                    fontWeight: '600'
                  }}
                >
                  + Demo Call
                </button>
              </div>
            </div>
            <div className="emergency-list">
              {activeEmergencies.map(emergency => (
                <div key={emergency.id} className={`emergency-card priority-${emergency.priority}`}>
                  <div className="emergency-header">
                    <span className="emergency-id">{emergency.id}</span>
                    <span className={`priority-badge ${emergency.priority}`}>
                      {emergency.priority.toUpperCase()}
                    </span>
                  </div>
                  <div className="emergency-details">
                    <div className="emergency-type">{emergency.type}</div>
                    <div className="emergency-location">📍 {emergency.location}</div>
                    <div className="emergency-time">
                      ⏱️ {Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago
                    </div>

                    {/* AI Classification Info */}
                    <div className="ai-classification">
                      <div className="ai-confidence">
                        🤖 AI: <span style={{ color: '#3b82f6', fontWeight: '600' }}>
                          {(emergency.aiClassification.confidence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="risk-score">
                        ⚠️ Risk: <span style={{
                          color: emergency.aiClassification.riskScore > 0.8 ? '#dc2626' : '#f59e0b',
                          fontWeight: '600'
                        }}>
                          {(emergency.aiClassification.riskScore * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>

                    <div className="emergency-status">
                      Status: <span style={{
                        color: emergency.status === 'dispatched' ? '#f59e0b' :
                               emergency.status === 'en-route' ? '#ef4444' : '#64748b',
                        fontWeight: '600'
                      }}>
                        {emergency.status.charAt(0).toUpperCase() + emergency.status.slice(1)}
                      </span>
                    </div>

                    {emergency.assignedAmbulance && (
                      <div className="assigned-ambulance">
                        🚑 {emergency.assignedAmbulance}
                        {emergency.assignedHospital && (
                          <div style={{ fontSize: '11px', marginTop: '2px' }}>
                            🏥 → {emergency.assignedHospital}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Ambulance Fleet Panel */}
          <div className="panel fleet-panel">
            <div className="panel-header">
              <h3>🚑 Fleet Status</h3>
            </div>
            <div className="ambulance-grid">
              {ambulances.map(ambulance => (
                <div
                  key={ambulance.id}
                  className={`ambulance-card ${ambulance.status} ${selectedAmbulance?.id === ambulance.id ? 'selected' : ''}`}
                  onClick={() => setSelectedAmbulance(ambulance)}
                >
                  <div className="ambulance-header">
                    <span className="ambulance-id">{ambulance.id}</span>
                    <span
                      className="status-dot"
                      style={{ backgroundColor: getStatusColor(ambulance.status) }}
                    ></span>
                  </div>
                  <div className="ambulance-info">
                    <div className="crew-info">
                      <div>👨‍⚕️ {ambulance.driver}</div>
                      <div>🩺 {ambulance.medic}</div>
                    </div>
                    <div className="vehicle-stats">
                      <div className="fuel-level">
                        ⛽ {ambulance.fuel}%
                        <div className="fuel-bar">
                          <div
                            className="fuel-fill"
                            style={{
                              width: `${ambulance.fuel}%`,
                              backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'
                            }}
                          ></div>
                        </div>
                      </div>
                    </div>
                    {ambulance.emergency && (
                      <div className="emergency-assignment">
                        <div className="assignment-type">{ambulance.emergency.type}</div>
                        <div className="assignment-eta">ETA: {ambulance.emergency.eta}</div>
                      </div>
                    )}
                    {ambulance.status === 'available' && (
                      <button
                        onClick={() => {
                          const pendingEmergency = activeEmergencies.find(e => !e.assignedAmbulance);
                          if (pendingEmergency) {
                            dispatchAmbulance(pendingEmergency.id, ambulance.id);
                          }
                        }}
                        style={{
                          background: '#059669',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          padding: '6px 12px',
                          fontSize: '11px',
                          cursor: 'pointer',
                          fontWeight: '600',
                          marginTop: '8px',
                          width: '100%'
                        }}
                        disabled={!activeEmergencies.some(e => !e.assignedAmbulance)}
                      >
                        {activeEmergencies.some(e => !e.assignedAmbulance) ? 'Quick Dispatch' : 'Standby'}
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

        </div>
        </div>
      )}

      {/* Patient Transport Optimizer Page */}
      {currentPage === 'transport-optimizer' && (
        <PatientTransportOptimizer />
      )}

      {/* Traffic Signal Hijacking Page */}
      {currentPage === 'traffic-hijacking' && (
        <TrafficSignalHijacking />
      )}

      {/* Mobile App Page */}
      {currentPage === 'mobile-app' && (
        <MobileApp />
      )}

      {/* Storyboard Page */}
      {currentPage === 'storyboard' && (
        <Storyboard />
      )}

    </div>
  );
};

export default App;
