import React, { useEffect, useState, useRef } from "react";
import "./App.css";
import TrafficDashboard from "./components/TrafficDashboard";
import PatientTransportOptimizer from "./components/PatientTransportOptimizer";
import TrafficSignalHijacking from "./components/TrafficSignalHijacking";
import MobileApp from "./components/MobileApp";
import Storyboard from "./components/Storyboard";
import ARNavigation from "./components/ARNavigation";

// Major locations and landmarks in Chennai
const majorLocations = [
  { id: 1, name: "Chennai Central Railway Station", coords: { lat: 13.0827, lng: 80.2707 }, type: "transport", traffic: "high" },
  { id: 2, name: "Marina Beach", coords: { lat: 13.0500, lng: 80.2824 }, type: "tourist", traffic: "medium" },
  { id: 3, name: "T Nagar Commercial Complex", coords: { lat: 13.0418, lng: 80.2341 }, type: "commercial", traffic: "very_high" },
  { id: 4, name: "Velachery IT Corridor", coords: { lat: 12.9756, lng: 80.2207 }, type: "business", traffic: "high" },
  { id: 5, name: "Adyar Residential Area", coords: { lat: 13.0067, lng: 80.2206 }, type: "residential", traffic: "low" },
  { id: 6, name: "Anna Nagar Metro Station", coords: { lat: 13.0850, lng: 80.2101 }, type: "transport", traffic: "medium" },
  { id: 7, name: "Guindy Industrial Estate", coords: { lat: 13.0067, lng: 80.2206 }, type: "industrial", traffic: "medium" },
  { id: 8, name: "Anna Salai Business District", coords: { lat: 13.0569, lng: 80.2378 }, type: "business", traffic: "very_high" },
  { id: 9, name: "OMR IT Corridor", coords: { lat: 12.9716, lng: 80.2341 }, type: "tech", traffic: "high" },
  { id: 10, name: "Chennai Airport", coords: { lat: 13.1986, lng: 80.1811 }, type: "transport", traffic: "high" }
];

// Traffic routes with density data
const trafficRoutes = [
  {
    id: 1,
    name: "Anna Salai - Main Corridor",
    path: [
      { lat: 13.0827, lng: 80.2707 },
      { lat: 13.0569, lng: 80.2378 },
      { lat: 13.0418, lng: 80.2341 }
    ],
    density: "very_high",
    avgSpeed: 15,
    congestionLevel: 85,
    estimatedTime: "25 mins",
    color: "#dc2626"
  },
  {
    id: 2,
    name: "OMR Tech Corridor",
    path: [
      { lat: 13.0732, lng: 80.2609 },
      { lat: 12.9716, lng: 80.2341 },
      { lat: 12.9141, lng: 80.2270 }
    ],
    density: "high",
    avgSpeed: 35,
    congestionLevel: 70,
    estimatedTime: "18 mins",
    color: "#f59e0b"
  },
  {
    id: 3,
    name: "ECR Coastal Route",
    path: [
      { lat: 13.0500, lng: 80.2824 },
      { lat: 12.9716, lng: 80.2341 },
      { lat: 12.8956, lng: 80.2267 }
    ],
    density: "medium",
    avgSpeed: 45,
    congestionLevel: 45,
    estimatedTime: "22 mins",
    color: "#10b981"
  },
  {
    id: 4,
    name: "GST Road Industrial",
    path: [
      { lat: 13.1986, lng: 80.1811 },
      { lat: 13.0067, lng: 80.2206 },
      { lat: 12.9141, lng: 80.2270 }
    ],
    density: "medium",
    avgSpeed: 40,
    congestionLevel: 55,
    estimatedTime: "20 mins",
    color: "#3b82f6"
  },
  {
    id: 5,
    name: "Inner Ring Road",
    path: [
      { lat: 13.0827, lng: 80.2707 },
      { lat: 13.0732, lng: 80.2609 },
      { lat: 13.0569, lng: 80.2378 },
      { lat: 13.0418, lng: 80.2341 }
    ],
    density: "high",
    avgSpeed: 25,
    congestionLevel: 75,
    estimatedTime: "30 mins",
    color: "#f59e0b"
  }
];

const App = () => {
  const [currentPage, setCurrentPage] = useState('dashboard');
  const [darkMode, setDarkMode] = useState(false);
  const [map, setMap] = useState(null);
  const [selectedRoute, setSelectedRoute] = useState(null);
  const [trafficData, setTrafficData] = useState({
    congestion: 65,
    incidents: 3,
    avgSpeed: 28,
    totalVehicles: 45230,
    activeRoutes: 5
  });
  const [currentTime, setCurrentTime] = useState(new Date());
  const [systemStatus, setSystemStatus] = useState("operational");
  const mapRef = useRef(null);
  const [trafficIncidents, setTrafficIncidents] = useState([
    {
      id: "INC001",
      type: "road accident",
      severity: "high",
      location: "Anna Salai - Thousand Lights",
      coords: { lat: 13.0569, lng: 80.2378 },
      reportedAt: new Date(Date.now() - 300000), // 5 minutes ago
      status: "active",
      affectedRoutes: ["Anna Salai - Main Corridor"]
    },
    {
      id: "INC002",
      type: "vehicle breakdown",
      severity: "medium",
      location: "OMR - Thoraipakkam",
      coords: { lat: 12.9716, lng: 80.2341 },
      reportedAt: new Date(Date.now() - 180000), // 3 minutes ago
      status: "clearing",
      affectedRoutes: ["OMR Tech Corridor"]
    }
  ]);

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update traffic data
      setTrafficData(prev => ({
        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),
        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),
        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))
      }));

      // Simulate ambulance position updates
      setAmbulances(prev => prev.map(amb => ({
        ...amb,
        location: {
          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,
          lng: amb.location.lng + (Math.random() - 0.5) * 0.001
        }
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const initMap = () => {
      const newMap = new window.google.maps.Map(document.getElementById("emergency-map"), {
        zoom: 12,
        center: { lat: 13.0827, lng: 80.2707 },
        styles: [
          { elementType: "geometry", stylers: [{ color: "#0a0f1e" }] },
          { elementType: "labels.text.stroke", stylers: [{ color: "#0a0f1e" }] },
          { elementType: "labels.text.fill", stylers: [{ color: "#00e5ff" }] },
          { featureType: "road", elementType: "geometry", stylers: [{ color: "#1a2332" }] },
          { featureType: "road", elementType: "geometry.stroke", stylers: [{ color: "#00e5ff" }] },
          { featureType: "water", elementType: "geometry", stylers: [{ color: "#001122" }] }
        ]
      });

      const trafficLayer = new window.google.maps.TrafficLayer();
      trafficLayer.setMap(newMap);

      setMap(newMap);
      mapRef.current = newMap;
    };

    const loadGoogleMapsScript = () => {
      if (!window.google || !window.google.maps) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);
        script.onload = initMap;
      } else {
        initMap();
      }
    };

    loadGoogleMapsScript();
  }, []);

  // Utility functions for ambulance management
  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#00ff88';
      case 'dispatched': return '#ffaa00';
      case 'en-route': return '#ff4444';
      case 'maintenance': return '#666666';
      default: return '#00e5ff';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#ff0044';
      case 'high': return '#ff6600';
      case 'medium': return '#ffaa00';
      case 'low': return '#00ff88';
      default: return '#00e5ff';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const findNearestAmbulance = (emergencyCoords) => {
    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');
    if (availableAmbulances.length === 0) return null;

    return availableAmbulances.reduce((nearest, current) => {
      const currentDistance = calculateDistance(
        emergencyCoords.lat, emergencyCoords.lng,
        current.location.lat, current.location.lng
      );
      const nearestDistance = calculateDistance(
        emergencyCoords.lat, emergencyCoords.lng,
        nearest.location.lat, nearest.location.lng
      );
      return currentDistance < nearestDistance ? current : nearest;
    });
  };

  const dispatchAmbulance = (emergencyId, ambulanceId) => {
    setAmbulances(prev => prev.map(amb =>
      amb.id === ambulanceId
        ? { ...amb, status: 'dispatched' }
        : amb
    ));

    setActiveEmergencies(prev => prev.map(emergency =>
      emergency.id === emergencyId
        ? { ...emergency, assignedAmbulance: ambulanceId, status: 'dispatched' }
        : emergency
    ));
  };


  const renderCurrentPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return <TrafficDashboard />;
      case 'transport':
        return <PatientTransportOptimizer />;
      case 'traffic-signals':
        return <TrafficSignalHijacking />;
      case 'mobile':
        return <MobileApp />;
      case 'storyboard':
        return <Storyboard />;
      case 'ar-navigation':
        return <ARNavigation />;
      default:
        return <TrafficDashboard />;
    }
  };

  return (
    <div className="app-container">
      {/* Navigation Header */}
      <header className="app-header">
        <div className="header-left">
          <h1 className="app-title">
            🚦 Chennai Traffic Management System
          </h1>
        </div>
        <nav className="main-navigation">
          <button
            className={`nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`}
            onClick={() => setCurrentPage('dashboard')}
          >
            🗺️ Traffic Dashboard
          </button>
          <button
            className={`nav-btn ${currentPage === 'transport' ? 'active' : ''}`}
            onClick={() => setCurrentPage('transport')}
          >
            🚑 Transport Optimizer
          </button>
          <button
            className={`nav-btn ${currentPage === 'traffic-signals' ? 'active' : ''}`}
            onClick={() => setCurrentPage('traffic-signals')}
          >
            🚦 Traffic Signals
          </button>
          <button
            className={`nav-btn ${currentPage === 'mobile' ? 'active' : ''}`}
            onClick={() => setCurrentPage('mobile')}
          >
            📱 Mobile App
          </button>
          <button
            className={`nav-btn ${currentPage === 'ar-navigation' ? 'active' : ''}`}
            onClick={() => setCurrentPage('ar-navigation')}
          >
            🥽 AR Navigation
          </button>
          <button
            className={`nav-btn ${currentPage === 'storyboard' ? 'active' : ''}`}
            onClick={() => setCurrentPage('storyboard')}
          >
            📋 System Overview
          </button>
        </nav>
      </header>

      {/* Main Content */}
      <main className="app-main">
        {renderCurrentPage()}
      </main>
    </div>
  );
};

export default App;
