import React, { useEffect, useState, useRef } from "react";
import "./App.css";

// Emergency locations and hospitals in Chennai
const emergencyLocations = [
  { id: 1, name: "Chennai Central Railway Station", coords: { lat: 13.0827, lng: 80.2707 }, type: "transport" },
  { id: 2, name: "Marina Beach", coords: { lat: 13.0500, lng: 80.2824 }, type: "public" },
  { id: 3, name: "T Nagar Commercial Complex", coords: { lat: 13.0418, lng: 80.2341 }, type: "commercial" },
  { id: 4, name: "Velachery IT Corridor", coords: { lat: 12.9756, lng: 80.2207 }, type: "business" },
  { id: 5, name: "Adyar Residential Area", coords: { lat: 13.0067, lng: 80.2206 }, type: "residential" },
  { id: 6, name: "Anna Nagar Metro Station", coords: { lat: 13.0850, lng: 80.2101 }, type: "transport" },
  { id: 7, name: "Guindy Industrial Estate", coords: { lat: 13.0067, lng: 80.2206 }, type: "industrial" },
];

const hospitals = [
  { id: 1, name: "Apollo Hospital", coords: { lat: 13.0358, lng: 80.2297 }, capacity: 85, emergency: true },
  { id: 2, name: "Fortis Malar Hospital", coords: { lat: 13.0067, lng: 80.2206 }, capacity: 92, emergency: true },
  { id: 3, name: "MIOT International", coords: { lat: 12.9756, lng: 80.2207 }, capacity: 78, emergency: true },
  { id: 4, name: "Stanley Medical College", coords: { lat: 13.0827, lng: 80.2707 }, capacity: 65, emergency: true },
  { id: 5, name: "Rajiv Gandhi Govt Hospital", coords: { lat: 13.1358, lng: 80.2297 }, capacity: 45, emergency: true },
];

// Mock ambulance data
const initialAmbulances = [
  {
    id: "AMB001",
    status: "available",
    location: { lat: 13.0827, lng: 80.2707 },
    driver: "Raj Kumar",
    medic: "Dr. Priya",
    fuel: 85,
    lastMaintenance: "2024-01-10"
  },
  {
    id: "AMB002",
    status: "dispatched",
    location: { lat: 13.0500, lng: 80.2824 },
    driver: "Suresh M",
    medic: "Dr. Arun",
    fuel: 92,
    lastMaintenance: "2024-01-08",
    emergency: { type: "cardiac", priority: "high", eta: "4 min" }
  },
  {
    id: "AMB003",
    status: "en-route",
    location: { lat: 13.0418, lng: 80.2341 },
    driver: "Karthik S",
    medic: "Dr. Meera",
    fuel: 67,
    lastMaintenance: "2024-01-12",
    emergency: { type: "accident", priority: "critical", eta: "7 min" }
  },
  {
    id: "AMB004",
    status: "maintenance",
    location: { lat: 12.9756, lng: 80.2207 },
    driver: "Venkat R",
    medic: "Dr. Lakshmi",
    fuel: 23,
    lastMaintenance: "2024-01-15"
  },
];

const App = () => {
  const [map, setMap] = useState(null);
  const [ambulances, setAmbulances] = useState(initialAmbulances);
  const [selectedAmbulance, setSelectedAmbulance] = useState(null);
  const [emergencyAlerts, setEmergencyAlerts] = useState([]);
  const [trafficData, setTrafficData] = useState({ congestion: 65, incidents: 3, avgSpeed: 28 });
  const [currentTime, setCurrentTime] = useState(new Date());
  const [systemStatus, setSystemStatus] = useState("operational");
  const mapRef = useRef(null);
  const [activeEmergencies, setActiveEmergencies] = useState([
    {
      id: "EMG001",
      type: "cardiac arrest",
      priority: "critical",
      location: "T Nagar Commercial Complex",
      coords: { lat: 13.0418, lng: 80.2341 },
      reportedAt: new Date(Date.now() - 300000), // 5 minutes ago
      assignedAmbulance: "AMB002",
      status: "dispatched"
    },
    {
      id: "EMG002",
      type: "road accident",
      priority: "high",
      location: "Anna Nagar Metro Station",
      coords: { lat: 13.0850, lng: 80.2101 },
      reportedAt: new Date(Date.now() - 180000), // 3 minutes ago
      assignedAmbulance: "AMB003",
      status: "en-route"
    }
  ]);

  // Real-time clock update
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Simulate real-time data updates
  useEffect(() => {
    const interval = setInterval(() => {
      // Update traffic data
      setTrafficData(prev => ({
        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),
        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),
        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))
      }));

      // Simulate ambulance position updates
      setAmbulances(prev => prev.map(amb => ({
        ...amb,
        location: {
          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,
          lng: amb.location.lng + (Math.random() - 0.5) * 0.001
        }
      })));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const initMap = () => {
      const newMap = new window.google.maps.Map(document.getElementById("emergency-map"), {
        zoom: 12,
        center: { lat: 13.0827, lng: 80.2707 },
        styles: [
          { elementType: "geometry", stylers: [{ color: "#0a0f1e" }] },
          { elementType: "labels.text.stroke", stylers: [{ color: "#0a0f1e" }] },
          { elementType: "labels.text.fill", stylers: [{ color: "#00e5ff" }] },
          { featureType: "road", elementType: "geometry", stylers: [{ color: "#1a2332" }] },
          { featureType: "road", elementType: "geometry.stroke", stylers: [{ color: "#00e5ff" }] },
          { featureType: "water", elementType: "geometry", stylers: [{ color: "#001122" }] }
        ]
      });

      const trafficLayer = new window.google.maps.TrafficLayer();
      trafficLayer.setMap(newMap);

      setMap(newMap);
      mapRef.current = newMap;
    };

    const loadGoogleMapsScript = () => {
      if (!window.google || !window.google.maps) {
        const script = document.createElement("script");
        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;
        script.async = true;
        script.defer = true;
        document.body.appendChild(script);
        script.onload = initMap;
      } else {
        initMap();
      }
    };

    loadGoogleMapsScript();
  }, []);

  // Utility functions for ambulance management
  const getStatusColor = (status) => {
    switch (status) {
      case 'available': return '#00ff88';
      case 'dispatched': return '#ffaa00';
      case 'en-route': return '#ff4444';
      case 'maintenance': return '#666666';
      default: return '#00e5ff';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'critical': return '#ff0044';
      case 'high': return '#ff6600';
      case 'medium': return '#ffaa00';
      case 'low': return '#00ff88';
      default: return '#00e5ff';
    }
  };

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const findNearestAmbulance = (emergencyCoords) => {
    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');
    if (availableAmbulances.length === 0) return null;

    return availableAmbulances.reduce((nearest, current) => {
      const currentDistance = calculateDistance(
        emergencyCoords.lat, emergencyCoords.lng,
        current.location.lat, current.location.lng
      );
      const nearestDistance = calculateDistance(
        emergencyCoords.lat, emergencyCoords.lng,
        nearest.location.lat, nearest.location.lng
      );
      return currentDistance < nearestDistance ? current : nearest;
    });
  };

  const dispatchAmbulance = (emergencyId, ambulanceId) => {
    setAmbulances(prev => prev.map(amb =>
      amb.id === ambulanceId
        ? { ...amb, status: 'dispatched' }
        : amb
    ));

    setActiveEmergencies(prev => prev.map(emergency =>
      emergency.id === emergencyId
        ? { ...emergency, assignedAmbulance: ambulanceId, status: 'dispatched' }
        : emergency
    ));
  };


  return (
    <div className="dispatch-center">
      {/* Header with system status */}
      <header className="dispatch-header">
        <div className="header-left">
          <h1 className="system-title">
            <span className="pulse-dot"></span>
            SMART AMBULANCE DISPATCH
          </h1>
          <div className="system-status">
            <span className={`status-indicator ${systemStatus}`}></span>
            System Status: {systemStatus.toUpperCase()}
          </div>
        </div>
        <div className="header-right">
          <div className="current-time">
            <div className="time-display">{formatTime(currentTime)}</div>
            <div className="date-display">{currentTime.toLocaleDateString()}</div>
          </div>
        </div>
      </header>

      {/* Main dashboard grid */}
      <div className="dashboard-grid">

        {/* Emergency Alerts Panel */}
        <div className="panel emergency-panel">
          <div className="panel-header">
            <h3>🚨 ACTIVE EMERGENCIES</h3>
            <span className="emergency-count">{activeEmergencies.length}</span>
          </div>
          <div className="emergency-list">
            {activeEmergencies.map(emergency => (
              <div key={emergency.id} className={`emergency-card priority-${emergency.priority}`}>
                <div className="emergency-header">
                  <span className="emergency-id">{emergency.id}</span>
                  <span className={`priority-badge ${emergency.priority}`}>
                    {emergency.priority.toUpperCase()}
                  </span>
                </div>
                <div className="emergency-details">
                  <div className="emergency-type">{emergency.type}</div>
                  <div className="emergency-location">📍 {emergency.location}</div>
                  <div className="emergency-time">
                    ⏱️ {Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago
                  </div>
                  {emergency.assignedAmbulance && (
                    <div className="assigned-ambulance">
                      🚑 {emergency.assignedAmbulance} - {emergency.status}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Traffic Analytics Panel */}
        <div className="panel analytics-panel">
          <div className="panel-header">
            <h3>📊 TRAFFIC ANALYTICS</h3>
          </div>
          <div className="analytics-grid">
            <div className="metric-card">
              <div className="metric-value">{trafficData.congestion}%</div>
              <div className="metric-label">Traffic Congestion</div>
              <div className={`metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`}>
                {trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'}
              </div>
            </div>
            <div className="metric-card">
              <div className="metric-value">{trafficData.incidents}</div>
              <div className="metric-label">Active Incidents</div>
              <div className={`metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`}>
                {trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'}
              </div>
            </div>
            <div className="metric-card">
              <div className="metric-value">{trafficData.avgSpeed} km/h</div>
              <div className="metric-label">Avg Speed</div>
              <div className={`metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`}>
                {trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'}
              </div>
            </div>
          </div>
        </div>

        {/* Ambulance Fleet Panel */}
        <div className="panel fleet-panel">
          <div className="panel-header">
            <h3>🚑 AMBULANCE FLEET</h3>
            <div className="fleet-summary">
              <span className="available-count">
                {ambulances.filter(a => a.status === 'available').length} Available
              </span>
              <span className="active-count">
                {ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length} Active
              </span>
            </div>
          </div>
          <div className="ambulance-grid">
            {ambulances.map(ambulance => (
              <div
                key={ambulance.id}
                className={`ambulance-card ${ambulance.status} ${selectedAmbulance?.id === ambulance.id ? 'selected' : ''}`}
                onClick={() => setSelectedAmbulance(ambulance)}
              >
                <div className="ambulance-header">
                  <span className="ambulance-id">{ambulance.id}</span>
                  <span
                    className="status-dot"
                    style={{ backgroundColor: getStatusColor(ambulance.status) }}
                  ></span>
                </div>
                <div className="ambulance-info">
                  <div className="crew-info">
                    <div>👨‍⚕️ {ambulance.driver}</div>
                    <div>🩺 {ambulance.medic}</div>
                  </div>
                  <div className="vehicle-stats">
                    <div className="fuel-level">
                      ⛽ {ambulance.fuel}%
                      <div className="fuel-bar">
                        <div
                          className="fuel-fill"
                          style={{
                            width: `${ambulance.fuel}%`,
                            backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                  {ambulance.emergency && (
                    <div className="emergency-assignment">
                      <div className="assignment-type">{ambulance.emergency.type}</div>
                      <div className="assignment-eta">ETA: {ambulance.emergency.eta}</div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Map Panel */}
        <div className="panel map-panel">
          <div className="panel-header">
            <h3>🗺️ REAL-TIME MAP</h3>
            <div className="map-controls">
              <button className="map-btn">Traffic Layer</button>
              <button className="map-btn">Hospitals</button>
              <button className="map-btn">Routes</button>
            </div>
          </div>
          <div id="emergency-map" className="emergency-map"></div>
        </div>

      </div>
    </div>
  );
};

export default App;
