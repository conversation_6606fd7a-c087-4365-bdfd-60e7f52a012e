import cv2
import numpy as np
import pyautogui
import pygame
import sys
from PyQt5.QtWidgets import QApplication, QRubberBand, QWidget
from PyQt5.QtCore import QRect, QPoint
from PyQt5.QtGui import QPixmap

# Step 1: Select a Region of the Screen
class ScreenCapture(QWidget):
    def __init__(self):
        super().__init__()
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.rubber_band = QRubberBand(QRubberBand.Rectangle, self)
        self.setWindowOpacity(0.3)  # Transparent overlay
        self.showFullScreen()  # Capture full screen

    def mousePressEvent(self, event):
        self.start_point = event.pos()
        self.rubber_band.setGeometry(QRect(self.start_point, self.start_point))
        self.rubber_band.show()

    def mouseMoveEvent(self, event):
        self.rubber_band.setGeometry(QRect(self.start_point, event.pos()).normalized())

    def mouseReleaseEvent(self, event):
        self.end_point = event.pos()
        self.capture_screenshot()
        self.close()

    def capture_screenshot(self):
        rect = QRect(self.start_point, self.end_point)
        screen = QApplication.primaryScreen()
        pixmap = screen.grabWindow(0, rect.x(), rect.y(), rect.width(), rect.height())
        pixmap.save("screenshot.png", "PNG")


# Step 2: Detect Roads in the Screenshot
def process_image():
    img = cv2.imread("screenshot.png")

    # Convert to HSV to detect highlighted roads
    hsv = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    lower_bound = np.array([75, 100, 100])  # Adjust for neon green/cyan
    upper_bound = np.array([100, 255, 255])
    mask = cv2.inRange(hsv, lower_bound, upper_bound)
    roads_highlighted = cv2.bitwise_and(img, img, mask=mask)

    # Convert to grayscale
    gray = cv2.cvtColor(roads_highlighted, cv2.COLOR_BGR2GRAY)

    # Apply Canny edge detection
    edges = cv2.Canny(gray, 50, 150)

    # Detect lines using Hough Transform
    lines = cv2.HoughLinesP(edges, 1, np.pi / 180, threshold=50, minLineLength=50, maxLineGap=20)

    path = []
    if lines is not None:
        for line in lines:
            x1, y1, x2, y2 = line[0]
            path.append((x1, y1))
            path.append((x2, y2))

    return img, path


# Step 3: Follow the Roads with Pygame
def follow_lines(image, path):
    pygame.init()
    HEIGHT, WIDTH, _ = image.shape
    screen = pygame.display.set_mode((WIDTH, HEIGHT))
    pygame.display.set_caption("Following the Roads")

    # Convert image for Pygame
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    image = np.rot90(image)
    image = pygame.surfarray.make_surface(image)

    follower_index = 0
    clock = pygame.time.Clock()
    running = True

    while running:
        screen.blit(image, (0, 0))  # Display Screenshot

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

        # Draw detected roads
        if path:
            for i in range(0, len(path) - 1, 2):
                pygame.draw.line(screen, (0, 255, 0), path[i], path[i + 1], 2)

        # Move object along detected roads
        if path and follower_index < len(path):
            pygame.draw.circle(screen, (255, 0, 0), path[follower_index], 8)  # Moving Object
            follower_index = (follower_index + 1) % len(path)  # Loop movement

        pygame.display.flip()
        clock.tick(30)  # 30 FPS

    pygame.quit()


# Run the Application
if __name__ == "__main__":
    app = QApplication(sys.argv)
    capture = ScreenCapture()
    app.exec_()  # Wait for user to select region

    img, path = process_image()
    follow_lines(img, path)
