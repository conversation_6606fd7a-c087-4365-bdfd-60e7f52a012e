.traffic-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: white;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  font-size: 14px;
  color: #e5e7eb;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.view-controls {
  display: flex;
  gap: 10px;
}

.view-btn {
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
}

.view-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.view-btn.active {
  background: linear-gradient(45deg, #10b981, #059669);
  border-color: #10b981;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.dashboard-content {
  display: grid;
  grid-template-columns: 300px 1fr 350px;
  gap: 20px;
  padding: 20px 30px;
  height: calc(100vh - 120px);
}

/* Stats Panel */
.stats-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
}

.stats-panel h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffd700;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  margin-bottom: 15px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #d1d5db;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-status {
  font-size: 11px;
  font-weight: 600;
  margin-top: 2px;
}

.legend-section {
  margin-top: 30px;
}

.legend-section h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #ffd700;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 12px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

/* Map Panel */
.map-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
}

.map-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.map-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.map-overlay {
  text-align: center;
  z-index: 10;
  position: relative;
}

.map-overlay h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 700;
}

.map-overlay p {
  margin: 0 0 15px 0;
  color: #d1d5db;
}

.map-info {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  font-size: 14px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.traffic-zones {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.traffic-zone {
  transition: all 0.3s ease;
}

.traffic-zone:hover {
  transform: scale(1.05);
}

.route-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.route-line {
  transition: all 0.3s ease;
}

.route-line:hover {
  height: 6px !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Details Panel */
.details-panel {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow-y: auto;
}

.details-panel h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 600;
  color: #ffd700;
}

.route-details h4 {
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.route-details p {
  margin: 0 0 20px 0;
  color: #d1d5db;
  font-size: 14px;
  line-height: 1.5;
}

.route-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.route-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.route-stat .label {
  font-size: 12px;
  color: #d1d5db;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.route-stat .value {
  font-weight: 600;
  color: white;
}

.route-path h5, .peak-hours h5 {
  margin: 20px 0 10px 0;
  font-size: 14px;
  color: #ffd700;
}

.route-path ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.route-path li {
  padding: 6px 0;
  color: #d1d5db;
  font-size: 13px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.route-path li:last-child {
  border-bottom: none;
}

.peak-times {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.peak-time {
  padding: 4px 8px;
  background: rgba(255, 215, 0, 0.2);
  color: #ffd700;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.no-selection p {
  color: #d1d5db;
  text-align: center;
  margin-bottom: 20px;
}

.route-list h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  color: #ffd700;
}

.route-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.route-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.route-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.route-info {
  flex: 1;
}

.route-name {
  font-size: 13px;
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
}

.route-summary {
  font-size: 11px;
  color: #d1d5db;
}

.congestion-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-content {
    grid-template-columns: 250px 1fr 300px;
  }
}

@media (max-width: 768px) {
  .dashboard-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr auto;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .view-controls {
    justify-content: center;
  }
}
