import cv2
import numpy as np
from PIL import ImageGrab, Image
from PyQt5.QtWidgets import (QApplication, QRubberBand, QWidget, QMainWindow, 
                           QLabel, QGridLayout, QScrollArea, QPushButton, QVBoxLayout)
from PyQt5.QtCore import QRect, QPoint, Qt
from PyQt5.QtGui import QPixmap, QImage
import sys

class ImageViewer(QMainWindow):
    def __init__(self, images_dict):
        super().__init__()
        self.setWindowTitle("Map Analysis Gallery")
        self.setGeometry(100, 100, 1200, 800)

        # Create a central widget and layout
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Create a scroll area
        scroll = QScrollArea()
        layout.addWidget(scroll)
        scroll.setWidgetResizable(True)

        # Create a widget to hold the grid
        content_widget = QWidget()
        grid = QGridLayout(content_widget)
        scroll.setWidget(content_widget)

        # Add images to grid
        row = 0
        col = 0
        for title, img in images_dict.items():
            # Convert cv2 image to QPixmap
            if len(img.shape) == 2:  # Grayscale
                height, width = img.shape
                qimg = QImage(img.data, width, height, width, QImage.Format_Grayscale8)
            else:  # Color
                height, width, channel = img.shape
                bytes_per_line = 3 * width
                qimg = QImage(img.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            
            # Create QPixmap and scale it
            pixmap = QPixmap.fromImage(qimg)
            scaled_pixmap = pixmap.scaled(400, 300, Qt.KeepAspectRatio, Qt.SmoothTransformation)

            # Create label for image
            image_label = QLabel()
            image_label.setPixmap(scaled_pixmap)
            image_label.setAlignment(Qt.AlignCenter)

            # Create label for title
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-top: 5px;")

            # Create a container for image and title
            container = QWidget()
            container_layout = QVBoxLayout(container)
            container_layout.addWidget(image_label)
            container_layout.addWidget(title_label)
            container.setStyleSheet("background-color: white; border: 1px solid #cccccc; border-radius: 5px; padding: 10px; margin: 5px;")

            # Add to grid
            grid.addWidget(container, row, col)
            
            # Update grid position
            col += 1
            if col > 1:  # 2 images per row
                col = 0
                row += 1

        self.show()

class MapScreenCapture(QWidget):
    def __init__(self):
        super().__init__()
        self.start_point = QPoint()
        self.end_point = QPoint()
        self.rubber_band = QRubberBand(QRubberBand.Rectangle, self)
        self.setWindowOpacity(0.3)
        self.showFullScreen()

    def mousePressEvent(self, event):
        self.start_point = event.pos()
        self.rubber_band.setGeometry(QRect(self.start_point, self.start_point))
        self.rubber_band.show()

    def mouseMoveEvent(self, event):
        self.rubber_band.setGeometry(QRect(self.start_point, event.pos()).normalized())

    def mouseReleaseEvent(self, event):
        self.end_point = event.pos()
        self.capture_and_process()
        self.close()

    def remove_text(self, image):
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        mask = np.ones_like(image) * 255
        
        for cnt in contours:
            x, y, w, h = cv2.boundingRect(cnt)
            aspect_ratio = float(w)/h
            area = cv2.contourArea(cnt)
            if area < 500 and (aspect_ratio > 0.2 and aspect_ratio < 5):
                cv2.drawContours(mask, [cnt], -1, (0, 0, 0), -1)
        
        result = cv2.bitwise_and(image, mask)
        return result

    def detect_roads(self, image):
        no_text_img = self.remove_text(image)
        hsv = cv2.cvtColor(no_text_img, cv2.COLOR_BGR2HSV)
        
        lower_white = np.array([0, 0, 200])
        upper_white = np.array([180, 30, 255])
        lower_yellow = np.array([20, 50, 180])
        upper_yellow = np.array([35, 255, 255])
        
        white_mask = cv2.inRange(hsv, lower_white, upper_white)
        yellow_mask = cv2.inRange(hsv, lower_yellow, upper_yellow)
        road_mask = cv2.bitwise_or(white_mask, yellow_mask)
        
        kernel = np.ones((3,3), np.uint8)
        road_mask = cv2.morphologyEx(road_mask, cv2.MORPH_CLOSE, kernel)
        road_mask = cv2.morphologyEx(road_mask, cv2.MORPH_OPEN, kernel)
        
        road_image = image.copy()
        road_image[road_mask > 0] = [0, 255, 0]
        
        return road_image, road_mask

    def detect_and_draw_lines(self, image, gray):
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        kernel = np.ones((3,3), np.uint8)
        dilated = cv2.dilate(edges, kernel, iterations=1)
        
        lines = cv2.HoughLinesP(
            dilated,
            rho=1,
            theta=np.pi/180,
            threshold=50,
            minLineLength=30,
            maxLineGap=20
        )
        
        line_image = image.copy()
        if lines is not None:
            for line in lines:
                x1, y1, x2, y2 = line[0]
                cv2.line(line_image, (x1, y1), (x2, y2), (255, 0, 0), 2)
            print(f"Number of line segments detected: {len(lines)}")
        
        return line_image, lines

    def capture_and_process(self):
        # Capture the selected region
        rect = QRect(self.start_point, self.end_point)
        screen = QApplication.primaryScreen()
        original = screen.grabWindow(0, rect.x(), rect.y(), rect.width(), rect.height())
        original.save("original_map.png", "PNG")

        # Load and process images
        self.original_img = cv2.imread("original_map.png")
        self.grayscale_img = cv2.cvtColor(self.original_img, cv2.COLOR_BGR2GRAY)
        self.line_img, _ = self.detect_and_draw_lines(self.original_img, self.grayscale_img)
        self.road_img, self.road_mask = self.detect_roads(self.original_img)
        self.combined_img = cv2.addWeighted(self.line_img, 0.5, self.road_img, 0.5, 0)
        
        # Save all versions
        cv2.imwrite("grayscale_map.png", self.grayscale_img)
        cv2.imwrite("detected_lines_map.png", self.line_img)
        cv2.imwrite("detected_roads_map.png", self.road_img)
        cv2.imwrite("combined_view.png", self.combined_img)
        cv2.imwrite("road_mask.png", self.road_mask)

        # Create dictionary of images for gallery
        images_dict = {
            "Original Image": self.original_img,
            "Grayscale": self.grayscale_img,
            "Detected Lines": self.line_img,
            "Detected Roads": self.road_img,
            "Combined View": self.combined_img,
            "Road Mask": self.road_mask
        }

        # Show gallery
        self.gallery = ImageViewer(images_dict)

def main():
    print("Please select the area of the Google Maps route you want to capture.")
    print("Click and drag to select the area.")
    print("After selection, the program will:")
    print("1. Capture the selected area")
    print("2. Process the image in multiple ways:")
    print("   - Convert to grayscale")
    print("   - Detect and draw line segments")
    print("   - Remove text and detect roads")
    print("   - Create combined view")
    print("3. Display all versions in a gallery view")
    
    app = QApplication(sys.argv)
    screen_capture = MapScreenCapture()
    app.exec_()

if __name__ == "__main__":
    main() 