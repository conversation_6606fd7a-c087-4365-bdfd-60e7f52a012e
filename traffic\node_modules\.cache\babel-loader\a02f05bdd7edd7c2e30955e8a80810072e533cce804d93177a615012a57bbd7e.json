{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\components\\\\MobileApp.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './MobileApp.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileApp = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [emergencyForm, setEmergencyForm] = useState({\n    emergencyType: '',\n    description: '',\n    location: '',\n    patientAge: '',\n    patientGender: '',\n    symptoms: [],\n    urgency: 'medium',\n    contactNumber: '',\n    additionalInfo: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submissionStatus, setSubmissionStatus] = useState(null);\n  const [userLocation, setUserLocation] = useState(null);\n  const [isVoiceRecording, setIsVoiceRecording] = useState(false);\n  const [voiceTranscript, setVoiceTranscript] = useState('');\n\n  // Emergency types with icons\n  const emergencyTypes = [{\n    id: 'cardiac',\n    name: 'Heart Attack / Cardiac Emergency',\n    icon: '💓',\n    priority: 'critical'\n  }, {\n    id: 'accident',\n    name: 'Road Accident / Trauma',\n    icon: '🚗',\n    priority: 'high'\n  }, {\n    id: 'breathing',\n    name: 'Breathing Difficulty',\n    icon: '🫁',\n    priority: 'high'\n  }, {\n    id: 'stroke',\n    name: 'Stroke / Neurological',\n    icon: '🧠',\n    priority: 'critical'\n  }, {\n    id: 'overdose',\n    name: 'Poisoning / Overdose',\n    icon: '💊',\n    priority: 'high'\n  }, {\n    id: 'injury',\n    name: 'Severe Injury / Bleeding',\n    icon: '🩸',\n    priority: 'medium'\n  }, {\n    id: 'pregnancy',\n    name: 'Pregnancy Emergency',\n    icon: '🤱',\n    priority: 'high'\n  }, {\n    id: 'other',\n    name: 'Other Emergency',\n    icon: '🚨',\n    priority: 'medium'\n  }];\n\n  // Common symptoms\n  const commonSymptoms = ['Chest pain', 'Difficulty breathing', 'Unconscious', 'Severe bleeding', 'Broken bones', 'Severe pain', 'Nausea/Vomiting', 'Dizziness', 'High fever', 'Seizure', 'Confusion', 'Weakness'];\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        setUserLocation({\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        });\n      }, error => {\n        console.log('Location access denied:', error);\n      });\n    }\n  }, []);\n  const handleInputChange = (field, value) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSymptomToggle = symptom => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      symptoms: prev.symptoms.includes(symptom) ? prev.symptoms.filter(s => s !== symptom) : [...prev.symptoms, symptom]\n    }));\n  };\n  const handleEmergencyTypeSelect = type => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      emergencyType: type.id,\n      urgency: type.priority\n    }));\n  };\n  const startVoiceRecording = () => {\n    setIsVoiceRecording(true);\n    // Simulate voice recording\n    setTimeout(() => {\n      setVoiceTranscript(\"Help! I need an ambulance at Marina Beach. My friend is having chest pain and difficulty breathing.\");\n      setIsVoiceRecording(false);\n      // Auto-fill form based on voice\n      setEmergencyForm(prev => ({\n        ...prev,\n        emergencyType: 'cardiac',\n        description: 'Chest pain and difficulty breathing',\n        location: 'Marina Beach',\n        symptoms: ['Chest pain', 'Difficulty breathing'],\n        urgency: 'critical'\n      }));\n    }, 3000);\n  };\n  const submitEmergencyRequest = async () => {\n    setIsSubmitting(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      const emergencyId = `EMG${Date.now().toString().slice(-3)}`;\n      setSubmissionStatus({\n        success: true,\n        emergencyId,\n        estimatedArrival: '8-12 minutes',\n        assignedAmbulance: 'AMB001',\n        message: 'Emergency request submitted successfully! Ambulance is being dispatched.'\n      });\n      setIsSubmitting(false);\n    }, 2000);\n  };\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mobile-app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-app-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCF1 Emergency Mobile App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-time\",\n        children: formatTime(currentTime)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), !submissionStatus ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"emergency-request-form\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDEA8 Request Emergency Ambulance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"form-description\",\n          children: \"Fill out this form to request immediate medical assistance. Your location will be automatically detected if available.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Emergency Type *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"emergency-types-grid\",\n            children: emergencyTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `emergency-type-btn ${emergencyForm.emergencyType === type.id ? 'selected' : ''}`,\n              onClick: () => handleEmergencyTypeSelect(type),\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"emergency-icon\",\n                children: type.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"emergency-name\",\n                children: type.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 21\n              }, this)]\n            }, type.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\uD83C\\uDFA4 Voice Description (Optional)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"voice-input-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: `voice-btn ${isVoiceRecording ? 'recording' : ''}`,\n              onClick: startVoiceRecording,\n              disabled: isVoiceRecording,\n              children: isVoiceRecording ? '🔴 Recording...' : '🎤 Describe Emergency'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 17\n            }, this), voiceTranscript && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"voice-transcript\",\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Transcript:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 181,\n                columnNumber: 21\n              }, this), \" \", voiceTranscript]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 180,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Location *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: emergencyForm.location,\n            onChange: e => handleInputChange('location', e.target.value),\n            placeholder: \"Enter your current location or address\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), userLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"location-detected\",\n            children: [\"\\uD83D\\uDCCD Location detected: \", userLocation.lat.toFixed(4), \", \", userLocation.lng.toFixed(4)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Emergency Description *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n            value: emergencyForm.description,\n            onChange: e => handleInputChange('description', e.target.value),\n            placeholder: \"Describe what happened and current condition\",\n            className: \"form-textarea\",\n            rows: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Patient Age\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              value: emergencyForm.patientAge,\n              onChange: e => handleInputChange('patientAge', e.target.value),\n              placeholder: \"Age\",\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Gender\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              value: emergencyForm.patientGender,\n              onChange: e => handleInputChange('patientGender', e.target.value),\n              className: \"form-select\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"male\",\n                children: \"Male\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"female\",\n                children: \"Female\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"other\",\n                children: \"Other\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 230,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Symptoms (Select all that apply)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"symptoms-grid\",\n            children: commonSymptoms.map(symptom => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `symptom-btn ${emergencyForm.symptoms.includes(symptom) ? 'selected' : ''}`,\n              onClick: () => handleSymptomToggle(symptom),\n              children: symptom\n            }, symptom, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Contact Number *\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"tel\",\n            value: emergencyForm.contactNumber,\n            onChange: e => handleInputChange('contactNumber', e.target.value),\n            placeholder: \"+91 XXXXX XXXXX\",\n            className: \"form-input\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Urgency Level\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"urgency-buttons\",\n            children: ['low', 'medium', 'high', 'critical'].map(level => /*#__PURE__*/_jsxDEV(\"button\", {\n              className: `urgency-btn ${emergencyForm.urgency === level ? 'selected' : ''} ${level}`,\n              onClick: () => handleInputChange('urgency', level),\n              children: level.toUpperCase()\n            }, level, false, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"submit-emergency-btn\",\n          onClick: submitEmergencyRequest,\n          disabled: isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location || !emergencyForm.contactNumber,\n          children: isSubmitting ? '🚑 Dispatching Ambulance...' : '🚨 REQUEST EMERGENCY AMBULANCE'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"submission-success\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-icon\",\n        children: \"\\u2705\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 299,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Emergency Request Submitted!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 300,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Emergency ID:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this), \" \", submissionStatus.emergencyId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Assigned Ambulance:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 15\n          }, this), \" \", submissionStatus.assignedAmbulance]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"detail-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Estimated Arrival:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), \" \", submissionStatus.estimatedArrival]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"success-message\",\n        children: submissionStatus.message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"new-request-btn\",\n        onClick: () => {\n          setSubmissionStatus(null);\n          setEmergencyForm({\n            emergencyType: '',\n            description: '',\n            location: '',\n            patientAge: '',\n            patientGender: '',\n            symptoms: [],\n            urgency: 'medium',\n            contactNumber: '',\n            additionalInfo: ''\n          });\n          setVoiceTranscript('');\n        },\n        children: \"Submit New Request\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 298,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(MobileApp, \"QB+94yed2EmGzqn5UUkGBGhZMms=\");\n_c = MobileApp;\nexport default MobileApp;\nvar _c;\n$RefreshReg$(_c, \"MobileApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "MobileApp", "_s", "currentTime", "setCurrentTime", "Date", "emergencyForm", "setEmergencyForm", "emergencyType", "description", "location", "patientAge", "patientGender", "symptoms", "urgency", "contactNumber", "additionalInfo", "isSubmitting", "setIsSubmitting", "submissionStatus", "setSubmissionStatus", "userLocation", "setUserLocation", "isVoiceRecording", "setIsVoiceRecording", "voiceTranscript", "setVoiceTranscript", "emergencyTypes", "id", "name", "icon", "priority", "commonSymptoms", "timer", "setInterval", "clearInterval", "navigator", "geolocation", "getCurrentPosition", "position", "lat", "coords", "latitude", "lng", "longitude", "error", "console", "log", "handleInputChange", "field", "value", "prev", "handleSymptomToggle", "symptom", "includes", "filter", "s", "handleEmergencyTypeSelect", "type", "startVoiceRecording", "setTimeout", "submitEmergencyRequest", "emergencyId", "now", "toString", "slice", "success", "estimatedArrival", "assignedAmbulance", "message", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "onClick", "disabled", "onChange", "e", "target", "placeholder", "toFixed", "rows", "level", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/components/MobileApp.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './MobileApp.css';\n\nconst MobileApp = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [emergencyForm, setEmergencyForm] = useState({\n    emergencyType: '',\n    description: '',\n    location: '',\n    patientAge: '',\n    patientGender: '',\n    symptoms: [],\n    urgency: 'medium',\n    contactNumber: '',\n    additionalInfo: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submissionStatus, setSubmissionStatus] = useState(null);\n  const [userLocation, setUserLocation] = useState(null);\n  const [isVoiceRecording, setIsVoiceRecording] = useState(false);\n  const [voiceTranscript, setVoiceTranscript] = useState('');\n\n  // Emergency types with icons\n  const emergencyTypes = [\n    { id: 'cardiac', name: 'Heart Attack / Cardiac Emergency', icon: '💓', priority: 'critical' },\n    { id: 'accident', name: 'Road Accident / Trauma', icon: '🚗', priority: 'high' },\n    { id: 'breathing', name: 'Breathing Difficulty', icon: '🫁', priority: 'high' },\n    { id: 'stroke', name: 'Stroke / Neurological', icon: '🧠', priority: 'critical' },\n    { id: 'overdose', name: 'Poisoning / Overdose', icon: '💊', priority: 'high' },\n    { id: 'injury', name: 'Severe Injury / Bleeding', icon: '🩸', priority: 'medium' },\n    { id: 'pregnancy', name: 'Pregnancy Emergency', icon: '🤱', priority: 'high' },\n    { id: 'other', name: 'Other Emergency', icon: '🚨', priority: 'medium' }\n  ];\n\n  // Common symptoms\n  const commonSymptoms = [\n    'Chest pain', 'Difficulty breathing', 'Unconscious', 'Severe bleeding',\n    'Broken bones', 'Severe pain', 'Nausea/Vomiting', 'Dizziness',\n    'High fever', 'Seizure', 'Confusion', 'Weakness'\n  ];\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          setUserLocation({\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          });\n        },\n        (error) => {\n          console.log('Location access denied:', error);\n        }\n      );\n    }\n  }, []);\n\n  const handleInputChange = (field, value) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSymptomToggle = (symptom) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      symptoms: prev.symptoms.includes(symptom)\n        ? prev.symptoms.filter(s => s !== symptom)\n        : [...prev.symptoms, symptom]\n    }));\n  };\n\n  const handleEmergencyTypeSelect = (type) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      emergencyType: type.id,\n      urgency: type.priority\n    }));\n  };\n\n  const startVoiceRecording = () => {\n    setIsVoiceRecording(true);\n    // Simulate voice recording\n    setTimeout(() => {\n      setVoiceTranscript(\"Help! I need an ambulance at Marina Beach. My friend is having chest pain and difficulty breathing.\");\n      setIsVoiceRecording(false);\n      // Auto-fill form based on voice\n      setEmergencyForm(prev => ({\n        ...prev,\n        emergencyType: 'cardiac',\n        description: 'Chest pain and difficulty breathing',\n        location: 'Marina Beach',\n        symptoms: ['Chest pain', 'Difficulty breathing'],\n        urgency: 'critical'\n      }));\n    }, 3000);\n  };\n\n  const submitEmergencyRequest = async () => {\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      const emergencyId = `EMG${Date.now().toString().slice(-3)}`;\n      setSubmissionStatus({\n        success: true,\n        emergencyId,\n        estimatedArrival: '8-12 minutes',\n        assignedAmbulance: 'AMB001',\n        message: 'Emergency request submitted successfully! Ambulance is being dispatched.'\n      });\n      setIsSubmitting(false);\n    }, 2000);\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"mobile-app-container\">\n      <div className=\"mobile-app-header\">\n        <h1>📱 Emergency Mobile App</h1>\n        <div className=\"app-time\">{formatTime(currentTime)}</div>\n      </div>\n\n      {!submissionStatus ? (\n        <div className=\"emergency-request-form\">\n          <div className=\"form-section\">\n            <h2>🚨 Request Emergency Ambulance</h2>\n            <p className=\"form-description\">\n              Fill out this form to request immediate medical assistance. \n              Your location will be automatically detected if available.\n            </p>\n\n            {/* Emergency Type Selection */}\n            <div className=\"form-group\">\n              <label>Emergency Type *</label>\n              <div className=\"emergency-types-grid\">\n                {emergencyTypes.map(type => (\n                  <button\n                    key={type.id}\n                    className={`emergency-type-btn ${emergencyForm.emergencyType === type.id ? 'selected' : ''}`}\n                    onClick={() => handleEmergencyTypeSelect(type)}\n                  >\n                    <span className=\"emergency-icon\">{type.icon}</span>\n                    <span className=\"emergency-name\">{type.name}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Voice Input */}\n            <div className=\"form-group\">\n              <label>🎤 Voice Description (Optional)</label>\n              <div className=\"voice-input-section\">\n                <button\n                  className={`voice-btn ${isVoiceRecording ? 'recording' : ''}`}\n                  onClick={startVoiceRecording}\n                  disabled={isVoiceRecording}\n                >\n                  {isVoiceRecording ? '🔴 Recording...' : '🎤 Describe Emergency'}\n                </button>\n                {voiceTranscript && (\n                  <div className=\"voice-transcript\">\n                    <strong>Transcript:</strong> {voiceTranscript}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Location */}\n            <div className=\"form-group\">\n              <label>Location *</label>\n              <input\n                type=\"text\"\n                value={emergencyForm.location}\n                onChange={(e) => handleInputChange('location', e.target.value)}\n                placeholder=\"Enter your current location or address\"\n                className=\"form-input\"\n              />\n              {userLocation && (\n                <div className=\"location-detected\">\n                  📍 Location detected: {userLocation.lat.toFixed(4)}, {userLocation.lng.toFixed(4)}\n                </div>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"form-group\">\n              <label>Emergency Description *</label>\n              <textarea\n                value={emergencyForm.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                placeholder=\"Describe what happened and current condition\"\n                className=\"form-textarea\"\n                rows=\"3\"\n              />\n            </div>\n\n            {/* Patient Information */}\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Patient Age</label>\n                <input\n                  type=\"number\"\n                  value={emergencyForm.patientAge}\n                  onChange={(e) => handleInputChange('patientAge', e.target.value)}\n                  placeholder=\"Age\"\n                  className=\"form-input\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender</label>\n                <select\n                  value={emergencyForm.patientGender}\n                  onChange={(e) => handleInputChange('patientGender', e.target.value)}\n                  className=\"form-select\"\n                >\n                  <option value=\"\">Select</option>\n                  <option value=\"male\">Male</option>\n                  <option value=\"female\">Female</option>\n                  <option value=\"other\">Other</option>\n                </select>\n              </div>\n            </div>\n\n            {/* Symptoms */}\n            <div className=\"form-group\">\n              <label>Symptoms (Select all that apply)</label>\n              <div className=\"symptoms-grid\">\n                {commonSymptoms.map(symptom => (\n                  <button\n                    key={symptom}\n                    className={`symptom-btn ${emergencyForm.symptoms.includes(symptom) ? 'selected' : ''}`}\n                    onClick={() => handleSymptomToggle(symptom)}\n                  >\n                    {symptom}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Contact Number */}\n            <div className=\"form-group\">\n              <label>Contact Number *</label>\n              <input\n                type=\"tel\"\n                value={emergencyForm.contactNumber}\n                onChange={(e) => handleInputChange('contactNumber', e.target.value)}\n                placeholder=\"+91 XXXXX XXXXX\"\n                className=\"form-input\"\n              />\n            </div>\n\n            {/* Urgency Level */}\n            <div className=\"form-group\">\n              <label>Urgency Level</label>\n              <div className=\"urgency-buttons\">\n                {['low', 'medium', 'high', 'critical'].map(level => (\n                  <button\n                    key={level}\n                    className={`urgency-btn ${emergencyForm.urgency === level ? 'selected' : ''} ${level}`}\n                    onClick={() => handleInputChange('urgency', level)}\n                  >\n                    {level.toUpperCase()}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <button\n              className=\"submit-emergency-btn\"\n              onClick={submitEmergencyRequest}\n              disabled={isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location || !emergencyForm.contactNumber}\n            >\n              {isSubmitting ? '🚑 Dispatching Ambulance...' : '🚨 REQUEST EMERGENCY AMBULANCE'}\n            </button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"submission-success\">\n          <div className=\"success-icon\">✅</div>\n          <h2>Emergency Request Submitted!</h2>\n          <div className=\"success-details\">\n            <div className=\"detail-item\">\n              <strong>Emergency ID:</strong> {submissionStatus.emergencyId}\n            </div>\n            <div className=\"detail-item\">\n              <strong>Assigned Ambulance:</strong> {submissionStatus.assignedAmbulance}\n            </div>\n            <div className=\"detail-item\">\n              <strong>Estimated Arrival:</strong> {submissionStatus.estimatedArrival}\n            </div>\n          </div>\n          <div className=\"success-message\">\n            {submissionStatus.message}\n          </div>\n          <button\n            className=\"new-request-btn\"\n            onClick={() => {\n              setSubmissionStatus(null);\n              setEmergencyForm({\n                emergencyType: '',\n                description: '',\n                location: '',\n                patientAge: '',\n                patientGender: '',\n                symptoms: [],\n                urgency: 'medium',\n                contactNumber: '',\n                additionalInfo: ''\n              });\n              setVoiceTranscript('');\n            }}\n          >\n            Submit New Request\n          </button>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default MobileApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC;IACjDW,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,QAAQ;IACjBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM8B,cAAc,GAAG,CACrB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,kCAAkC;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAW,CAAC,EAC7F;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAChF;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACjF;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAS,CAAC,EAClF;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC9E;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAS,CAAC,CACzE;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB,YAAY,EAAE,sBAAsB,EAAE,aAAa,EAAE,iBAAiB,EACtE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,WAAW,EAC7D,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CACjD;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,MAAMmC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B9B,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM8B,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIsC,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZjB,eAAe,CAAC;UACdkB,GAAG,EAAED,QAAQ,CAACE,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEJ,QAAQ,CAACE,MAAM,CAACG;QACvB,CAAC,CAAC;MACJ,CAAC,EACAC,KAAK,IAAK;QACTC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,KAAK,CAAC;MAC/C,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C3C,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,mBAAmB,GAAIC,OAAO,IAAK;IACvC9C,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPtC,QAAQ,EAAEsC,IAAI,CAACtC,QAAQ,CAACyC,QAAQ,CAACD,OAAO,CAAC,GACrCF,IAAI,CAACtC,QAAQ,CAAC0C,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,OAAO,CAAC,GACxC,CAAC,GAAGF,IAAI,CAACtC,QAAQ,EAAEwC,OAAO;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,yBAAyB,GAAIC,IAAI,IAAK;IAC1CnD,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP3C,aAAa,EAAEkD,IAAI,CAAC9B,EAAE;MACtBd,OAAO,EAAE4C,IAAI,CAAC3B;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAChCnC,mBAAmB,CAAC,IAAI,CAAC;IACzB;IACAoC,UAAU,CAAC,MAAM;MACflC,kBAAkB,CAAC,qGAAqG,CAAC;MACzHF,mBAAmB,CAAC,KAAK,CAAC;MAC1B;MACAjB,gBAAgB,CAAC4C,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP3C,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE,qCAAqC;QAClDC,QAAQ,EAAE,cAAc;QACxBG,QAAQ,EAAE,CAAC,YAAY,EAAE,sBAAsB,CAAC;QAChDC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM+C,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC3C,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA0C,UAAU,CAAC,MAAM;MACf,MAAME,WAAW,GAAG,MAAMzD,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3D7C,mBAAmB,CAAC;QAClB8C,OAAO,EAAE,IAAI;QACbJ,WAAW;QACXK,gBAAgB,EAAE,cAAc;QAChCC,iBAAiB,EAAE,QAAQ;QAC3BC,OAAO,EAAE;MACX,CAAC,CAAC;MACFnD,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMoD,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5E,OAAA;IAAK6E,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnC9E,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9E,OAAA;QAAA8E,QAAA,EAAI;MAAuB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChClF,OAAA;QAAK6E,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAER,UAAU,CAACnE,WAAW;MAAC;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,EAEL,CAAC/D,gBAAgB,gBAChBnB,OAAA;MAAK6E,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrC9E,OAAA;QAAK6E,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B9E,OAAA;UAAA8E,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvClF,OAAA;UAAG6E,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAGhC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAGJlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/BlF,OAAA;YAAK6E,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClCnD,cAAc,CAACwD,GAAG,CAACzB,IAAI,iBACtB1D,OAAA;cAEE6E,SAAS,EAAE,sBAAsBvE,aAAa,CAACE,aAAa,KAAKkD,IAAI,CAAC9B,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;cAC7FwD,OAAO,EAAEA,CAAA,KAAM3B,yBAAyB,CAACC,IAAI,CAAE;cAAAoB,QAAA,gBAE/C9E,OAAA;gBAAM6E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEpB,IAAI,CAAC5B;cAAI;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACnDlF,OAAA;gBAAM6E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAEpB,IAAI,CAAC7B;cAAI;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GAL9CxB,IAAI,CAAC9B,EAAE;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAMN,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9ClF,OAAA;YAAK6E,SAAS,EAAC,qBAAqB;YAAAC,QAAA,gBAClC9E,OAAA;cACE6E,SAAS,EAAE,aAAatD,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAG;cAC9D6D,OAAO,EAAEzB,mBAAoB;cAC7B0B,QAAQ,EAAE9D,gBAAiB;cAAAuD,QAAA,EAE1BvD,gBAAgB,GAAG,iBAAiB,GAAG;YAAuB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD,CAAC,EACRzD,eAAe,iBACdzB,OAAA;cAAK6E,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/B9E,OAAA;gBAAA8E,QAAA,EAAQ;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,KAAC,EAACzD,eAAe;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACzBlF,OAAA;YACE0D,IAAI,EAAC,MAAM;YACXR,KAAK,EAAE5C,aAAa,CAACI,QAAS;YAC9B4E,QAAQ,EAAGC,CAAC,IAAKvC,iBAAiB,CAAC,UAAU,EAAEuC,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;YAC/DuC,WAAW,EAAC,wCAAwC;YACpDZ,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,EACD7D,YAAY,iBACXrB,OAAA;YAAK6E,SAAS,EAAC,mBAAmB;YAAAC,QAAA,GAAC,kCACX,EAACzD,YAAY,CAACmB,GAAG,CAACkD,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACrE,YAAY,CAACsB,GAAG,CAAC+C,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9E,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACtClF,OAAA;YACEkD,KAAK,EAAE5C,aAAa,CAACG,WAAY;YACjC6E,QAAQ,EAAGC,CAAC,IAAKvC,iBAAiB,CAAC,aAAa,EAAEuC,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;YAClEuC,WAAW,EAAC,8CAA8C;YAC1DZ,SAAS,EAAC,eAAe;YACzBc,IAAI,EAAC;UAAG;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvB9E,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1BlF,OAAA;cACE0D,IAAI,EAAC,QAAQ;cACbR,KAAK,EAAE5C,aAAa,CAACK,UAAW;cAChC2E,QAAQ,EAAGC,CAAC,IAAKvC,iBAAiB,CAAC,YAAY,EAAEuC,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;cACjEuC,WAAW,EAAC,KAAK;cACjBZ,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrBlF,OAAA;cACEkD,KAAK,EAAE5C,aAAa,CAACM,aAAc;cACnC0E,QAAQ,EAAGC,CAAC,IAAKvC,iBAAiB,CAAC,eAAe,EAAEuC,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;cACpE2B,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAEvB9E,OAAA;gBAAQkD,KAAK,EAAC,EAAE;gBAAA4B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChClF,OAAA;gBAAQkD,KAAK,EAAC,MAAM;gBAAA4B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClClF,OAAA;gBAAQkD,KAAK,EAAC,QAAQ;gBAAA4B,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACtClF,OAAA;gBAAQkD,KAAK,EAAC,OAAO;gBAAA4B,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/ClF,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B9C,cAAc,CAACmD,GAAG,CAAC9B,OAAO,iBACzBrD,OAAA;cAEE6E,SAAS,EAAE,eAAevE,aAAa,CAACO,QAAQ,CAACyC,QAAQ,CAACD,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;cACvF+B,OAAO,EAAEA,CAAA,KAAMhC,mBAAmB,CAACC,OAAO,CAAE;cAAAyB,QAAA,EAE3CzB;YAAO,GAJHA,OAAO;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKN,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC/BlF,OAAA;YACE0D,IAAI,EAAC,KAAK;YACVR,KAAK,EAAE5C,aAAa,CAACS,aAAc;YACnCuE,QAAQ,EAAGC,CAAC,IAAKvC,iBAAiB,CAAC,eAAe,EAAEuC,CAAC,CAACC,MAAM,CAACtC,KAAK,CAAE;YACpEuC,WAAW,EAAC,iBAAiB;YAC7BZ,SAAS,EAAC;UAAY;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGNlF,OAAA;UAAK6E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzB9E,OAAA;YAAA8E,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BlF,OAAA;YAAK6E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAC7B,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAACK,GAAG,CAACS,KAAK,iBAC9C5F,OAAA;cAEE6E,SAAS,EAAE,eAAevE,aAAa,CAACQ,OAAO,KAAK8E,KAAK,GAAG,UAAU,GAAG,EAAE,IAAIA,KAAK,EAAG;cACvFR,OAAO,EAAEA,CAAA,KAAMpC,iBAAiB,CAAC,SAAS,EAAE4C,KAAK,CAAE;cAAAd,QAAA,EAElDc,KAAK,CAACC,WAAW,CAAC;YAAC,GAJfD,KAAK;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNlF,OAAA;UACE6E,SAAS,EAAC,sBAAsB;UAChCO,OAAO,EAAEvB,sBAAuB;UAChCwB,QAAQ,EAAEpE,YAAY,IAAI,CAACX,aAAa,CAACE,aAAa,IAAI,CAACF,aAAa,CAACI,QAAQ,IAAI,CAACJ,aAAa,CAACS,aAAc;UAAA+D,QAAA,EAEjH7D,YAAY,GAAG,6BAA6B,GAAG;QAAgC;UAAA8D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,gBAENlF,OAAA;MAAK6E,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjC9E,OAAA;QAAK6E,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACrClF,OAAA;QAAA8E,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrClF,OAAA;QAAK6E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B9E,OAAA;UAAK6E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9E,OAAA;YAAA8E,QAAA,EAAQ;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAAC2C,WAAW;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNlF,OAAA;UAAK6E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9E,OAAA;YAAA8E,QAAA,EAAQ;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACiD,iBAAiB;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACNlF,OAAA;UAAK6E,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9E,OAAA;YAAA8E,QAAA,EAAQ;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACgD,gBAAgB;QAAA;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACNlF,OAAA;QAAK6E,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAC7B3D,gBAAgB,CAACkD;MAAO;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC,eACNlF,OAAA;QACE6E,SAAS,EAAC,iBAAiB;QAC3BO,OAAO,EAAEA,CAAA,KAAM;UACbhE,mBAAmB,CAAC,IAAI,CAAC;UACzBb,gBAAgB,CAAC;YACfC,aAAa,EAAE,EAAE;YACjBC,WAAW,EAAE,EAAE;YACfC,QAAQ,EAAE,EAAE;YACZC,UAAU,EAAE,EAAE;YACdC,aAAa,EAAE,EAAE;YACjBC,QAAQ,EAAE,EAAE;YACZC,OAAO,EAAE,QAAQ;YACjBC,aAAa,EAAE,EAAE;YACjBC,cAAc,EAAE;UAClB,CAAC,CAAC;UACFU,kBAAkB,CAAC,EAAE,CAAC;QACxB,CAAE;QAAAoD,QAAA,EACH;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAChF,EAAA,CA/UID,SAAS;AAAA6F,EAAA,GAAT7F,SAAS;AAiVf,eAAeA,SAAS;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}