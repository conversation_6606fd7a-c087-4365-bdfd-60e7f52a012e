from fastapi import FastAP<PERSON>, UploadFile, File
from starlette.responses import JSONResponse
import numpy as np

app = FastAPI()

@app.post("/upload/")
async def upload_image(file: UploadFile = File(...)):
    # Simulating an issue where path_data might be a NumPy integer
    path_data = np.int32(123)  # Example NumPy integer

    # Convert NumPy int to a standard Python int
    path_data = int(path_data)

    # Print for debugging
    print(f"Path Data Type: {type(path_data)}, Value: {path_data}")

    return JSONResponse(content={"path": path_data})

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000, reload=True)
