{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\components\\\\MobileApp.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './MobileApp.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MobileApp = () => {\n  _s();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [emergencyForm, setEmergencyForm] = useState({\n    emergencyType: '',\n    description: '',\n    location: '',\n    patientAge: '',\n    patientGender: '',\n    symptoms: [],\n    urgency: 'medium',\n    contactNumber: '',\n    additionalInfo: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submissionStatus, setSubmissionStatus] = useState(null);\n  const [userLocation, setUserLocation] = useState(null);\n  const [isVoiceRecording, setIsVoiceRecording] = useState(false);\n  const [voiceTranscript, setVoiceTranscript] = useState('');\n\n  // Emergency types with icons\n  const emergencyTypes = [{\n    id: 'cardiac',\n    name: 'Heart Attack / Cardiac Emergency',\n    icon: '💓',\n    priority: 'critical'\n  }, {\n    id: 'accident',\n    name: 'Road Accident / Trauma',\n    icon: '🚗',\n    priority: 'high'\n  }, {\n    id: 'breathing',\n    name: 'Breathing Difficulty',\n    icon: '🫁',\n    priority: 'high'\n  }, {\n    id: 'stroke',\n    name: 'Stroke / Neurological',\n    icon: '🧠',\n    priority: 'critical'\n  }, {\n    id: 'overdose',\n    name: 'Poisoning / Overdose',\n    icon: '💊',\n    priority: 'high'\n  }, {\n    id: 'injury',\n    name: 'Severe Injury / Bleeding',\n    icon: '🩸',\n    priority: 'medium'\n  }, {\n    id: 'pregnancy',\n    name: 'Pregnancy Emergency',\n    icon: '🤱',\n    priority: 'high'\n  }, {\n    id: 'other',\n    name: 'Other Emergency',\n    icon: '🚨',\n    priority: 'medium'\n  }];\n\n  // Common symptoms\n  const commonSymptoms = ['Chest pain', 'Difficulty breathing', 'Unconscious', 'Severe bleeding', 'Broken bones', 'Severe pain', 'Nausea/Vomiting', 'Dizziness', 'High fever', 'Seizure', 'Confusion', 'Weakness'];\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(position => {\n        setUserLocation({\n          lat: position.coords.latitude,\n          lng: position.coords.longitude\n        });\n      }, error => {\n        console.log('Location access denied:', error);\n      });\n    }\n  }, []);\n  const handleInputChange = (field, value) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n  const handleSymptomToggle = symptom => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      symptoms: prev.symptoms.includes(symptom) ? prev.symptoms.filter(s => s !== symptom) : [...prev.symptoms, symptom]\n    }));\n  };\n  const handleEmergencyTypeSelect = type => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      emergencyType: type.id,\n      urgency: type.priority\n    }));\n  };\n  const startVoiceRecording = () => {\n    setIsVoiceRecording(true);\n    // Simulate voice recording\n    setTimeout(() => {\n      setVoiceTranscript(\"Help! I need an ambulance at Marina Beach. My friend is having chest pain and difficulty breathing.\");\n      setIsVoiceRecording(false);\n      // Auto-fill form based on voice\n      setEmergencyForm(prev => ({\n        ...prev,\n        emergencyType: 'cardiac',\n        description: 'Chest pain and difficulty breathing',\n        location: 'Marina Beach',\n        symptoms: ['Chest pain', 'Difficulty breathing'],\n        urgency: 'critical'\n      }));\n    }, 3000);\n  };\n  const submitEmergencyRequest = async () => {\n    setIsSubmitting(true);\n\n    // Simulate API call\n    setTimeout(() => {\n      const emergencyId = `EMG${Date.now().toString().slice(-3)}`;\n      setSubmissionStatus({\n        success: true,\n        emergencyId,\n        estimatedArrival: '8-12 minutes',\n        assignedAmbulance: 'AMB001',\n        message: 'Emergency request submitted successfully! Ambulance is being dispatched.'\n      });\n      setIsSubmitting(false);\n    }, 2000);\n  };\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mobile-app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-app-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83D\\uDCF1 Emergency Mobile App Demo\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"app-time\",\n        children: formatTime(currentTime)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 139,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mobile-demo-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-description\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCF1 Interactive Mobile App Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Experience our emergency ambulance request app in a realistic mobile interface. This demo shows how citizens can quickly request emergency medical assistance through our user-friendly mobile application.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-phone-frame\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"phone-screen\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-status-bar\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"signal-bars\",\n                children: \"\\uD83D\\uDCF6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"carrier\",\n                children: \"Emergency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"phone-time\",\n                children: formatTime(currentTime)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"status-right\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"battery\",\n                children: \"\\uD83D\\uDD0B 85%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-content\",\n            children: !submissionStatus ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mobile-app-interface\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"app-title\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"app-icon\",\n                    children: \"\\uD83D\\uDEA8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    children: \"Emergency SOS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"location-indicator\",\n                  children: \"\\uD83D\\uDCCD Chennai, Tamil Nadu\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"quick-emergency-buttons\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"quick-btn critical\",\n                  onClick: () => {\n                    setEmergencyForm(prev => ({\n                      ...prev,\n                      emergencyType: 'cardiac',\n                      urgency: 'critical',\n                      description: 'Heart attack emergency'\n                    }));\n                  },\n                  children: \"\\uD83D\\uDC93 Heart Attack\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 178,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"quick-btn high\",\n                  onClick: () => {\n                    setEmergencyForm(prev => ({\n                      ...prev,\n                      emergencyType: 'accident',\n                      urgency: 'high',\n                      description: 'Road accident with injuries'\n                    }));\n                  },\n                  children: \"\\uD83D\\uDE97 Accident\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 191,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"quick-btn high\",\n                  onClick: () => {\n                    setEmergencyForm(prev => ({\n                      ...prev,\n                      emergencyType: 'breathing',\n                      urgency: 'high',\n                      description: 'Severe breathing difficulty'\n                    }));\n                  },\n                  children: \"\\uD83E\\uDEC1 Breathing\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-form-mobile\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Emergency Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 221,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                    value: emergencyForm.emergencyType,\n                    onChange: e => handleInputChange('emergencyType', e.target.value),\n                    className: \"mobile-select\",\n                    children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                      value: \"\",\n                      children: \"Select Emergency Type\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 227,\n                      columnNumber: 25\n                    }, this), emergencyTypes.map(type => /*#__PURE__*/_jsxDEV(\"option\", {\n                      value: type.id,\n                      children: [type.icon, \" \", type.name]\n                    }, type.id, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 229,\n                      columnNumber: 27\n                    }, this))]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Your Location\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: emergencyForm.location,\n                    onChange: e => handleInputChange('location', e.target.value),\n                    placeholder: \"Current location...\",\n                    className: \"mobile-input\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"location-btn\",\n                    children: \"\\uD83D\\uDCCD Use GPS\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-group-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                    children: \"Describe Emergency\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                    value: emergencyForm.description,\n                    onChange: e => handleInputChange('description', e.target.value),\n                    placeholder: \"What happened? Current condition...\",\n                    className: \"mobile-textarea\",\n                    rows: \"3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 248,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"form-row-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group-mobile\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: \"Age\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 261,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      value: emergencyForm.patientAge,\n                      onChange: e => handleInputChange('patientAge', e.target.value),\n                      placeholder: \"Age\",\n                      className: \"mobile-input\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"form-group-mobile\",\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      children: \"Contact\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 271,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"tel\",\n                      value: emergencyForm.contactNumber,\n                      onChange: e => handleInputChange('contactNumber', e.target.value),\n                      placeholder: \"+91 XXXXX\",\n                      className: \"mobile-input\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 259,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"voice-section-mobile\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    className: `voice-btn-mobile ${isVoiceRecording ? 'recording' : ''}`,\n                    onClick: startVoiceRecording,\n                    disabled: isVoiceRecording,\n                    children: isVoiceRecording ? '🔴 Recording...' : '🎤 Voice Description'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 283,\n                    columnNumber: 23\n                  }, this), voiceTranscript && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"voice-transcript-mobile\",\n                    children: [\"\\\"\", voiceTranscript, \"\\\"\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 291,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 282,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"emergency-call-btn\",\n                  onClick: submitEmergencyRequest,\n                  disabled: isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location,\n                  children: isSubmitting ? '🚑 Calling Ambulance...' : '🚨 CALL AMBULANCE NOW'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"success-screen-mobile\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-animation\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"checkmark\",\n                  children: \"\\u2705\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"pulse-ring\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 310,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 308,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                children: \"Ambulance Dispatched!\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"success-details-mobile\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Emergency ID:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: submissionStatus.emergencyId\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 316,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Ambulance:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 319,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: submissionStatus.assignedAmbulance\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"detail-row\",\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"ETA:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 323,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: submissionStatus.estimatedArrival\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 324,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"tracking-section\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ambulance-icon\",\n                  children: \"\\uD83D\\uDE91\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"tracking-text\",\n                  children: \"Tracking ambulance in real-time...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"progress-bar\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"progress-fill\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 332,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"new-emergency-btn\",\n                onClick: () => {\n                  setSubmissionStatus(null);\n                  setEmergencyForm({\n                    emergencyType: '',\n                    description: '',\n                    location: '',\n                    patientAge: '',\n                    patientGender: '',\n                    symptoms: [],\n                    urgency: 'medium',\n                    contactNumber: '',\n                    additionalInfo: ''\n                  });\n                  setVoiceTranscript('');\n                },\n                children: \"New Emergency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 336,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"phone-home-indicator\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"desktop-form-section\",\n      children: !submissionStatus ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"emergency-request-form\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            children: \"\\uD83D\\uDEA8 Request Emergency Ambulance (Desktop View)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"form-description\",\n            children: \"Fill out this form to request immediate medical assistance. Your location will be automatically detected if available.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Emergency Type *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"emergency-types-grid\",\n              children: emergencyTypes.map(type => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `emergency-type-btn ${emergencyForm.emergencyType === type.id ? 'selected' : ''}`,\n                onClick: () => handleEmergencyTypeSelect(type),\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"emergency-icon\",\n                  children: type.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"emergency-name\",\n                  children: type.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 21\n                }, this)]\n              }, type.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 381,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"\\uD83C\\uDFA4 Voice Description (Optional)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"voice-input-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                className: `voice-btn ${isVoiceRecording ? 'recording' : ''}`,\n                onClick: startVoiceRecording,\n                disabled: isVoiceRecording,\n                children: isVoiceRecording ? '🔴 Recording...' : '🎤 Describe Emergency'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), voiceTranscript && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"voice-transcript\",\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: \"Transcript:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 21\n                }, this), \" \", voiceTranscript]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Location *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: emergencyForm.location,\n              onChange: e => handleInputChange('location', e.target.value),\n              placeholder: \"Enter your current location or address\",\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this), userLocation && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"location-detected\",\n              children: [\"\\uD83D\\uDCCD Location detected: \", userLocation.lat.toFixed(4), \", \", userLocation.lng.toFixed(4)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Emergency Description *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n              value: emergencyForm.description,\n              onChange: e => handleInputChange('description', e.target.value),\n              placeholder: \"Describe what happened and current condition\",\n              className: \"form-textarea\",\n              rows: \"3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 430,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-row\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Patient Age\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 444,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                value: emergencyForm.patientAge,\n                onChange: e => handleInputChange('patientAge', e.target.value),\n                placeholder: \"Age\",\n                className: \"form-input\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                children: \"Gender\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: emergencyForm.patientGender,\n                onChange: e => handleInputChange('patientGender', e.target.value),\n                className: \"form-select\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"male\",\n                  children: \"Male\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"female\",\n                  children: \"Female\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 462,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 442,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Symptoms (Select all that apply)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"symptoms-grid\",\n              children: commonSymptoms.map(symptom => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `symptom-btn ${emergencyForm.symptoms.includes(symptom) ? 'selected' : ''}`,\n                onClick: () => handleSymptomToggle(symptom),\n                children: symptom\n              }, symptom, false, {\n                fileName: _jsxFileName,\n                lineNumber: 473,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Contact Number *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"tel\",\n              value: emergencyForm.contactNumber,\n              onChange: e => handleInputChange('contactNumber', e.target.value),\n              placeholder: \"+91 XXXXX XXXXX\",\n              className: \"form-input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Urgency Level\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"urgency-buttons\",\n              children: ['low', 'medium', 'high', 'critical'].map(level => /*#__PURE__*/_jsxDEV(\"button\", {\n                className: `urgency-btn ${emergencyForm.urgency === level ? 'selected' : ''} ${level}`,\n                onClick: () => handleInputChange('urgency', level),\n                children: level.toUpperCase()\n              }, level, false, {\n                fileName: _jsxFileName,\n                lineNumber: 501,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"submit-emergency-btn\",\n            onClick: submitEmergencyRequest,\n            disabled: isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location || !emergencyForm.contactNumber,\n            children: isSubmitting ? '🚑 Dispatching Ambulance...' : '🚨 REQUEST EMERGENCY AMBULANCE'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 369,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 368,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"submission-success\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-icon\",\n          children: \"\\u2705\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 524,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Emergency Request Submitted!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Emergency ID:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this), \" \", submissionStatus.emergencyId]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Assigned Ambulance:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 531,\n              columnNumber: 15\n            }, this), \" \", submissionStatus.assignedAmbulance]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"detail-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Estimated Arrival:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 15\n            }, this), \" \", submissionStatus.estimatedArrival]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"success-message\",\n          children: submissionStatus.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"new-request-btn\",\n          onClick: () => {\n            setSubmissionStatus(null);\n            setEmergencyForm({\n              emergencyType: '',\n              description: '',\n              location: '',\n              patientAge: '',\n              patientGender: '',\n              symptoms: [],\n              urgency: 'medium',\n              contactNumber: '',\n              additionalInfo: ''\n            });\n            setVoiceTranscript('');\n          },\n          children: \"Submit New Request\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n};\n_s(MobileApp, \"QB+94yed2EmGzqn5UUkGBGhZMms=\");\n_c = MobileApp;\nexport default MobileApp;\nvar _c;\n$RefreshReg$(_c, \"MobileApp\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "MobileApp", "_s", "currentTime", "setCurrentTime", "Date", "emergencyForm", "setEmergencyForm", "emergencyType", "description", "location", "patientAge", "patientGender", "symptoms", "urgency", "contactNumber", "additionalInfo", "isSubmitting", "setIsSubmitting", "submissionStatus", "setSubmissionStatus", "userLocation", "setUserLocation", "isVoiceRecording", "setIsVoiceRecording", "voiceTranscript", "setVoiceTranscript", "emergencyTypes", "id", "name", "icon", "priority", "commonSymptoms", "timer", "setInterval", "clearInterval", "navigator", "geolocation", "getCurrentPosition", "position", "lat", "coords", "latitude", "lng", "longitude", "error", "console", "log", "handleInputChange", "field", "value", "prev", "handleSymptomToggle", "symptom", "includes", "filter", "s", "handleEmergencyTypeSelect", "type", "startVoiceRecording", "setTimeout", "submitEmergencyRequest", "emergencyId", "now", "toString", "slice", "success", "estimatedArrival", "assignedAmbulance", "message", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onChange", "e", "target", "map", "placeholder", "rows", "disabled", "toFixed", "level", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/components/MobileApp.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './MobileApp.css';\n\nconst MobileApp = () => {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [emergencyForm, setEmergencyForm] = useState({\n    emergencyType: '',\n    description: '',\n    location: '',\n    patientAge: '',\n    patientGender: '',\n    symptoms: [],\n    urgency: 'medium',\n    contactNumber: '',\n    additionalInfo: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submissionStatus, setSubmissionStatus] = useState(null);\n  const [userLocation, setUserLocation] = useState(null);\n  const [isVoiceRecording, setIsVoiceRecording] = useState(false);\n  const [voiceTranscript, setVoiceTranscript] = useState('');\n\n  // Emergency types with icons\n  const emergencyTypes = [\n    { id: 'cardiac', name: 'Heart Attack / Cardiac Emergency', icon: '💓', priority: 'critical' },\n    { id: 'accident', name: 'Road Accident / Trauma', icon: '🚗', priority: 'high' },\n    { id: 'breathing', name: 'Breathing Difficulty', icon: '🫁', priority: 'high' },\n    { id: 'stroke', name: 'Stroke / Neurological', icon: '🧠', priority: 'critical' },\n    { id: 'overdose', name: 'Poisoning / Overdose', icon: '💊', priority: 'high' },\n    { id: 'injury', name: 'Severe Injury / Bleeding', icon: '🩸', priority: 'medium' },\n    { id: 'pregnancy', name: 'Pregnancy Emergency', icon: '🤱', priority: 'high' },\n    { id: 'other', name: 'Other Emergency', icon: '🚨', priority: 'medium' }\n  ];\n\n  // Common symptoms\n  const commonSymptoms = [\n    'Chest pain', 'Difficulty breathing', 'Unconscious', 'Severe bleeding',\n    'Broken bones', 'Severe pain', 'Nausea/Vomiting', 'Dizziness',\n    'High fever', 'Seizure', 'Confusion', 'Weakness'\n  ];\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Get user location\n  useEffect(() => {\n    if (navigator.geolocation) {\n      navigator.geolocation.getCurrentPosition(\n        (position) => {\n          setUserLocation({\n            lat: position.coords.latitude,\n            lng: position.coords.longitude\n          });\n        },\n        (error) => {\n          console.log('Location access denied:', error);\n        }\n      );\n    }\n  }, []);\n\n  const handleInputChange = (field, value) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleSymptomToggle = (symptom) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      symptoms: prev.symptoms.includes(symptom)\n        ? prev.symptoms.filter(s => s !== symptom)\n        : [...prev.symptoms, symptom]\n    }));\n  };\n\n  const handleEmergencyTypeSelect = (type) => {\n    setEmergencyForm(prev => ({\n      ...prev,\n      emergencyType: type.id,\n      urgency: type.priority\n    }));\n  };\n\n  const startVoiceRecording = () => {\n    setIsVoiceRecording(true);\n    // Simulate voice recording\n    setTimeout(() => {\n      setVoiceTranscript(\"Help! I need an ambulance at Marina Beach. My friend is having chest pain and difficulty breathing.\");\n      setIsVoiceRecording(false);\n      // Auto-fill form based on voice\n      setEmergencyForm(prev => ({\n        ...prev,\n        emergencyType: 'cardiac',\n        description: 'Chest pain and difficulty breathing',\n        location: 'Marina Beach',\n        symptoms: ['Chest pain', 'Difficulty breathing'],\n        urgency: 'critical'\n      }));\n    }, 3000);\n  };\n\n  const submitEmergencyRequest = async () => {\n    setIsSubmitting(true);\n    \n    // Simulate API call\n    setTimeout(() => {\n      const emergencyId = `EMG${Date.now().toString().slice(-3)}`;\n      setSubmissionStatus({\n        success: true,\n        emergencyId,\n        estimatedArrival: '8-12 minutes',\n        assignedAmbulance: 'AMB001',\n        message: 'Emergency request submitted successfully! Ambulance is being dispatched.'\n      });\n      setIsSubmitting(false);\n    }, 2000);\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  return (\n    <div className=\"mobile-app-container\">\n      <div className=\"mobile-app-header\">\n        <h1>📱 Emergency Mobile App Demo</h1>\n        <div className=\"app-time\">{formatTime(currentTime)}</div>\n      </div>\n\n      <div className=\"mobile-demo-section\">\n        <div className=\"demo-description\">\n          <h2>📱 Interactive Mobile App Demo</h2>\n          <p>Experience our emergency ambulance request app in a realistic mobile interface. This demo shows how citizens can quickly request emergency medical assistance through our user-friendly mobile application.</p>\n        </div>\n\n        {/* Mobile Phone Frame */}\n        <div className=\"mobile-phone-frame\">\n          <div className=\"phone-screen\">\n            <div className=\"phone-status-bar\">\n              <div className=\"status-left\">\n                <span className=\"signal-bars\">📶</span>\n                <span className=\"carrier\">Emergency</span>\n              </div>\n              <div className=\"status-center\">\n                <span className=\"phone-time\">{formatTime(currentTime)}</span>\n              </div>\n              <div className=\"status-right\">\n                <span className=\"battery\">🔋 85%</span>\n              </div>\n            </div>\n\n            <div className=\"phone-content\">\n              {!submissionStatus ? (\n                <div className=\"mobile-app-interface\">\n                  <div className=\"app-header\">\n                    <div className=\"app-title\">\n                      <span className=\"app-icon\">🚨</span>\n                      <h3>Emergency SOS</h3>\n                    </div>\n                    <div className=\"location-indicator\">\n                      📍 Chennai, Tamil Nadu\n                    </div>\n                  </div>\n\n                  <div className=\"quick-emergency-buttons\">\n                    <button\n                      className=\"quick-btn critical\"\n                      onClick={() => {\n                        setEmergencyForm(prev => ({\n                          ...prev,\n                          emergencyType: 'cardiac',\n                          urgency: 'critical',\n                          description: 'Heart attack emergency'\n                        }));\n                      }}\n                    >\n                      💓 Heart Attack\n                    </button>\n                    <button\n                      className=\"quick-btn high\"\n                      onClick={() => {\n                        setEmergencyForm(prev => ({\n                          ...prev,\n                          emergencyType: 'accident',\n                          urgency: 'high',\n                          description: 'Road accident with injuries'\n                        }));\n                      }}\n                    >\n                      🚗 Accident\n                    </button>\n                    <button\n                      className=\"quick-btn high\"\n                      onClick={() => {\n                        setEmergencyForm(prev => ({\n                          ...prev,\n                          emergencyType: 'breathing',\n                          urgency: 'high',\n                          description: 'Severe breathing difficulty'\n                        }));\n                      }}\n                    >\n                      🫁 Breathing\n                    </button>\n                  </div>\n\n                  <div className=\"emergency-form-mobile\">\n                    <div className=\"form-group-mobile\">\n                      <label>Emergency Type</label>\n                      <select\n                        value={emergencyForm.emergencyType}\n                        onChange={(e) => handleInputChange('emergencyType', e.target.value)}\n                        className=\"mobile-select\"\n                      >\n                        <option value=\"\">Select Emergency Type</option>\n                        {emergencyTypes.map(type => (\n                          <option key={type.id} value={type.id}>\n                            {type.icon} {type.name}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div className=\"form-group-mobile\">\n                      <label>Your Location</label>\n                      <input\n                        type=\"text\"\n                        value={emergencyForm.location}\n                        onChange={(e) => handleInputChange('location', e.target.value)}\n                        placeholder=\"Current location...\"\n                        className=\"mobile-input\"\n                      />\n                      <button className=\"location-btn\">📍 Use GPS</button>\n                    </div>\n\n                    <div className=\"form-group-mobile\">\n                      <label>Describe Emergency</label>\n                      <textarea\n                        value={emergencyForm.description}\n                        onChange={(e) => handleInputChange('description', e.target.value)}\n                        placeholder=\"What happened? Current condition...\"\n                        className=\"mobile-textarea\"\n                        rows=\"3\"\n                      />\n                    </div>\n\n                    <div className=\"form-row-mobile\">\n                      <div className=\"form-group-mobile\">\n                        <label>Age</label>\n                        <input\n                          type=\"number\"\n                          value={emergencyForm.patientAge}\n                          onChange={(e) => handleInputChange('patientAge', e.target.value)}\n                          placeholder=\"Age\"\n                          className=\"mobile-input\"\n                        />\n                      </div>\n                      <div className=\"form-group-mobile\">\n                        <label>Contact</label>\n                        <input\n                          type=\"tel\"\n                          value={emergencyForm.contactNumber}\n                          onChange={(e) => handleInputChange('contactNumber', e.target.value)}\n                          placeholder=\"+91 XXXXX\"\n                          className=\"mobile-input\"\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"voice-section-mobile\">\n                      <button\n                        className={`voice-btn-mobile ${isVoiceRecording ? 'recording' : ''}`}\n                        onClick={startVoiceRecording}\n                        disabled={isVoiceRecording}\n                      >\n                        {isVoiceRecording ? '🔴 Recording...' : '🎤 Voice Description'}\n                      </button>\n                      {voiceTranscript && (\n                        <div className=\"voice-transcript-mobile\">\n                          \"{voiceTranscript}\"\n                        </div>\n                      )}\n                    </div>\n\n                    <button\n                      className=\"emergency-call-btn\"\n                      onClick={submitEmergencyRequest}\n                      disabled={isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location}\n                    >\n                      {isSubmitting ? '🚑 Calling Ambulance...' : '🚨 CALL AMBULANCE NOW'}\n                    </button>\n                  </div>\n                </div>\n              ) : (\n                <div className=\"success-screen-mobile\">\n                  <div className=\"success-animation\">\n                    <div className=\"checkmark\">✅</div>\n                    <div className=\"pulse-ring\"></div>\n                  </div>\n                  <h3>Ambulance Dispatched!</h3>\n                  <div className=\"success-details-mobile\">\n                    <div className=\"detail-row\">\n                      <span>Emergency ID:</span>\n                      <span>{submissionStatus.emergencyId}</span>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>Ambulance:</span>\n                      <span>{submissionStatus.assignedAmbulance}</span>\n                    </div>\n                    <div className=\"detail-row\">\n                      <span>ETA:</span>\n                      <span>{submissionStatus.estimatedArrival}</span>\n                    </div>\n                  </div>\n\n                  <div className=\"tracking-section\">\n                    <div className=\"ambulance-icon\">🚑</div>\n                    <div className=\"tracking-text\">Tracking ambulance in real-time...</div>\n                    <div className=\"progress-bar\">\n                      <div className=\"progress-fill\"></div>\n                    </div>\n                  </div>\n\n                  <button\n                    className=\"new-emergency-btn\"\n                    onClick={() => {\n                      setSubmissionStatus(null);\n                      setEmergencyForm({\n                        emergencyType: '',\n                        description: '',\n                        location: '',\n                        patientAge: '',\n                        patientGender: '',\n                        symptoms: [],\n                        urgency: 'medium',\n                        contactNumber: '',\n                        additionalInfo: ''\n                      });\n                      setVoiceTranscript('');\n                    }}\n                  >\n                    New Emergency\n                  </button>\n                </div>\n              )}\n            </div>\n\n            <div className=\"phone-home-indicator\"></div>\n          </div>\n        </div>\n      </div>\n\n      {/* Desktop Form (Original) */}\n      <div className=\"desktop-form-section\">\n        {!submissionStatus ? (\n          <div className=\"emergency-request-form\">\n            <div className=\"form-section\">\n              <h2>🚨 Request Emergency Ambulance (Desktop View)</h2>\n              <p className=\"form-description\">\n                Fill out this form to request immediate medical assistance.\n                Your location will be automatically detected if available.\n              </p>\n\n            {/* Emergency Type Selection */}\n            <div className=\"form-group\">\n              <label>Emergency Type *</label>\n              <div className=\"emergency-types-grid\">\n                {emergencyTypes.map(type => (\n                  <button\n                    key={type.id}\n                    className={`emergency-type-btn ${emergencyForm.emergencyType === type.id ? 'selected' : ''}`}\n                    onClick={() => handleEmergencyTypeSelect(type)}\n                  >\n                    <span className=\"emergency-icon\">{type.icon}</span>\n                    <span className=\"emergency-name\">{type.name}</span>\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Voice Input */}\n            <div className=\"form-group\">\n              <label>🎤 Voice Description (Optional)</label>\n              <div className=\"voice-input-section\">\n                <button\n                  className={`voice-btn ${isVoiceRecording ? 'recording' : ''}`}\n                  onClick={startVoiceRecording}\n                  disabled={isVoiceRecording}\n                >\n                  {isVoiceRecording ? '🔴 Recording...' : '🎤 Describe Emergency'}\n                </button>\n                {voiceTranscript && (\n                  <div className=\"voice-transcript\">\n                    <strong>Transcript:</strong> {voiceTranscript}\n                  </div>\n                )}\n              </div>\n            </div>\n\n            {/* Location */}\n            <div className=\"form-group\">\n              <label>Location *</label>\n              <input\n                type=\"text\"\n                value={emergencyForm.location}\n                onChange={(e) => handleInputChange('location', e.target.value)}\n                placeholder=\"Enter your current location or address\"\n                className=\"form-input\"\n              />\n              {userLocation && (\n                <div className=\"location-detected\">\n                  📍 Location detected: {userLocation.lat.toFixed(4)}, {userLocation.lng.toFixed(4)}\n                </div>\n              )}\n            </div>\n\n            {/* Description */}\n            <div className=\"form-group\">\n              <label>Emergency Description *</label>\n              <textarea\n                value={emergencyForm.description}\n                onChange={(e) => handleInputChange('description', e.target.value)}\n                placeholder=\"Describe what happened and current condition\"\n                className=\"form-textarea\"\n                rows=\"3\"\n              />\n            </div>\n\n            {/* Patient Information */}\n            <div className=\"form-row\">\n              <div className=\"form-group\">\n                <label>Patient Age</label>\n                <input\n                  type=\"number\"\n                  value={emergencyForm.patientAge}\n                  onChange={(e) => handleInputChange('patientAge', e.target.value)}\n                  placeholder=\"Age\"\n                  className=\"form-input\"\n                />\n              </div>\n              <div className=\"form-group\">\n                <label>Gender</label>\n                <select\n                  value={emergencyForm.patientGender}\n                  onChange={(e) => handleInputChange('patientGender', e.target.value)}\n                  className=\"form-select\"\n                >\n                  <option value=\"\">Select</option>\n                  <option value=\"male\">Male</option>\n                  <option value=\"female\">Female</option>\n                  <option value=\"other\">Other</option>\n                </select>\n              </div>\n            </div>\n\n            {/* Symptoms */}\n            <div className=\"form-group\">\n              <label>Symptoms (Select all that apply)</label>\n              <div className=\"symptoms-grid\">\n                {commonSymptoms.map(symptom => (\n                  <button\n                    key={symptom}\n                    className={`symptom-btn ${emergencyForm.symptoms.includes(symptom) ? 'selected' : ''}`}\n                    onClick={() => handleSymptomToggle(symptom)}\n                  >\n                    {symptom}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Contact Number */}\n            <div className=\"form-group\">\n              <label>Contact Number *</label>\n              <input\n                type=\"tel\"\n                value={emergencyForm.contactNumber}\n                onChange={(e) => handleInputChange('contactNumber', e.target.value)}\n                placeholder=\"+91 XXXXX XXXXX\"\n                className=\"form-input\"\n              />\n            </div>\n\n            {/* Urgency Level */}\n            <div className=\"form-group\">\n              <label>Urgency Level</label>\n              <div className=\"urgency-buttons\">\n                {['low', 'medium', 'high', 'critical'].map(level => (\n                  <button\n                    key={level}\n                    className={`urgency-btn ${emergencyForm.urgency === level ? 'selected' : ''} ${level}`}\n                    onClick={() => handleInputChange('urgency', level)}\n                  >\n                    {level.toUpperCase()}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <button\n              className=\"submit-emergency-btn\"\n              onClick={submitEmergencyRequest}\n              disabled={isSubmitting || !emergencyForm.emergencyType || !emergencyForm.location || !emergencyForm.contactNumber}\n            >\n              {isSubmitting ? '🚑 Dispatching Ambulance...' : '🚨 REQUEST EMERGENCY AMBULANCE'}\n            </button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"submission-success\">\n          <div className=\"success-icon\">✅</div>\n          <h2>Emergency Request Submitted!</h2>\n          <div className=\"success-details\">\n            <div className=\"detail-item\">\n              <strong>Emergency ID:</strong> {submissionStatus.emergencyId}\n            </div>\n            <div className=\"detail-item\">\n              <strong>Assigned Ambulance:</strong> {submissionStatus.assignedAmbulance}\n            </div>\n            <div className=\"detail-item\">\n              <strong>Estimated Arrival:</strong> {submissionStatus.estimatedArrival}\n            </div>\n          </div>\n          <div className=\"success-message\">\n            {submissionStatus.message}\n          </div>\n          <button\n            className=\"new-request-btn\"\n            onClick={() => {\n              setSubmissionStatus(null);\n              setEmergencyForm({\n                emergencyType: '',\n                description: '',\n                location: '',\n                patientAge: '',\n                patientGender: '',\n                symptoms: [],\n                urgency: 'medium',\n                contactNumber: '',\n                additionalInfo: ''\n              });\n              setVoiceTranscript('');\n            }}\n          >\n            Submit New Request\n          </button>\n        </div>\n      )}\n      </div>\n    </div>\n  );\n};\n\nexport default MobileApp;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAAC,IAAIQ,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,QAAQ,CAAC;IACjDW,aAAa,EAAE,EAAE;IACjBC,WAAW,EAAE,EAAE;IACfC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,EAAE;IACjBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,QAAQ;IACjBC,aAAa,EAAE,EAAE;IACjBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwB,YAAY,EAAEC,eAAe,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC4B,eAAe,EAAEC,kBAAkB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM8B,cAAc,GAAG,CACrB;IAAEC,EAAE,EAAE,SAAS;IAAEC,IAAI,EAAE,kCAAkC;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAW,CAAC,EAC7F;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAChF;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC/E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAW,CAAC,EACjF;IAAEH,EAAE,EAAE,UAAU;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC9E;IAAEH,EAAE,EAAE,QAAQ;IAAEC,IAAI,EAAE,0BAA0B;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAS,CAAC,EAClF;IAAEH,EAAE,EAAE,WAAW;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAO,CAAC,EAC9E;IAAEH,EAAE,EAAE,OAAO;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,IAAI;IAAEC,QAAQ,EAAE;EAAS,CAAC,CACzE;;EAED;EACA,MAAMC,cAAc,GAAG,CACrB,YAAY,EAAE,sBAAsB,EAAE,aAAa,EAAE,iBAAiB,EACtE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,WAAW,EAC7D,YAAY,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,CACjD;;EAED;EACAlC,SAAS,CAAC,MAAM;IACd,MAAMmC,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9B9B,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAM8B,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnC,SAAS,CAAC,MAAM;IACd,IAAIsC,SAAS,CAACC,WAAW,EAAE;MACzBD,SAAS,CAACC,WAAW,CAACC,kBAAkB,CACrCC,QAAQ,IAAK;QACZjB,eAAe,CAAC;UACdkB,GAAG,EAAED,QAAQ,CAACE,MAAM,CAACC,QAAQ;UAC7BC,GAAG,EAAEJ,QAAQ,CAACE,MAAM,CAACG;QACvB,CAAC,CAAC;MACJ,CAAC,EACAC,KAAK,IAAK;QACTC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,KAAK,CAAC;MAC/C,CACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1C3C,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP,CAACF,KAAK,GAAGC;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAME,mBAAmB,GAAIC,OAAO,IAAK;IACvC9C,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACPtC,QAAQ,EAAEsC,IAAI,CAACtC,QAAQ,CAACyC,QAAQ,CAACD,OAAO,CAAC,GACrCF,IAAI,CAACtC,QAAQ,CAAC0C,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKH,OAAO,CAAC,GACxC,CAAC,GAAGF,IAAI,CAACtC,QAAQ,EAAEwC,OAAO;IAChC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAMI,yBAAyB,GAAIC,IAAI,IAAK;IAC1CnD,gBAAgB,CAAC4C,IAAI,KAAK;MACxB,GAAGA,IAAI;MACP3C,aAAa,EAAEkD,IAAI,CAAC9B,EAAE;MACtBd,OAAO,EAAE4C,IAAI,CAAC3B;IAChB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAChCnC,mBAAmB,CAAC,IAAI,CAAC;IACzB;IACAoC,UAAU,CAAC,MAAM;MACflC,kBAAkB,CAAC,qGAAqG,CAAC;MACzHF,mBAAmB,CAAC,KAAK,CAAC;MAC1B;MACAjB,gBAAgB,CAAC4C,IAAI,KAAK;QACxB,GAAGA,IAAI;QACP3C,aAAa,EAAE,SAAS;QACxBC,WAAW,EAAE,qCAAqC;QAClDC,QAAQ,EAAE,cAAc;QACxBG,QAAQ,EAAE,CAAC,YAAY,EAAE,sBAAsB,CAAC;QAChDC,OAAO,EAAE;MACX,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAM+C,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzC3C,eAAe,CAAC,IAAI,CAAC;;IAErB;IACA0C,UAAU,CAAC,MAAM;MACf,MAAME,WAAW,GAAG,MAAMzD,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;MAC3D7C,mBAAmB,CAAC;QAClB8C,OAAO,EAAE,IAAI;QACbJ,WAAW;QACXK,gBAAgB,EAAE,cAAc;QAChCC,iBAAiB,EAAE,QAAQ;QAC3BC,OAAO,EAAE;MACX,CAAC,CAAC;MACFnD,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMoD,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACE5E,OAAA;IAAK6E,SAAS,EAAC,sBAAsB;IAAAC,QAAA,gBACnC9E,OAAA;MAAK6E,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChC9E,OAAA;QAAA8E,QAAA,EAAI;MAA4B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrClF,OAAA;QAAK6E,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAER,UAAU,CAACnE,WAAW;MAAC;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtD,CAAC,eAENlF,OAAA;MAAK6E,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClC9E,OAAA;QAAK6E,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9E,OAAA;UAAA8E,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvClF,OAAA;UAAA8E,QAAA,EAAG;QAA2M;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/M,CAAC,eAGNlF,OAAA;QAAK6E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,eACjC9E,OAAA;UAAK6E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9E,OAAA;YAAK6E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9E,OAAA;cAAK6E,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAC1B9E,OAAA;gBAAM6E,SAAS,EAAC,aAAa;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACvClF,OAAA;gBAAM6E,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5B9E,OAAA;gBAAM6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,EAAER,UAAU,CAACnE,WAAW;cAAC;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,cAAc;cAAAC,QAAA,eAC3B9E,OAAA;gBAAM6E,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC,eAAe;YAAAC,QAAA,EAC3B,CAAC3D,gBAAgB,gBAChBnB,OAAA;cAAK6E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnC9E,OAAA;gBAAK6E,SAAS,EAAC,YAAY;gBAAAC,QAAA,gBACzB9E,OAAA;kBAAK6E,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxB9E,OAAA;oBAAM6E,SAAS,EAAC,UAAU;oBAAAC,QAAA,EAAC;kBAAE;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACpClF,OAAA;oBAAA8E,QAAA,EAAI;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,oBAAoB;kBAAAC,QAAA,EAAC;gBAEpC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,gBACtC9E,OAAA;kBACE6E,SAAS,EAAC,oBAAoB;kBAC9BM,OAAO,EAAEA,CAAA,KAAM;oBACb5E,gBAAgB,CAAC4C,IAAI,KAAK;sBACxB,GAAGA,IAAI;sBACP3C,aAAa,EAAE,SAAS;sBACxBM,OAAO,EAAE,UAAU;sBACnBL,WAAW,EAAE;oBACf,CAAC,CAAC,CAAC;kBACL,CAAE;kBAAAqE,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlF,OAAA;kBACE6E,SAAS,EAAC,gBAAgB;kBAC1BM,OAAO,EAAEA,CAAA,KAAM;oBACb5E,gBAAgB,CAAC4C,IAAI,KAAK;sBACxB,GAAGA,IAAI;sBACP3C,aAAa,EAAE,UAAU;sBACzBM,OAAO,EAAE,MAAM;sBACfL,WAAW,EAAE;oBACf,CAAC,CAAC,CAAC;kBACL,CAAE;kBAAAqE,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlF,OAAA;kBACE6E,SAAS,EAAC,gBAAgB;kBAC1BM,OAAO,EAAEA,CAAA,KAAM;oBACb5E,gBAAgB,CAAC4C,IAAI,KAAK;sBACxB,GAAGA,IAAI;sBACP3C,aAAa,EAAE,WAAW;sBAC1BM,OAAO,EAAE,MAAM;sBACfL,WAAW,EAAE;oBACf,CAAC,CAAC,CAAC;kBACL,CAAE;kBAAAqE,QAAA,EACH;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,gBACpC9E,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9E,OAAA;oBAAA8E,QAAA,EAAO;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC7BlF,OAAA;oBACEkD,KAAK,EAAE5C,aAAa,CAACE,aAAc;oBACnC4E,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;oBACpE2B,SAAS,EAAC,eAAe;oBAAAC,QAAA,gBAEzB9E,OAAA;sBAAQkD,KAAK,EAAC,EAAE;sBAAA4B,QAAA,EAAC;oBAAqB;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,EAC9CvD,cAAc,CAAC4D,GAAG,CAAC7B,IAAI,iBACtB1D,OAAA;sBAAsBkD,KAAK,EAAEQ,IAAI,CAAC9B,EAAG;sBAAAkD,QAAA,GAClCpB,IAAI,CAAC5B,IAAI,EAAC,GAAC,EAAC4B,IAAI,CAAC7B,IAAI;oBAAA,GADX6B,IAAI,CAAC9B,EAAE;sBAAAmD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAEZ,CACT,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAENlF,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9E,OAAA;oBAAA8E,QAAA,EAAO;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eAC5BlF,OAAA;oBACE0D,IAAI,EAAC,MAAM;oBACXR,KAAK,EAAE5C,aAAa,CAACI,QAAS;oBAC9B0E,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;oBAC/DsC,WAAW,EAAC,qBAAqB;oBACjCX,SAAS,EAAC;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACFlF,OAAA;oBAAQ6E,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjD,CAAC,eAENlF,OAAA;kBAAK6E,SAAS,EAAC,mBAAmB;kBAAAC,QAAA,gBAChC9E,OAAA;oBAAA8E,QAAA,EAAO;kBAAkB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACjClF,OAAA;oBACEkD,KAAK,EAAE5C,aAAa,CAACG,WAAY;oBACjC2E,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;oBAClEsC,WAAW,EAAC,qCAAqC;oBACjDX,SAAS,EAAC,iBAAiB;oBAC3BY,IAAI,EAAC;kBAAG;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAENlF,OAAA;kBAAK6E,SAAS,EAAC,iBAAiB;kBAAAC,QAAA,gBAC9B9E,OAAA;oBAAK6E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC9E,OAAA;sBAAA8E,QAAA,EAAO;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eAClBlF,OAAA;sBACE0D,IAAI,EAAC,QAAQ;sBACbR,KAAK,EAAE5C,aAAa,CAACK,UAAW;sBAChCyE,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,YAAY,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;sBACjEsC,WAAW,EAAC,KAAK;sBACjBX,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNlF,OAAA;oBAAK6E,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAChC9E,OAAA;sBAAA8E,QAAA,EAAO;oBAAO;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACtBlF,OAAA;sBACE0D,IAAI,EAAC,KAAK;sBACVR,KAAK,EAAE5C,aAAa,CAACS,aAAc;sBACnCqE,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;sBACpEsC,WAAW,EAAC,WAAW;sBACvBX,SAAS,EAAC;oBAAc;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENlF,OAAA;kBAAK6E,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnC9E,OAAA;oBACE6E,SAAS,EAAE,oBAAoBtD,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAG;oBACrE4D,OAAO,EAAExB,mBAAoB;oBAC7B+B,QAAQ,EAAEnE,gBAAiB;oBAAAuD,QAAA,EAE1BvD,gBAAgB,GAAG,iBAAiB,GAAG;kBAAsB;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC,EACRzD,eAAe,iBACdzB,OAAA;oBAAK6E,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,GAAC,IACtC,EAACrD,eAAe,EAAC,IACpB;kBAAA;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,eAENlF,OAAA;kBACE6E,SAAS,EAAC,oBAAoB;kBAC9BM,OAAO,EAAEtB,sBAAuB;kBAChC6B,QAAQ,EAAEzE,YAAY,IAAI,CAACX,aAAa,CAACE,aAAa,IAAI,CAACF,aAAa,CAACI,QAAS;kBAAAoE,QAAA,EAEjF7D,YAAY,GAAG,yBAAyB,GAAG;gBAAuB;kBAAA8D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAENlF,OAAA;cAAK6E,SAAS,EAAC,uBAAuB;cAAAC,QAAA,gBACpC9E,OAAA;gBAAK6E,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC9E,OAAA;kBAAK6E,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClClF,OAAA;kBAAK6E,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNlF,OAAA;gBAAA8E,QAAA,EAAI;cAAqB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9BlF,OAAA;gBAAK6E,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrC9E,OAAA;kBAAK6E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB9E,OAAA;oBAAA8E,QAAA,EAAM;kBAAa;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1BlF,OAAA;oBAAA8E,QAAA,EAAO3D,gBAAgB,CAAC2C;kBAAW;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB9E,OAAA;oBAAA8E,QAAA,EAAM;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACvBlF,OAAA;oBAAA8E,QAAA,EAAO3D,gBAAgB,CAACiD;kBAAiB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9C,CAAC,eACNlF,OAAA;kBAAK6E,SAAS,EAAC,YAAY;kBAAAC,QAAA,gBACzB9E,OAAA;oBAAA8E,QAAA,EAAM;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjBlF,OAAA;oBAAA8E,QAAA,EAAO3D,gBAAgB,CAACgD;kBAAgB;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBAAK6E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B9E,OAAA;kBAAK6E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACxClF,OAAA;kBAAK6E,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAkC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvElF,OAAA;kBAAK6E,SAAS,EAAC,cAAc;kBAAAC,QAAA,eAC3B9E,OAAA;oBAAK6E,SAAS,EAAC;kBAAe;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENlF,OAAA;gBACE6E,SAAS,EAAC,mBAAmB;gBAC7BM,OAAO,EAAEA,CAAA,KAAM;kBACb/D,mBAAmB,CAAC,IAAI,CAAC;kBACzBb,gBAAgB,CAAC;oBACfC,aAAa,EAAE,EAAE;oBACjBC,WAAW,EAAE,EAAE;oBACfC,QAAQ,EAAE,EAAE;oBACZC,UAAU,EAAE,EAAE;oBACdC,aAAa,EAAE,EAAE;oBACjBC,QAAQ,EAAE,EAAE;oBACZC,OAAO,EAAE,QAAQ;oBACjBC,aAAa,EAAE,EAAE;oBACjBC,cAAc,EAAE;kBAClB,CAAC,CAAC;kBACFU,kBAAkB,CAAC,EAAE,CAAC;gBACxB,CAAE;gBAAAoD,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlF,OAAA;YAAK6E,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlF,OAAA;MAAK6E,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAClC,CAAC3D,gBAAgB,gBAChBnB,OAAA;QAAK6E,SAAS,EAAC,wBAAwB;QAAAC,QAAA,eACrC9E,OAAA;UAAK6E,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3B9E,OAAA;YAAA8E,QAAA,EAAI;UAA6C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDlF,OAAA;YAAG6E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAGhC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BlF,OAAA;cAAK6E,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCnD,cAAc,CAAC4D,GAAG,CAAC7B,IAAI,iBACtB1D,OAAA;gBAEE6E,SAAS,EAAE,sBAAsBvE,aAAa,CAACE,aAAa,KAAKkD,IAAI,CAAC9B,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;gBAC7FuD,OAAO,EAAEA,CAAA,KAAM1B,yBAAyB,CAACC,IAAI,CAAE;gBAAAoB,QAAA,gBAE/C9E,OAAA;kBAAM6E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAEpB,IAAI,CAAC5B;gBAAI;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnDlF,OAAA;kBAAM6E,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,EAAEpB,IAAI,CAAC7B;gBAAI;kBAAAkD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA,GAL9CxB,IAAI,CAAC9B,EAAE;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAMN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAA+B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9ClF,OAAA;cAAK6E,SAAS,EAAC,qBAAqB;cAAAC,QAAA,gBAClC9E,OAAA;gBACE6E,SAAS,EAAE,aAAatD,gBAAgB,GAAG,WAAW,GAAG,EAAE,EAAG;gBAC9D4D,OAAO,EAAExB,mBAAoB;gBAC7B+B,QAAQ,EAAEnE,gBAAiB;gBAAAuD,QAAA,EAE1BvD,gBAAgB,GAAG,iBAAiB,GAAG;cAAuB;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,EACRzD,eAAe,iBACdzB,OAAA;gBAAK6E,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/B9E,OAAA;kBAAA8E,QAAA,EAAQ;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,KAAC,EAACzD,eAAe;cAAA;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzBlF,OAAA;cACE0D,IAAI,EAAC,MAAM;cACXR,KAAK,EAAE5C,aAAa,CAACI,QAAS;cAC9B0E,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,UAAU,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAC/DsC,WAAW,EAAC,wCAAwC;cACpDX,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,EACD7D,YAAY,iBACXrB,OAAA;cAAK6E,SAAS,EAAC,mBAAmB;cAAAC,QAAA,GAAC,kCACX,EAACzD,YAAY,CAACmB,GAAG,CAACmD,OAAO,CAAC,CAAC,CAAC,EAAC,IAAE,EAACtE,YAAY,CAACsB,GAAG,CAACgD,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9E,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAuB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtClF,OAAA;cACEkD,KAAK,EAAE5C,aAAa,CAACG,WAAY;cACjC2E,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,aAAa,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cAClEsC,WAAW,EAAC,8CAA8C;cAC1DX,SAAS,EAAC,eAAe;cACzBY,IAAI,EAAC;YAAG;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvB9E,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9E,OAAA;gBAAA8E,QAAA,EAAO;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC1BlF,OAAA;gBACE0D,IAAI,EAAC,QAAQ;gBACbR,KAAK,EAAE5C,aAAa,CAACK,UAAW;gBAChCyE,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,YAAY,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACjEsC,WAAW,EAAC,KAAK;gBACjBX,SAAS,EAAC;cAAY;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNlF,OAAA;cAAK6E,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACzB9E,OAAA;gBAAA8E,QAAA,EAAO;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACrBlF,OAAA;gBACEkD,KAAK,EAAE5C,aAAa,CAACM,aAAc;gBACnCwE,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;gBACpE2B,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAEvB9E,OAAA;kBAAQkD,KAAK,EAAC,EAAE;kBAAA4B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChClF,OAAA;kBAAQkD,KAAK,EAAC,MAAM;kBAAA4B,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClClF,OAAA;kBAAQkD,KAAK,EAAC,QAAQ;kBAAA4B,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtClF,OAAA;kBAAQkD,KAAK,EAAC,OAAO;kBAAA4B,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAgC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/ClF,OAAA;cAAK6E,SAAS,EAAC,eAAe;cAAAC,QAAA,EAC3B9C,cAAc,CAACuD,GAAG,CAAClC,OAAO,iBACzBrD,OAAA;gBAEE6E,SAAS,EAAE,eAAevE,aAAa,CAACO,QAAQ,CAACyC,QAAQ,CAACD,OAAO,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;gBACvF8B,OAAO,EAAEA,CAAA,KAAM/B,mBAAmB,CAACC,OAAO,CAAE;gBAAAyB,QAAA,EAE3CzB;cAAO,GAJHA,OAAO;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKN,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC/BlF,OAAA;cACE0D,IAAI,EAAC,KAAK;cACVR,KAAK,EAAE5C,aAAa,CAACS,aAAc;cACnCqE,QAAQ,EAAGC,CAAC,IAAKrC,iBAAiB,CAAC,eAAe,EAAEqC,CAAC,CAACC,MAAM,CAACpC,KAAK,CAAE;cACpEsC,WAAW,EAAC,iBAAiB;cAC7BX,SAAS,EAAC;YAAY;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNlF,OAAA;YAAK6E,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzB9E,OAAA;cAAA8E,QAAA,EAAO;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5BlF,OAAA;cAAK6E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAC7B,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,UAAU,CAAC,CAACS,GAAG,CAACK,KAAK,iBAC9C5F,OAAA;gBAEE6E,SAAS,EAAE,eAAevE,aAAa,CAACQ,OAAO,KAAK8E,KAAK,GAAG,UAAU,GAAG,EAAE,IAAIA,KAAK,EAAG;gBACvFT,OAAO,EAAEA,CAAA,KAAMnC,iBAAiB,CAAC,SAAS,EAAE4C,KAAK,CAAE;gBAAAd,QAAA,EAElDc,KAAK,CAACC,WAAW,CAAC;cAAC,GAJfD,KAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKJ,CACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlF,OAAA;YACE6E,SAAS,EAAC,sBAAsB;YAChCM,OAAO,EAAEtB,sBAAuB;YAChC6B,QAAQ,EAAEzE,YAAY,IAAI,CAACX,aAAa,CAACE,aAAa,IAAI,CAACF,aAAa,CAACI,QAAQ,IAAI,CAACJ,aAAa,CAACS,aAAc;YAAA+D,QAAA,EAEjH7D,YAAY,GAAG,6BAA6B,GAAG;UAAgC;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENlF,OAAA;QAAK6E,SAAS,EAAC,oBAAoB;QAAAC,QAAA,gBACjC9E,OAAA;UAAK6E,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrClF,OAAA;UAAA8E,QAAA,EAAI;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrClF,OAAA;UAAK6E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9B9E,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9E,OAAA;cAAA8E,QAAA,EAAQ;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAAC2C,WAAW;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNlF,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9E,OAAA;cAAA8E,QAAA,EAAQ;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACiD,iBAAiB;UAAA;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrE,CAAC,eACNlF,OAAA;YAAK6E,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1B9E,OAAA;cAAA8E,QAAA,EAAQ;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,KAAC,EAAC/D,gBAAgB,CAACgD,gBAAgB;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlF,OAAA;UAAK6E,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAC7B3D,gBAAgB,CAACkD;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACNlF,OAAA;UACE6E,SAAS,EAAC,iBAAiB;UAC3BM,OAAO,EAAEA,CAAA,KAAM;YACb/D,mBAAmB,CAAC,IAAI,CAAC;YACzBb,gBAAgB,CAAC;cACfC,aAAa,EAAE,EAAE;cACjBC,WAAW,EAAE,EAAE;cACfC,QAAQ,EAAE,EAAE;cACZC,UAAU,EAAE,EAAE;cACdC,aAAa,EAAE,EAAE;cACjBC,QAAQ,EAAE,EAAE;cACZC,OAAO,EAAE,QAAQ;cACjBC,aAAa,EAAE,EAAE;cACjBC,cAAc,EAAE;YAClB,CAAC,CAAC;YACFU,kBAAkB,CAAC,EAAE,CAAC;UACxB,CAAE;UAAAoD,QAAA,EACH;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChF,EAAA,CAjjBID,SAAS;AAAA6F,EAAA,GAAT7F,SAAS;AAmjBf,eAAeA,SAAS;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}