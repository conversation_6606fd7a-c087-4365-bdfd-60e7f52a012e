[{"D:\\EMBEDDED\\Project\\traffic\\src\\index.js": "1", "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js": "2", "D:\\EMBEDDED\\Project\\traffic\\src\\App.js": "3", "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js": "4", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js": "5", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js": "6", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js": "7", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js": "8"}, {"size": 535, "mtime": 1741241102046, "results": "9", "hashOfConfig": "10"}, {"size": 362, "mtime": 1741241102355, "results": "11", "hashOfConfig": "10"}, {"size": 61609, "mtime": 1750151052794, "results": "12", "hashOfConfig": "10"}, {"size": 5919, "mtime": 1741266905829, "results": "13", "hashOfConfig": "10"}, {"size": 20747, "mtime": 1750013579959, "results": "14", "hashOfConfig": "10"}, {"size": 35647, "mtime": 1750015465671, "results": "15", "hashOfConfig": "10"}, {"size": 11972, "mtime": 1750150186469, "results": "16", "hashOfConfig": "10"}, {"size": 15700, "mtime": 1750150731742, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ly5ux3", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\EMBEDDED\\Project\\traffic\\src\\index.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\App.js", ["42", "43", "44", "45", "46", "47", "48", "49", "50", "51", "52"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js", ["53", "54", "55", "56", "57"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js", ["58", "59", "60", "61"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js", [], [], {"ruleId": "62", "severity": 1, "message": "63", "line": 9, "column": 7, "nodeType": "64", "messageId": "65", "endLine": 9, "endColumn": 25}, {"ruleId": "62", "severity": 1, "message": "66", "line": 88, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 88, "endColumn": 13}, {"ruleId": "62", "severity": 1, "message": "67", "line": 91, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 91, "endColumn": 25}, {"ruleId": "62", "severity": 1, "message": "68", "line": 91, "column": 27, "nodeType": "64", "messageId": "65", "endLine": 91, "endColumn": 45}, {"ruleId": "62", "severity": 1, "message": "69", "line": 94, "column": 24, "nodeType": "64", "messageId": "65", "endLine": 94, "endColumn": 39}, {"ruleId": "62", "severity": 1, "message": "70", "line": 101, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 101, "endColumn": 20}, {"ruleId": "62", "severity": 1, "message": "71", "line": 101, "column": 22, "nodeType": "64", "messageId": "65", "endLine": 101, "endColumn": 35}, {"ruleId": "62", "severity": 1, "message": "72", "line": 170, "column": 23, "nodeType": "64", "messageId": "65", "endLine": 170, "endColumn": 37}, {"ruleId": "62", "severity": 1, "message": "73", "line": 183, "column": 30, "nodeType": "64", "messageId": "65", "endLine": 183, "endColumn": 51}, {"ruleId": "62", "severity": 1, "message": "74", "line": 434, "column": 15, "nodeType": "64", "messageId": "65", "endLine": 434, "endColumn": 28}, {"ruleId": "75", "severity": 1, "message": "76", "line": 464, "column": 6, "nodeType": "77", "endLine": 464, "endColumn": 8, "suggestions": "78"}, {"ruleId": "62", "severity": 1, "message": "79", "line": 54, "column": 31, "nodeType": "64", "messageId": "65", "endLine": 54, "endColumn": 53}, {"ruleId": "62", "severity": 1, "message": "80", "line": 75, "column": 31, "nodeType": "64", "messageId": "65", "endLine": 75, "endColumn": 53}, {"ruleId": "62", "severity": 1, "message": "81", "line": 158, "column": 9, "nodeType": "64", "messageId": "65", "endLine": 158, "endColumn": 19}, {"ruleId": "62", "severity": 1, "message": "82", "line": 167, "column": 9, "nodeType": "64", "messageId": "65", "endLine": 167, "endColumn": 25}, {"ruleId": "62", "severity": 1, "message": "83", "line": 177, "column": 9, "nodeType": "64", "messageId": "65", "endLine": 177, "endColumn": 23}, {"ruleId": "62", "severity": 1, "message": "84", "line": 10, "column": 10, "nodeType": "64", "messageId": "65", "endLine": 10, "endColumn": 21}, {"ruleId": "75", "severity": 1, "message": "85", "line": 184, "column": 6, "nodeType": "77", "endLine": 184, "endColumn": 8, "suggestions": "86"}, {"ruleId": "62", "severity": 1, "message": "87", "line": 306, "column": 11, "nodeType": "64", "messageId": "65", "endLine": 306, "endColumn": 18}, {"ruleId": "75", "severity": 1, "message": "88", "line": 514, "column": 6, "nodeType": "77", "endLine": 514, "endColumn": 27, "suggestions": "89"}, "no-unused-vars", "'emergencyLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'map' is assigned a value but never used.", "'emergencyAlerts' is assigned a value but never used.", "'setEmergencyAlerts' is assigned a value but never used.", "'setSystemStatus' is assigned a value but never used.", "'mapMarkers' is assigned a value but never used.", "'setMapMarkers' is assigned a value but never used.", "'setHeatmapData' is assigned a value but never used.", "'setSystemArchitecture' is assigned a value but never used.", "'trafficCircle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'activeEmergencies', 'ambulances', and 'currentTime'. Either include them or remove the dependency array.", "ArrayExpression", ["90"], "'setAvailableAmbulances' is assigned a value but never used.", "'setOptimizationMetrics' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roadNetwork.roads' and 'trafficLights'. Either include them or remove the dependency array.", ["91"], "'bearing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'moveAmbulanceAlongRoute'. Either include it or remove the dependency array.", ["92"], {"desc": "93", "fix": "94"}, {"desc": "95", "fix": "96"}, {"desc": "97", "fix": "98"}, "Update the dependencies array to be: [activeEmergencies, ambulances, currentTime]", {"range": "99", "text": "100"}, "Update the dependencies array to be: [roadNetwork.roads, trafficLights]", {"range": "101", "text": "102"}, "Update the dependencies array to be: [isSimulationRunning, moveAmbulanceAlongRoute]", {"range": "103", "text": "104"}, [19390, 19392], "[activeEmergencies, ambulances, currentTime]", [6571, 6573], "[roadNetwork.roads, trafficLights]", [16529, 16550], "[isSimulationRunning, moveAmbulanceAlongRoute]"]