[{"D:\\EMBEDDED\\Project\\traffic\\src\\index.js": "1", "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js": "2", "D:\\EMBEDDED\\Project\\traffic\\src\\App.js": "3", "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js": "4", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js": "5", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js": "6", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js": "7", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js": "8", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\ARNavigation.js": "9"}, {"size": 535, "mtime": 1741241102046, "results": "10", "hashOfConfig": "11"}, {"size": 362, "mtime": 1741241102355, "results": "12", "hashOfConfig": "11"}, {"size": 62013, "mtime": 1750155023717, "results": "13", "hashOfConfig": "11"}, {"size": 5919, "mtime": 1741266905829, "results": "14", "hashOfConfig": "11"}, {"size": 20747, "mtime": 1750013579959, "results": "15", "hashOfConfig": "11"}, {"size": 35647, "mtime": 1750015465671, "results": "16", "hashOfConfig": "11"}, {"size": 21526, "mtime": 1750152019414, "results": "17", "hashOfConfig": "11"}, {"size": 15700, "mtime": 1750150731742, "results": "18", "hashOfConfig": "11"}, {"size": 13467, "mtime": 1750154943575, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ly5ux3", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\EMBEDDED\\Project\\traffic\\src\\index.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\App.js", ["47", "48", "49", "50", "51", "52", "53", "54", "55", "56", "57"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js", ["58", "59", "60", "61", "62"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js", ["63", "64", "65", "66"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\ARNavigation.js", ["67", "68"], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 10, "column": 7, "nodeType": "71", "messageId": "72", "endLine": 10, "endColumn": 25}, {"ruleId": "69", "severity": 1, "message": "73", "line": 89, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 89, "endColumn": 13}, {"ruleId": "69", "severity": 1, "message": "74", "line": 92, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 92, "endColumn": 25}, {"ruleId": "69", "severity": 1, "message": "75", "line": 92, "column": 27, "nodeType": "71", "messageId": "72", "endLine": 92, "endColumn": 45}, {"ruleId": "69", "severity": 1, "message": "76", "line": 95, "column": 24, "nodeType": "71", "messageId": "72", "endLine": 95, "endColumn": 39}, {"ruleId": "69", "severity": 1, "message": "77", "line": 102, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 102, "endColumn": 20}, {"ruleId": "69", "severity": 1, "message": "78", "line": 102, "column": 22, "nodeType": "71", "messageId": "72", "endLine": 102, "endColumn": 35}, {"ruleId": "69", "severity": 1, "message": "79", "line": 171, "column": 23, "nodeType": "71", "messageId": "72", "endLine": 171, "endColumn": 37}, {"ruleId": "69", "severity": 1, "message": "80", "line": 184, "column": 30, "nodeType": "71", "messageId": "72", "endLine": 184, "endColumn": 51}, {"ruleId": "69", "severity": 1, "message": "81", "line": 435, "column": 15, "nodeType": "71", "messageId": "72", "endLine": 435, "endColumn": 28}, {"ruleId": "82", "severity": 1, "message": "83", "line": 465, "column": 6, "nodeType": "84", "endLine": 465, "endColumn": 8, "suggestions": "85"}, {"ruleId": "69", "severity": 1, "message": "86", "line": 54, "column": 31, "nodeType": "71", "messageId": "72", "endLine": 54, "endColumn": 53}, {"ruleId": "69", "severity": 1, "message": "87", "line": 75, "column": 31, "nodeType": "71", "messageId": "72", "endLine": 75, "endColumn": 53}, {"ruleId": "69", "severity": 1, "message": "88", "line": 158, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 158, "endColumn": 19}, {"ruleId": "69", "severity": 1, "message": "89", "line": 167, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 167, "endColumn": 25}, {"ruleId": "69", "severity": 1, "message": "90", "line": 177, "column": 9, "nodeType": "71", "messageId": "72", "endLine": 177, "endColumn": 23}, {"ruleId": "69", "severity": 1, "message": "91", "line": 10, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 10, "endColumn": 21}, {"ruleId": "82", "severity": 1, "message": "92", "line": 184, "column": 6, "nodeType": "84", "endLine": 184, "endColumn": 8, "suggestions": "93"}, {"ruleId": "69", "severity": 1, "message": "94", "line": 306, "column": 11, "nodeType": "71", "messageId": "72", "endLine": 306, "endColumn": 18}, {"ruleId": "82", "severity": 1, "message": "95", "line": 514, "column": 6, "nodeType": "84", "endLine": 514, "endColumn": 27, "suggestions": "96"}, {"ruleId": "69", "severity": 1, "message": "97", "line": 9, "column": 10, "nodeType": "71", "messageId": "72", "endLine": 9, "endColumn": 24}, {"ruleId": "69", "severity": 1, "message": "98", "line": 9, "column": 26, "nodeType": "71", "messageId": "72", "endLine": 9, "endColumn": 43}, "no-unused-vars", "'emergencyLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'map' is assigned a value but never used.", "'emergencyAlerts' is assigned a value but never used.", "'setEmergencyAlerts' is assigned a value but never used.", "'setSystemStatus' is assigned a value but never used.", "'mapMarkers' is assigned a value but never used.", "'setMapMarkers' is assigned a value but never used.", "'setHeatmapData' is assigned a value but never used.", "'setSystemArchitecture' is assigned a value but never used.", "'trafficCircle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'activeEmergencies', 'ambulances', and 'currentTime'. Either include them or remove the dependency array.", "ArrayExpression", ["99"], "'setAvailableAmbulances' is assigned a value but never used.", "'setOptimizationMetrics' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roadNetwork.roads' and 'trafficLights'. Either include them or remove the dependency array.", ["100"], "'bearing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'moveAmbulanceAlongRoute'. Either include it or remove the dependency array.", ["101"], "'navigationMode' is assigned a value but never used.", "'setNavigationMode' is assigned a value but never used.", {"desc": "102", "fix": "103"}, {"desc": "104", "fix": "105"}, {"desc": "106", "fix": "107"}, "Update the dependencies array to be: [activeEmergencies, ambulances, currentTime]", {"range": "108", "text": "109"}, "Update the dependencies array to be: [roadNetwork.roads, trafficLights]", {"range": "110", "text": "111"}, "Update the dependencies array to be: [isSimulationRunning, moveAmbulanceAlongRoute]", {"range": "112", "text": "113"}, [19444, 19446], "[activeEmergencies, ambulances, currentTime]", [6571, 6573], "[roadNetwork.roads, trafficLights]", [16529, 16550], "[isSimulationRunning, moveAmbulanceAlongRoute]"]