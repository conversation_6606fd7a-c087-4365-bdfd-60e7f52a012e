[{"D:\\EMBEDDED\\Project\\traffic\\src\\index.js": "1", "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js": "2", "D:\\EMBEDDED\\Project\\traffic\\src\\App.js": "3", "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js": "4", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficDashboard.js": "5"}, {"size": 535, "mtime": 1741241102046, "results": "6", "hashOfConfig": "7"}, {"size": 362, "mtime": 1741241102355, "results": "8", "hashOfConfig": "7"}, {"size": 12374, "mtime": 1751108856122, "results": "9", "hashOfConfig": "7"}, {"size": 4655, "mtime": 1751108264720, "results": "10", "hashOfConfig": "7"}, {"size": 12149, "mtime": 1751108345430, "results": "11", "hashOfConfig": "7"}, {"filePath": "12", "messages": "13", "suppressedMessages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ly5ux3", {"filePath": "15", "messages": "16", "suppressedMessages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\EMBEDDED\\Project\\traffic\\src\\index.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\App.js", ["27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficDashboard.js", ["49"], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 42, "column": 7, "nodeType": "52", "messageId": "53", "endLine": 42, "endColumn": 21}, {"ruleId": "50", "severity": 1, "message": "54", "line": 56, "column": 7, "nodeType": "52", "messageId": "53", "endLine": 56, "endColumn": 20}, {"ruleId": "50", "severity": 1, "message": "55", "line": 132, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 132, "endColumn": 18}, {"ruleId": "50", "severity": 1, "message": "56", "line": 132, "column": 20, "nodeType": "52", "messageId": "53", "endLine": 132, "endColumn": 31}, {"ruleId": "50", "severity": 1, "message": "57", "line": 133, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 133, "endColumn": 13}, {"ruleId": "50", "severity": 1, "message": "58", "line": 134, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 134, "endColumn": 23}, {"ruleId": "50", "severity": 1, "message": "59", "line": 134, "column": 25, "nodeType": "52", "messageId": "53", "endLine": 134, "endColumn": 41}, {"ruleId": "50", "severity": 1, "message": "60", "line": 135, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 135, "endColumn": 21}, {"ruleId": "50", "severity": 1, "message": "61", "line": 142, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 142, "endColumn": 21}, {"ruleId": "50", "severity": 1, "message": "62", "line": 143, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 143, "endColumn": 22}, {"ruleId": "50", "severity": 1, "message": "63", "line": 143, "column": 24, "nodeType": "52", "messageId": "53", "endLine": 143, "endColumn": 39}, {"ruleId": "50", "severity": 1, "message": "64", "line": 145, "column": 10, "nodeType": "52", "messageId": "53", "endLine": 145, "endColumn": 26}, {"ruleId": "50", "severity": 1, "message": "65", "line": 145, "column": 28, "nodeType": "52", "messageId": "53", "endLine": 145, "endColumn": 47}, {"ruleId": "66", "severity": 2, "message": "67", "line": 187, "column": 7, "nodeType": "52", "messageId": "68", "endLine": 187, "endColumn": 20}, {"ruleId": "50", "severity": 1, "message": "69", "line": 238, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 238, "endColumn": 23}, {"ruleId": "50", "severity": 1, "message": "70", "line": 248, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 248, "endColumn": 25}, {"ruleId": "50", "severity": 1, "message": "71", "line": 258, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 258, "endColumn": 19}, {"ruleId": "50", "severity": 1, "message": "72", "line": 278, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 278, "endColumn": 29}, {"ruleId": "66", "severity": 2, "message": "73", "line": 279, "column": 33, "nodeType": "52", "messageId": "68", "endLine": 279, "endColumn": 43}, {"ruleId": "50", "severity": 1, "message": "74", "line": 295, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 295, "endColumn": 26}, {"ruleId": "66", "severity": 2, "message": "67", "line": 296, "column": 5, "nodeType": "52", "messageId": "68", "endLine": 296, "endColumn": 18}, {"ruleId": "66", "severity": 2, "message": "75", "line": 302, "column": 5, "nodeType": "52", "messageId": "68", "endLine": 302, "endColumn": 25}, {"ruleId": "50", "severity": 1, "message": "76", "line": 48, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 48, "endColumn": 24}, "no-unused-vars", "'majorLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'trafficRoutes' is assigned a value but never used.", "'darkMode' is assigned a value but never used.", "'setDarkMode' is assigned a value but never used.", "'map' is assigned a value but never used.", "'selectedRoute' is assigned a value but never used.", "'setSelectedRoute' is assigned a value but never used.", "'trafficData' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "'systemStatus' is assigned a value but never used.", "'setSystemStatus' is assigned a value but never used.", "'trafficIncidents' is assigned a value but never used.", "'setTrafficIncidents' is assigned a value but never used.", "no-undef", "'setAmbulances' is not defined.", "undef", "'getStatusColor' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'findNearestAmbulance' is assigned a value but never used.", "'ambulances' is not defined.", "'dispatchAmbulance' is assigned a value but never used.", "'setActiveEmergencies' is not defined.", "'getDensityColor' is assigned a value but never used."]