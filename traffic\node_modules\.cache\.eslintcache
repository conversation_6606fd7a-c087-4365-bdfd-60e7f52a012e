[{"D:\\EMBEDDED\\Project\\traffic\\src\\index.js": "1", "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js": "2", "D:\\EMBEDDED\\Project\\traffic\\src\\App.js": "3", "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js": "4", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js": "5", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js": "6", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js": "7"}, {"size": 535, "mtime": 1741241102046, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1741241102355, "results": "10", "hashOfConfig": "9"}, {"size": 61226, "mtime": 1750150289229, "results": "11", "hashOfConfig": "9"}, {"size": 5919, "mtime": 1741266905829, "results": "12", "hashOfConfig": "9"}, {"size": 20747, "mtime": 1750013579959, "results": "13", "hashOfConfig": "9"}, {"size": 35647, "mtime": 1750015465671, "results": "14", "hashOfConfig": "9"}, {"size": 11972, "mtime": 1750150186469, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ly5ux3", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\EMBEDDED\\Project\\traffic\\src\\index.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\App.js", ["37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\PatientTransportOptimizer.js", ["48", "49", "50", "51", "52"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\TrafficSignalHijacking.js", ["53", "54", "55", "56"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js", [], [], {"ruleId": "57", "severity": 1, "message": "58", "line": 8, "column": 7, "nodeType": "59", "messageId": "60", "endLine": 8, "endColumn": 25}, {"ruleId": "57", "severity": 1, "message": "61", "line": 87, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 87, "endColumn": 13}, {"ruleId": "57", "severity": 1, "message": "62", "line": 90, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 90, "endColumn": 25}, {"ruleId": "57", "severity": 1, "message": "63", "line": 90, "column": 27, "nodeType": "59", "messageId": "60", "endLine": 90, "endColumn": 45}, {"ruleId": "57", "severity": 1, "message": "64", "line": 93, "column": 24, "nodeType": "59", "messageId": "60", "endLine": 93, "endColumn": 39}, {"ruleId": "57", "severity": 1, "message": "65", "line": 100, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 100, "endColumn": 20}, {"ruleId": "57", "severity": 1, "message": "66", "line": 100, "column": 22, "nodeType": "59", "messageId": "60", "endLine": 100, "endColumn": 35}, {"ruleId": "57", "severity": 1, "message": "67", "line": 169, "column": 23, "nodeType": "59", "messageId": "60", "endLine": 169, "endColumn": 37}, {"ruleId": "57", "severity": 1, "message": "68", "line": 182, "column": 30, "nodeType": "59", "messageId": "60", "endLine": 182, "endColumn": 51}, {"ruleId": "57", "severity": 1, "message": "69", "line": 433, "column": 15, "nodeType": "59", "messageId": "60", "endLine": 433, "endColumn": 28}, {"ruleId": "70", "severity": 1, "message": "71", "line": 463, "column": 6, "nodeType": "72", "endLine": 463, "endColumn": 8, "suggestions": "73"}, {"ruleId": "57", "severity": 1, "message": "74", "line": 54, "column": 31, "nodeType": "59", "messageId": "60", "endLine": 54, "endColumn": 53}, {"ruleId": "57", "severity": 1, "message": "75", "line": 75, "column": 31, "nodeType": "59", "messageId": "60", "endLine": 75, "endColumn": 53}, {"ruleId": "57", "severity": 1, "message": "76", "line": 158, "column": 9, "nodeType": "59", "messageId": "60", "endLine": 158, "endColumn": 19}, {"ruleId": "57", "severity": 1, "message": "77", "line": 167, "column": 9, "nodeType": "59", "messageId": "60", "endLine": 167, "endColumn": 25}, {"ruleId": "57", "severity": 1, "message": "78", "line": 177, "column": 9, "nodeType": "59", "messageId": "60", "endLine": 177, "endColumn": 23}, {"ruleId": "57", "severity": 1, "message": "79", "line": 10, "column": 10, "nodeType": "59", "messageId": "60", "endLine": 10, "endColumn": 21}, {"ruleId": "70", "severity": 1, "message": "80", "line": 184, "column": 6, "nodeType": "72", "endLine": 184, "endColumn": 8, "suggestions": "81"}, {"ruleId": "57", "severity": 1, "message": "82", "line": 306, "column": 11, "nodeType": "59", "messageId": "60", "endLine": 306, "endColumn": 18}, {"ruleId": "70", "severity": 1, "message": "83", "line": 514, "column": 6, "nodeType": "72", "endLine": 514, "endColumn": 27, "suggestions": "84"}, "no-unused-vars", "'emergencyLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'map' is assigned a value but never used.", "'emergencyAlerts' is assigned a value but never used.", "'setEmergencyAlerts' is assigned a value but never used.", "'setSystemStatus' is assigned a value but never used.", "'mapMarkers' is assigned a value but never used.", "'setMapMarkers' is assigned a value but never used.", "'setHeatmapData' is assigned a value but never used.", "'setSystemArchitecture' is assigned a value but never used.", "'trafficCircle' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'activeEmergencies', 'ambulances', and 'currentTime'. Either include them or remove the dependency array.", "ArrayExpression", ["85"], "'setAvailableAmbulances' is assigned a value but never used.", "'setOptimizationMetrics' is assigned a value but never used.", "'formatTime' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'getStatusColor' is assigned a value but never used.", "'currentTime' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roadNetwork.roads' and 'trafficLights'. Either include them or remove the dependency array.", ["86"], "'bearing' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'moveAmbulanceAlongRoute'. Either include it or remove the dependency array.", ["87"], {"desc": "88", "fix": "89"}, {"desc": "90", "fix": "91"}, {"desc": "92", "fix": "93"}, "Update the dependencies array to be: [activeEmergencies, ambulances, currentTime]", {"range": "94", "text": "95"}, "Update the dependencies array to be: [roadNetwork.roads, trafficLights]", {"range": "96", "text": "97"}, "Update the dependencies array to be: [isSimulationRunning, moveAmbulanceAlongRoute]", {"range": "98", "text": "99"}, [19340, 19342], "[activeEmergencies, ambulances, currentTime]", [6571, 6573], "[roadNetwork.roads, trafficLights]", [16529, 16550], "[isSimulationRunning, moveAmbulanceAlongRoute]"]