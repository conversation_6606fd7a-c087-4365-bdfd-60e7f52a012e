[{"D:\\EMBEDDED\\Project\\traffic\\src\\index.js": "1", "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js": "2", "D:\\EMBEDDED\\Project\\traffic\\src\\App.js": "3", "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js": "4", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js": "5", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js": "6", "D:\\EMBEDDED\\Project\\traffic\\src\\components\\ARNavigation.js": "7"}, {"size": 535, "mtime": 1741241102046, "results": "8", "hashOfConfig": "9"}, {"size": 362, "mtime": 1741241102355, "results": "10", "hashOfConfig": "9"}, {"size": 15229, "mtime": 1751051531557, "results": "11", "hashOfConfig": "9"}, {"size": 5919, "mtime": 1741266905829, "results": "12", "hashOfConfig": "9"}, {"size": 21526, "mtime": 1750152019414, "results": "13", "hashOfConfig": "9"}, {"size": 21106, "mtime": 1750156535632, "results": "14", "hashOfConfig": "9"}, {"size": 13467, "mtime": 1750154943575, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ly5ux3", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\EMBEDDED\\Project\\traffic\\src\\index.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\reportWebVitals.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\App.js", ["37", "38", "39", "40", "41", "42", "43", "44", "45"], [], "D:\\EMBEDDED\\Project\\traffic\\src\\routes.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\MobileApp.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\Storyboard.js", [], [], "D:\\EMBEDDED\\Project\\traffic\\src\\components\\ARNavigation.js", ["46", "47"], [], {"ruleId": "48", "severity": 1, "message": "49", "line": 5, "column": 7, "nodeType": "50", "messageId": "51", "endLine": 5, "endColumn": 25}, {"ruleId": "48", "severity": 1, "message": "52", "line": 15, "column": 7, "nodeType": "50", "messageId": "51", "endLine": 15, "endColumn": 16}, {"ruleId": "48", "severity": 1, "message": "53", "line": 66, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 66, "endColumn": 13}, {"ruleId": "48", "severity": 1, "message": "54", "line": 69, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 69, "endColumn": 25}, {"ruleId": "48", "severity": 1, "message": "55", "line": 69, "column": 27, "nodeType": "50", "messageId": "51", "endLine": 69, "endColumn": 45}, {"ruleId": "48", "severity": 1, "message": "56", "line": 72, "column": 24, "nodeType": "50", "messageId": "51", "endLine": 72, "endColumn": 39}, {"ruleId": "48", "severity": 1, "message": "57", "line": 177, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 177, "endColumn": 25}, {"ruleId": "48", "severity": 1, "message": "58", "line": 207, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 207, "endColumn": 29}, {"ruleId": "48", "severity": 1, "message": "59", "line": 224, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 224, "endColumn": 26}, {"ruleId": "48", "severity": 1, "message": "60", "line": 9, "column": 10, "nodeType": "50", "messageId": "51", "endLine": 9, "endColumn": 24}, {"ruleId": "48", "severity": 1, "message": "61", "line": 9, "column": 26, "nodeType": "50", "messageId": "51", "endLine": 9, "endColumn": 43}, "no-unused-vars", "'emergencyLocations' is assigned a value but never used.", "Identifier", "unusedVar", "'hospitals' is assigned a value but never used.", "'map' is assigned a value but never used.", "'emergencyAlerts' is assigned a value but never used.", "'setEmergencyAlerts' is assigned a value but never used.", "'setSystemStatus' is assigned a value but never used.", "'getPriorityColor' is assigned a value but never used.", "'findNearestAmbulance' is assigned a value but never used.", "'dispatchAmbulance' is assigned a value but never used.", "'navigationMode' is assigned a value but never used.", "'setNavigationMode' is assigned a value but never used."]