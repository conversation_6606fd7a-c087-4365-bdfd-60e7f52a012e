.storyboard-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.storyboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.storyboard-header h1 {
  color: var(--text-primary);
  margin: 0;
  font-size: 32px;
  font-weight: 700;
}

.header-time {
  color: var(--text-secondary);
  font-size: 18px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.storyboard-intro {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  overflow: hidden;
}

.intro-content {
  padding: 30px;
}

.intro-content h2 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 26px;
  font-weight: 700;
}

.intro-content p {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.storyboard-flow {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.storyboard-flow h3 {
  color: var(--text-primary);
  margin: 0 0 30px 0;
  font-size: 24px;
  font-weight: 700;
}

.flow-timeline {
  position: relative;
}

.flow-step {
  position: relative;
  margin-bottom: 30px;
  cursor: pointer;
}

.step-connector {
  position: absolute;
  left: 30px;
  top: 0;
  width: 4px;
  height: 100%;
  border-radius: 2px;
  z-index: 1;
}

.connector-line {
  position: absolute;
  top: 60px;
  left: -2px;
  width: 8px;
  height: calc(100% - 60px);
  background: var(--border-color);
  border-radius: 4px;
}

.step-card {
  margin-left: 80px;
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.step-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.flow-step.active .step-card {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.step-header {
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.step-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.step-info {
  flex: 1;
}

.step-info h4 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
}

.step-info p {
  color: var(--text-secondary);
  margin: 0 0 10px 0;
  font-size: 16px;
  line-height: 1.5;
}

.step-timeframe {
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 5px;
}

.step-details {
  margin-top: 25px;
  padding-top: 25px;
  border-top: 1px solid var(--border-color);
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.details-section {
  margin-bottom: 20px;
}

.details-section h5 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.details-section ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.details-section li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.technologies-section h5 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-tag {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  border: 1px solid currentColor;
}

.features-overview {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.features-overview h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.feature-category {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.feature-category h4 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #3b82f6;
}

.feature-category ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.feature-category li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.performance-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.performance-section h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-value {
  font-size: 28px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 8px;
}

.metric-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 5px;
}

.metric-improvement {
  font-size: 14px;
  color: #059669;
  font-weight: 500;
}

.architecture-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.architecture-section h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.architecture-content {
  display: grid;
  gap: 25px;
}

.architecture-description h4 {
  color: var(--text-primary);
  margin: 0 0 10px 0;
  font-size: 20px;
  font-weight: 600;
}

.architecture-description p {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.architecture-components {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.component-group {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.component-group h5 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #f59e0b;
}

.component-group ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.component-group li {
  margin-bottom: 8px;
  line-height: 1.4;
}

.impact-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.impact-section h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.impact-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 25px;
  text-align: center;
  transition: all 0.2s ease;
}

.impact-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.impact-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.impact-card h4 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.impact-card p {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.tech-stack-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  padding: 30px;
}

.tech-stack-section h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.tech-stack-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.tech-category {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.tech-category h4 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #dc2626;
}

.tech-items {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tech-item {
  padding: 8px 14px;
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tech-item:hover {
  background: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .storyboard-container {
    padding: 15px;
  }

  .storyboard-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .step-card {
    margin-left: 60px;
  }

  .step-header {
    flex-direction: column;
    text-align: center;
  }

  .features-grid,
  .metrics-grid,
  .architecture-components,
  .impact-grid,
  .tech-stack-grid {
    grid-template-columns: 1fr;
  }
}
