/* Enterprise Traffic Management System */
.traffic-hijacking {
  min-height: calc(100vh - 120px);
  background: var(--bg-secondary);
  padding: 24px;
  color: var(--text-primary);
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enterprise Header */
.enterprise-header {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  margin-bottom: 24px;
  box-shadow: var(--shadow-xl);
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.header-info h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.header-info p {
  margin: 0;
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.header-status {
  display: flex;
  align-items: center;
  gap: 24px;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-dot.active {
  background: #dc2626;
  box-shadow: 0 0 8px rgba(220, 38, 38, 0.4);
}

.status-dot.standby {
  background: #10b981;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-text {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.fsm-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fsm-label {
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fsm-value {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.fsm-value.normal-operation {
  background: #dcfce7;
  color: #166534;
}

.fsm-value.emergency-detected {
  background: #fecaca;
  color: #991b1b;
}

.fsm-value.corridor-planning {
  background: #fef3c7;
  color: #92400e;
}

.fsm-value.signal-hijacking {
  background: #dbeafe;
  color: #1e40af;
}

.fsm-value.corridor-active {
  background: #d1fae5;
  color: #065f46;
}

.control-panel {
  display: flex;
  gap: 12px;
}

.enterprise-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.enterprise-btn.primary {
  background: #2563eb;
  color: white;
  border: 1px solid #2563eb;
}

.enterprise-btn.primary:hover {
  background: #1d4ed8;
  border-color: #1d4ed8;
}

.enterprise-btn.danger {
  background: #dc2626;
  color: white;
  border: 1px solid #dc2626;
}

.enterprise-btn.danger:hover {
  background: #b91c1c;
  border-color: #b91c1c;
}

.enterprise-content {
  display: grid;
  grid-template-columns: 320px 1fr 320px;
  gap: 24px;
  height: calc(100vh - 240px);
}

/* Enterprise Panel Styles */
.enterprise-panel {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 24px;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  height: fit-content;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.enterprise-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--accent-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enterprise-panel:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.enterprise-panel:hover::before {
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.panel-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  transition: color 0.3s ease;
}

/* Left Panel - Enterprise Metrics */
.left-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.metrics-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.metric-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.metric-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.metric-icon {
  font-size: 16px;
}

.metric-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: color 0.3s ease;
}

.metric-card .metric-value {
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 4px;
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.metric-card .metric-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 600;
  transition: color 0.3s ease;
}

/* AI Control Panel */
.control-mode-selector {
  display: flex;
  align-items: center;
}

.enterprise-select {
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  color: #374151;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  font-weight: 500;
}

.enterprise-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 1px #2563eb;
}

.ai-performance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.performance-metric {
  text-align: center;
  padding: 12px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.performance-metric:hover {
  background: #e2e8f0;
}

.performance-metric .metric-value {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 4px;
}

.performance-metric .metric-label {
  font-size: 10px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.capabilities-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.capability-item:hover {
  background: #f1f5f9;
}

.capability-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.capability-content {
  flex: 1;
}

.capability-title {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
}

.capability-desc {
  font-size: 11px;
  color: #64748b;
}

.swarm-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid #334155;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.feature-item:hover {
  border-color: #a78bfa;
  transform: translateX(5px);
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-title {
  font-size: 13px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 2px;
}

.feature-desc {
  font-size: 11px;
  color: #94a3b8;
}

/* Center Panel - Simulation */
.center-panel {
  display: flex;
  flex-direction: column;
}

.simulation-panel {
  flex: 1;
}

.simulation-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.active {
  background: #10b981;
  box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-indicator.inactive {
  background: #6b7280;
}

/* Center Panel - Map */
.center-panel {
  display: flex;
  flex-direction: column;
}

.map-panel {
  flex: 1;
}

/* Stunning Map Controls */
.map-controls {
  display: flex;
  gap: 20px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 8px 16px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.control-group:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.control-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  transition: color 0.3s ease;
}

.map-control-btn {
  padding: 6px 12px;
  border: 1px solid var(--border-secondary);
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.map-control-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.map-control-btn:hover::before {
  left: 100%;
}

.map-control-btn.active {
  background: var(--accent-gradient);
  border-color: var(--accent-primary);
  color: white;
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

.map-control-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;
  height: 2px;
  background: white;
  border-radius: 1px;
  animation: glow 2s infinite;
}

.map-control-btn:hover:not(.active) {
  background: var(--bg-tertiary);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.map-container {
  position: relative;
  flex: 1;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  min-height: 500px;
}

.enterprise-map {
  width: 100%;
  height: 100%;
  border-radius: 6px;
}

.map-overlay {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 1000;
}

/* Stunning Map Legend */
.map-legend {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 20px;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.map-legend::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--accent-gradient);
  opacity: 0.8;
}

.map-legend:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

.legend-header h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.legend-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid transparent;
}

.legend-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateX(4px);
  color: var(--text-primary);
}

.legend-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.legend-item:hover .legend-indicator {
  transform: scale(1.2);
  box-shadow: var(--shadow-lg);
}

.legend-indicator.red-signal {
  background: var(--error);
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.4);
}

.legend-indicator.yellow-signal {
  background: var(--warning);
  box-shadow: 0 0 8px rgba(245, 158, 11, 0.4);
}

.legend-indicator.green-signal {
  background: var(--success);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.legend-indicator.ai-controlled {
  background: var(--accent-primary);
  box-shadow: 0 0 12px rgba(59, 130, 246, 0.6);
  animation: glow 2s infinite;
}

.legend-indicator.emergency-vehicle {
  background: var(--error);
  border: 3px solid var(--bg-primary);
  box-shadow: 0 0 12px rgba(239, 68, 68, 0.6);
  animation: pulse 1.5s infinite;
}

/* Stunning System State Display */
.system-state-display {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 20px;
  text-align: center;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.system-state-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--accent-gradient);
  opacity: 0.8;
}

.system-state-display:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-2xl);
}

.state-header {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  margin-bottom: 12px;
  transition: color 0.3s ease;
}

.state-value {
  padding: 10px 16px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.state-value::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.state-value:hover::before {
  left: 100%;
}

.state-value.normal-operation {
  background: var(--success);
  color: white;
  box-shadow: 0 0 16px rgba(16, 185, 129, 0.4);
  animation: pulse 3s infinite;
}

.state-value.emergency-detected {
  background: var(--error);
  color: white;
  box-shadow: 0 0 16px rgba(239, 68, 68, 0.6);
  animation: glow 1.5s infinite;
}

.state-value.corridor-planning {
  background: var(--warning);
  color: white;
  box-shadow: 0 0 16px rgba(245, 158, 11, 0.4);
  animation: shimmer 2s infinite;
}

.state-value.signal-hijacking {
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 0 16px rgba(59, 130, 246, 0.6);
  animation: glow 2s infinite;
}

.state-value.corridor-active {
  background: var(--info);
  color: white;
  box-shadow: 0 0 16px rgba(6, 182, 212, 0.6);
  animation: float 3s infinite;
}

.legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #e2e8f0;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-color.red { background: #ef4444; }
.legend-color.yellow { background: #f59e0b; }
.legend-color.green { background: #10b981; }
.legend-color.hijacked { 
  background: #3b82f6; 
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}
.legend-color.ambulance { background: #ffffff; }

/* Right Panel - Analytics */
.right-panel {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.analytics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.analytics-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  transition: all 0.2s ease;
}

.analytics-card:hover {
  background: #f1f5f9;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.analytics-title {
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.analytics-trend {
  font-size: 11px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.analytics-trend.positive {
  background: #dcfce7;
  color: #166534;
}

.analytics-trend.negative {
  background: #fecaca;
  color: #991b1b;
}

.analytics-value {
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 12px;
}

.analytics-chart {
  height: 40px;
  background: #e2e8f0;
  border-radius: 4px;
  display: flex;
  align-items: end;
  justify-content: center;
  padding: 4px;
}

.analytics-chart .chart-bar {
  width: 100%;
  background: linear-gradient(to top, #2563eb, #3b82f6);
  border-radius: 2px;
  transition: height 0.3s ease;
  min-height: 8px;
}

.capabilities-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.capability-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 12px;
  text-align: center;
  transition: all 0.2s ease;
}

.capability-card:hover {
  background: #f1f5f9;
}

.capability-card .capability-icon {
  font-size: 20px;
  margin-bottom: 8px;
}

.capability-card .capability-title {
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.capability-card .capability-metric {
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
}

.benefits-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px;
  background: rgba(15, 23, 42, 0.4);
  border: 1px solid #334155;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.benefit-item:hover {
  border-color: #10b981;
  transform: translateX(3px);
}

.benefit-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.benefit-title {
  font-size: 12px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 2px;
}

.benefit-desc {
  font-size: 10px;
  color: #94a3b8;
}

.activity-log {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.activity-entry {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-size: 11px;
  transition: all 0.2s ease;
}

.activity-entry:hover {
  background: #f1f5f9;
}

.activity-time {
  color: #64748b;
  font-weight: 500;
  font-size: 10px;
}

.activity-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.activity-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-indicator.emergency {
  background: #dc2626;
}

.activity-indicator.success {
  background: #059669;
}

.activity-indicator.info {
  background: #2563eb;
}

.activity-message {
  color: #374151;
  font-weight: 500;
}

/* Ambulance marker styling */
.ambulance-marker-enterprise {
  animation: ambulance-pulse 2s infinite;
}

@keyframes ambulance-pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Stunning Leaflet Popup Customization */
.leaflet-popup-content-wrapper {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  color: var(--text-primary);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-2xl);
  transition: all 0.3s ease;
}

.leaflet-popup-tip {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
}

/* Custom Leaflet Controls */
.leaflet-control-zoom a {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.leaflet-control-zoom a:hover {
  background: var(--accent-primary);
  color: white;
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.leaflet-control-zoom {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

/* Enhanced Popup Content */
.leaflet-popup-content {
  margin: 0;
  padding: 0;
}

.leaflet-popup h4 {
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.leaflet-popup .popup-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: inline-block;
  margin-top: 4px;
}

.leaflet-popup .popup-status.red {
  background: var(--error);
  color: white;
}

.leaflet-popup .popup-status.yellow {
  background: var(--warning);
  color: white;
}

.leaflet-popup .popup-status.green {
  background: var(--success);
  color: white;
}

.leaflet-popup .popup-status.ai-controlled {
  background: var(--accent-primary);
  color: white;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
}

/* Scrollbar styling */
.swarm-features::-webkit-scrollbar,
.benefits-list::-webkit-scrollbar,
.emergency-log::-webkit-scrollbar {
  width: 4px;
}

.swarm-features::-webkit-scrollbar-track,
.benefits-list::-webkit-scrollbar-track,
.emergency-log::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.5);
  border-radius: 2px;
}

.swarm-features::-webkit-scrollbar-thumb,
.benefits-list::-webkit-scrollbar-thumb,
.emergency-log::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 2px;
}

.swarm-features::-webkit-scrollbar-thumb:hover,
.benefits-list::-webkit-scrollbar-thumb:hover,
.emergency-log::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .enterprise-content {
    grid-template-columns: 280px 1fr 280px;
  }

  .metrics-container,
  .ai-performance-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .enterprise-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 16px;
  }

  .left-panel,
  .right-panel {
    flex-direction: row;
    gap: 16px;
  }

  .enterprise-panel {
    flex: 1;
    min-height: 250px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .enterprise-map {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .traffic-hijacking {
    padding: 16px;
  }

  .enterprise-content {
    gap: 12px;
  }

  .left-panel,
  .right-panel {
    flex-direction: column;
  }

  .metrics-container,
  .ai-performance-grid,
  .analytics-grid,
  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .enterprise-map {
    height: 300px;
  }

  .map-overlay {
    position: relative;
    margin-top: 10px;
    flex-direction: row;
    gap: 12px;
  }

  .map-controls {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .enterprise-btn {
    padding: 8px 16px;
    font-size: 12px;
  }

  .header-info h1 {
    font-size: 20px;
  }

  .enterprise-panel {
    padding: 16px;
  }

  .enterprise-map {
    height: 250px;
  }

  .header-status {
    flex-direction: column;
    gap: 12px;
  }
}
