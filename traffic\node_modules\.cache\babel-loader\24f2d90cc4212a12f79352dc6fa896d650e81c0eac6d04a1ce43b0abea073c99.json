{"ast": null, "code": "var _jsxFileName = \"D:\\\\EMBEDDED\\\\Project\\\\traffic\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport \"./App.css\";\nimport TrafficDashboard from \"./components/TrafficDashboard\";\nimport PatientTransportOptimizer from \"./components/PatientTransportOptimizer\";\nimport TrafficSignalHijacking from \"./components/TrafficSignalHijacking\";\nimport MobileApp from \"./components/MobileApp\";\nimport Storyboard from \"./components/Storyboard\";\nimport ARNavigation from \"./components/ARNavigation\";\n\n// Major locations and landmarks in Chennai\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst majorLocations = [{\n  id: 1,\n  name: \"Chennai Central Railway Station\",\n  coords: {\n    lat: 13.0827,\n    lng: 80.2707\n  },\n  type: \"transport\",\n  traffic: \"high\"\n}, {\n  id: 2,\n  name: \"Marina Beach\",\n  coords: {\n    lat: 13.0500,\n    lng: 80.2824\n  },\n  type: \"tourist\",\n  traffic: \"medium\"\n}, {\n  id: 3,\n  name: \"T Nagar Commercial Complex\",\n  coords: {\n    lat: 13.0418,\n    lng: 80.2341\n  },\n  type: \"commercial\",\n  traffic: \"very_high\"\n}, {\n  id: 4,\n  name: \"Velachery IT Corridor\",\n  coords: {\n    lat: 12.9756,\n    lng: 80.2207\n  },\n  type: \"business\",\n  traffic: \"high\"\n}, {\n  id: 5,\n  name: \"Adyar Residential Area\",\n  coords: {\n    lat: 13.0067,\n    lng: 80.2206\n  },\n  type: \"residential\",\n  traffic: \"low\"\n}, {\n  id: 6,\n  name: \"Anna Nagar Metro Station\",\n  coords: {\n    lat: 13.0850,\n    lng: 80.2101\n  },\n  type: \"transport\",\n  traffic: \"medium\"\n}, {\n  id: 7,\n  name: \"Guindy Industrial Estate\",\n  coords: {\n    lat: 13.0067,\n    lng: 80.2206\n  },\n  type: \"industrial\",\n  traffic: \"medium\"\n}, {\n  id: 8,\n  name: \"Anna Salai Business District\",\n  coords: {\n    lat: 13.0569,\n    lng: 80.2378\n  },\n  type: \"business\",\n  traffic: \"very_high\"\n}, {\n  id: 9,\n  name: \"OMR IT Corridor\",\n  coords: {\n    lat: 12.9716,\n    lng: 80.2341\n  },\n  type: \"tech\",\n  traffic: \"high\"\n}, {\n  id: 10,\n  name: \"Chennai Airport\",\n  coords: {\n    lat: 13.1986,\n    lng: 80.1811\n  },\n  type: \"transport\",\n  traffic: \"high\"\n}];\n\n// Traffic routes with density data\nconst trafficRoutes = [{\n  id: 1,\n  name: \"Anna Salai - Main Corridor\",\n  path: [{\n    lat: 13.0827,\n    lng: 80.2707\n  }, {\n    lat: 13.0569,\n    lng: 80.2378\n  }, {\n    lat: 13.0418,\n    lng: 80.2341\n  }],\n  density: \"very_high\",\n  avgSpeed: 15,\n  congestionLevel: 85,\n  estimatedTime: \"25 mins\",\n  color: \"#dc2626\"\n}, {\n  id: 2,\n  name: \"OMR Tech Corridor\",\n  path: [{\n    lat: 13.0732,\n    lng: 80.2609\n  }, {\n    lat: 12.9716,\n    lng: 80.2341\n  }, {\n    lat: 12.9141,\n    lng: 80.2270\n  }],\n  density: \"high\",\n  avgSpeed: 35,\n  congestionLevel: 70,\n  estimatedTime: \"18 mins\",\n  color: \"#f59e0b\"\n}, {\n  id: 3,\n  name: \"ECR Coastal Route\",\n  path: [{\n    lat: 13.0500,\n    lng: 80.2824\n  }, {\n    lat: 12.9716,\n    lng: 80.2341\n  }, {\n    lat: 12.8956,\n    lng: 80.2267\n  }],\n  density: \"medium\",\n  avgSpeed: 45,\n  congestionLevel: 45,\n  estimatedTime: \"22 mins\",\n  color: \"#10b981\"\n}, {\n  id: 4,\n  name: \"GST Road Industrial\",\n  path: [{\n    lat: 13.1986,\n    lng: 80.1811\n  }, {\n    lat: 13.0067,\n    lng: 80.2206\n  }, {\n    lat: 12.9141,\n    lng: 80.2270\n  }],\n  density: \"medium\",\n  avgSpeed: 40,\n  congestionLevel: 55,\n  estimatedTime: \"20 mins\",\n  color: \"#3b82f6\"\n}, {\n  id: 5,\n  name: \"Inner Ring Road\",\n  path: [{\n    lat: 13.0827,\n    lng: 80.2707\n  }, {\n    lat: 13.0732,\n    lng: 80.2609\n  }, {\n    lat: 13.0569,\n    lng: 80.2378\n  }, {\n    lat: 13.0418,\n    lng: 80.2341\n  }],\n  density: \"high\",\n  avgSpeed: 25,\n  congestionLevel: 75,\n  estimatedTime: \"30 mins\",\n  color: \"#f59e0b\"\n}];\nconst App = () => {\n  _s();\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [darkMode, setDarkMode] = useState(false);\n  const [map, setMap] = useState(null);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [trafficData, setTrafficData] = useState({\n    congestion: 65,\n    incidents: 3,\n    avgSpeed: 28,\n    totalVehicles: 45230,\n    activeRoutes: 5\n  });\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [systemStatus, setSystemStatus] = useState(\"operational\");\n  const mapRef = useRef(null);\n  const [trafficIncidents, setTrafficIncidents] = useState([{\n    id: \"INC001\",\n    type: \"road accident\",\n    severity: \"high\",\n    location: \"Anna Salai - Thousand Lights\",\n    coords: {\n      lat: 13.0569,\n      lng: 80.2378\n    },\n    reportedAt: new Date(Date.now() - 300000),\n    // 5 minutes ago\n    status: \"active\",\n    affectedRoutes: [\"Anna Salai - Main Corridor\"]\n  }, {\n    id: \"INC002\",\n    type: \"vehicle breakdown\",\n    severity: \"medium\",\n    location: \"OMR - Thoraipakkam\",\n    coords: {\n      lat: 12.9716,\n      lng: 80.2341\n    },\n    reportedAt: new Date(Date.now() - 180000),\n    // 3 minutes ago\n    status: \"clearing\",\n    affectedRoutes: [\"OMR Tech Corridor\"]\n  }]);\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Simulate real-time data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Update traffic data\n      setTrafficData(prev => ({\n        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),\n        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),\n        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))\n      }));\n\n      // Simulate ambulance position updates\n      setAmbulances(prev => prev.map(amb => ({\n        ...amb,\n        location: {\n          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,\n          lng: amb.location.lng + (Math.random() - 0.5) * 0.001\n        }\n      })));\n    }, 5000);\n    return () => clearInterval(interval);\n  }, []);\n  useEffect(() => {\n    const initMap = () => {\n      const newMap = new window.google.maps.Map(document.getElementById(\"emergency-map\"), {\n        zoom: 12,\n        center: {\n          lat: 13.0827,\n          lng: 80.2707\n        },\n        styles: [{\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#0a0f1e\"\n          }]\n        }, {\n          elementType: \"labels.text.stroke\",\n          stylers: [{\n            color: \"#0a0f1e\"\n          }]\n        }, {\n          elementType: \"labels.text.fill\",\n          stylers: [{\n            color: \"#00e5ff\"\n          }]\n        }, {\n          featureType: \"road\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#1a2332\"\n          }]\n        }, {\n          featureType: \"road\",\n          elementType: \"geometry.stroke\",\n          stylers: [{\n            color: \"#00e5ff\"\n          }]\n        }, {\n          featureType: \"water\",\n          elementType: \"geometry\",\n          stylers: [{\n            color: \"#001122\"\n          }]\n        }]\n      });\n      const trafficLayer = new window.google.maps.TrafficLayer();\n      trafficLayer.setMap(newMap);\n      setMap(newMap);\n      mapRef.current = newMap;\n    };\n    const loadGoogleMapsScript = () => {\n      if (!window.google || !window.google.maps) {\n        const script = document.createElement(\"script\");\n        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;\n        script.async = true;\n        script.defer = true;\n        document.body.appendChild(script);\n        script.onload = initMap;\n      } else {\n        initMap();\n      }\n    };\n    loadGoogleMapsScript();\n  }, []);\n\n  // Utility functions for ambulance management\n  const getStatusColor = status => {\n    switch (status) {\n      case 'available':\n        return '#00ff88';\n      case 'dispatched':\n        return '#ffaa00';\n      case 'en-route':\n        return '#ff4444';\n      case 'maintenance':\n        return '#666666';\n      default:\n        return '#00e5ff';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'critical':\n        return '#ff0044';\n      case 'high':\n        return '#ff6600';\n      case 'medium':\n        return '#ffaa00';\n      case 'low':\n        return '#00ff88';\n      default:\n        return '#00e5ff';\n    }\n  };\n  const formatTime = date => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n  const calculateDistance = (lat1, lng1, lat2, lng2) => {\n    const R = 6371; // Earth's radius in km\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLng = (lng2 - lng1) * Math.PI / 180;\n    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) + Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * Math.sin(dLng / 2) * Math.sin(dLng / 2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));\n    return R * c;\n  };\n  const findNearestAmbulance = emergencyCoords => {\n    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');\n    if (availableAmbulances.length === 0) return null;\n    return availableAmbulances.reduce((nearest, current) => {\n      const currentDistance = calculateDistance(emergencyCoords.lat, emergencyCoords.lng, current.location.lat, current.location.lng);\n      const nearestDistance = calculateDistance(emergencyCoords.lat, emergencyCoords.lng, nearest.location.lat, nearest.location.lng);\n      return currentDistance < nearestDistance ? current : nearest;\n    });\n  };\n  const dispatchAmbulance = (emergencyId, ambulanceId) => {\n    setAmbulances(prev => prev.map(amb => amb.id === ambulanceId ? {\n      ...amb,\n      status: 'dispatched'\n    } : amb));\n    setActiveEmergencies(prev => prev.map(emergency => emergency.id === emergencyId ? {\n      ...emergency,\n      assignedAmbulance: ambulanceId,\n      status: 'dispatched'\n    } : emergency));\n  };\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return /*#__PURE__*/_jsxDEV(TrafficDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 16\n        }, this);\n      case 'transport':\n        return /*#__PURE__*/_jsxDEV(PatientTransportOptimizer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 16\n        }, this);\n      case 'traffic-signals':\n        return /*#__PURE__*/_jsxDEV(TrafficSignalHijacking, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 16\n        }, this);\n      case 'mobile':\n        return /*#__PURE__*/_jsxDEV(MobileApp, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 16\n        }, this);\n      case 'storyboard':\n        return /*#__PURE__*/_jsxDEV(Storyboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 16\n        }, this);\n      case 'ar-navigation':\n        return /*#__PURE__*/_jsxDEV(ARNavigation, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(TrafficDashboard, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"app-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-left\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"app-title\",\n          children: \"\\uD83D\\uDEA6 Chennai Traffic Management System\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"main-navigation\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('dashboard'),\n          children: \"\\uD83D\\uDDFA\\uFE0F Traffic Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'transport' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('transport'),\n          children: \"\\uD83D\\uDE91 Transport Optimizer\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 314,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'traffic-signals' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('traffic-signals'),\n          children: \"\\uD83D\\uDEA6 Traffic Signals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'mobile' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('mobile'),\n          children: \"\\uD83D\\uDCF1 Mobile App\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'ar-navigation' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('ar-navigation'),\n          children: \"\\uD83E\\uDD7D AR Navigation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: `nav-btn ${currentPage === 'storyboard' ? 'active' : ''}`,\n          onClick: () => setCurrentPage('storyboard'),\n          children: \"\\uD83D\\uDCCB System Overview\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 301,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-grid\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"panel emergency-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDEA8 ACTIVE EMERGENCIES\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"emergency-count\",\n            children: activeEmergencies.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"emergency-list\",\n          children: activeEmergencies.map(emergency => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `emergency-card priority-${emergency.priority}`,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"emergency-header\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"emergency-id\",\n                children: emergency.id\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `priority-badge ${emergency.priority}`,\n                children: emergency.priority.toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"emergency-details\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-type\",\n                children: emergency.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 366,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-location\",\n                children: [\"\\uD83D\\uDCCD \", emergency.location]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 367,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"emergency-time\",\n                children: [\"\\u23F1\\uFE0F \", Math.floor((currentTime - emergency.reportedAt) / 60000), \" min ago\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 368,\n                columnNumber: 19\n              }, this), emergency.assignedAmbulance && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"assigned-ambulance\",\n                children: [\"\\uD83D\\uDE91 \", emergency.assignedAmbulance, \" - \", emergency.status]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 372,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 365,\n              columnNumber: 17\n            }, this)]\n          }, emergency.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"panel analytics-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA TRAFFIC ANALYTICS\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 385,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"analytics-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-value\",\n              children: [trafficData.congestion, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-label\",\n              children: \"Traffic Congestion\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 390,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`,\n              children: trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-value\",\n              children: trafficData.incidents\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-label\",\n              children: \"Active Incidents\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`,\n              children: trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"metric-card\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-value\",\n              children: [trafficData.avgSpeed, \" km/h\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 403,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"metric-label\",\n              children: \"Avg Speed\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`,\n              children: trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 405,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 402,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"panel fleet-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDE91 AMBULANCE FLEET\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"fleet-summary\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"available-count\",\n              children: [ambulances.filter(a => a.status === 'available').length, \" Available\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"active-count\",\n              children: [ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length, \" Active\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 414,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ambulance-grid\",\n          children: ambulances.map(ambulance => {\n            var _selectedAmbulance;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `ambulance-card ${ambulance.status} ${((_selectedAmbulance = selectedAmbulance) === null || _selectedAmbulance === void 0 ? void 0 : _selectedAmbulance.id) === ambulance.id ? 'selected' : ''}`,\n              onClick: () => setSelectedAmbulance(ambulance),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ambulance-header\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"ambulance-id\",\n                  children: ambulance.id\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"status-dot\",\n                  style: {\n                    backgroundColor: getStatusColor(ambulance.status)\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ambulance-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"crew-info\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83D\\uDC68\\u200D\\u2695\\uFE0F \", ambulance.driver]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 441,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [\"\\uD83E\\uDE7A \", ambulance.medic]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 442,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"vehicle-stats\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"fuel-level\",\n                    children: [\"\\u26FD \", ambulance.fuel, \"%\", /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"fuel-bar\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"fuel-fill\",\n                        style: {\n                          width: `${ambulance.fuel}%`,\n                          backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 448,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 447,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), ambulance.emergency && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"emergency-assignment\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"assignment-type\",\n                    children: ambulance.emergency.type\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 460,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"assignment-eta\",\n                    children: [\"ETA: \", ambulance.emergency.eta]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 461,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 459,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 17\n              }, this)]\n            }, ambulance.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 413,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"panel map-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"panel-header\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDDFA\\uFE0F REAL-TIME MAP\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"map-controls\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"map-btn\",\n              children: \"Traffic Layer\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"map-btn\",\n              children: \"Hospitals\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"map-btn\",\n              children: \"Routes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          id: \"emergency-map\",\n          className: \"emergency-map\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 5\n  }, this);\n};\n_s(App, \"cBy+KXtZmUxy2gcYDgjLyD0Zqu0=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "TrafficDashboard", "PatientTransportOptimizer", "TrafficSignalHijacking", "MobileApp", "Storyboard", "ARNavigation", "jsxDEV", "_jsxDEV", "majorLocations", "id", "name", "coords", "lat", "lng", "type", "traffic", "trafficRoutes", "path", "density", "avgSpeed", "congestionLevel", "estimatedTime", "color", "App", "_s", "currentPage", "setCurrentPage", "darkMode", "setDarkMode", "map", "setMap", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedRoute", "trafficData", "setTrafficData", "congestion", "incidents", "totalVehicles", "activeRoutes", "currentTime", "setCurrentTime", "Date", "systemStatus", "setSystemStatus", "mapRef", "trafficIncidents", "setTrafficIncidents", "severity", "location", "reportedAt", "now", "status", "affectedRoutes", "timer", "setInterval", "clearInterval", "interval", "prev", "Math", "max", "min", "random", "setAmbulances", "amb", "initMap", "newMap", "window", "google", "maps", "Map", "document", "getElementById", "zoom", "center", "styles", "elementType", "stylers", "featureType", "trafficLayer", "TrafficLayer", "current", "loadGoogleMapsScript", "script", "createElement", "src", "async", "defer", "body", "append<PERSON><PERSON><PERSON>", "onload", "getStatusColor", "getPriorityColor", "priority", "formatTime", "date", "toLocaleTimeString", "hour12", "hour", "minute", "second", "calculateDistance", "lat1", "lng1", "lat2", "lng2", "R", "dLat", "PI", "dLng", "a", "sin", "cos", "c", "atan2", "sqrt", "findNearestAmbulance", "emergencyCoords", "availableAmbulances", "ambulances", "filter", "length", "reduce", "nearest", "currentDistance", "nearestDistance", "dispatchAmbulance", "emergencyId", "ambulanceId", "setActiveEmergencies", "emergency", "assignedAmbulance", "renderCurrentPage", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "activeEmergencies", "toUpperCase", "floor", "ambulance", "_selectedAmbulance", "selectedAmbulance", "setSelectedAmbulance", "style", "backgroundColor", "driver", "medic", "fuel", "width", "eta", "_c", "$RefreshReg$"], "sources": ["D:/EMBEDDED/Project/traffic/src/App.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\nimport \"./App.css\";\nimport TrafficDashboard from \"./components/TrafficDashboard\";\nimport PatientTransportOptimizer from \"./components/PatientTransportOptimizer\";\nimport TrafficSignalHijacking from \"./components/TrafficSignalHijacking\";\nimport MobileApp from \"./components/MobileApp\";\nimport Storyboard from \"./components/Storyboard\";\nimport ARNavigation from \"./components/ARNavigation\";\n\n// Major locations and landmarks in Chennai\nconst majorLocations = [\n  { id: 1, name: \"Chennai Central Railway Station\", coords: { lat: 13.0827, lng: 80.2707 }, type: \"transport\", traffic: \"high\" },\n  { id: 2, name: \"Marina Beach\", coords: { lat: 13.0500, lng: 80.2824 }, type: \"tourist\", traffic: \"medium\" },\n  { id: 3, name: \"T Nagar Commercial Complex\", coords: { lat: 13.0418, lng: 80.2341 }, type: \"commercial\", traffic: \"very_high\" },\n  { id: 4, name: \"Velachery IT Corridor\", coords: { lat: 12.9756, lng: 80.2207 }, type: \"business\", traffic: \"high\" },\n  { id: 5, name: \"Adyar Residential Area\", coords: { lat: 13.0067, lng: 80.2206 }, type: \"residential\", traffic: \"low\" },\n  { id: 6, name: \"Anna Nagar Metro Station\", coords: { lat: 13.0850, lng: 80.2101 }, type: \"transport\", traffic: \"medium\" },\n  { id: 7, name: \"Guindy Industrial Estate\", coords: { lat: 13.0067, lng: 80.2206 }, type: \"industrial\", traffic: \"medium\" },\n  { id: 8, name: \"Anna Salai Business District\", coords: { lat: 13.0569, lng: 80.2378 }, type: \"business\", traffic: \"very_high\" },\n  { id: 9, name: \"OMR IT Corridor\", coords: { lat: 12.9716, lng: 80.2341 }, type: \"tech\", traffic: \"high\" },\n  { id: 10, name: \"Chennai Airport\", coords: { lat: 13.1986, lng: 80.1811 }, type: \"transport\", traffic: \"high\" }\n];\n\n// Traffic routes with density data\nconst trafficRoutes = [\n  {\n    id: 1,\n    name: \"Anna Salai - Main Corridor\",\n    path: [\n      { lat: 13.0827, lng: 80.2707 },\n      { lat: 13.0569, lng: 80.2378 },\n      { lat: 13.0418, lng: 80.2341 }\n    ],\n    density: \"very_high\",\n    avgSpeed: 15,\n    congestionLevel: 85,\n    estimatedTime: \"25 mins\",\n    color: \"#dc2626\"\n  },\n  {\n    id: 2,\n    name: \"OMR Tech Corridor\",\n    path: [\n      { lat: 13.0732, lng: 80.2609 },\n      { lat: 12.9716, lng: 80.2341 },\n      { lat: 12.9141, lng: 80.2270 }\n    ],\n    density: \"high\",\n    avgSpeed: 35,\n    congestionLevel: 70,\n    estimatedTime: \"18 mins\",\n    color: \"#f59e0b\"\n  },\n  {\n    id: 3,\n    name: \"ECR Coastal Route\",\n    path: [\n      { lat: 13.0500, lng: 80.2824 },\n      { lat: 12.9716, lng: 80.2341 },\n      { lat: 12.8956, lng: 80.2267 }\n    ],\n    density: \"medium\",\n    avgSpeed: 45,\n    congestionLevel: 45,\n    estimatedTime: \"22 mins\",\n    color: \"#10b981\"\n  },\n  {\n    id: 4,\n    name: \"GST Road Industrial\",\n    path: [\n      { lat: 13.1986, lng: 80.1811 },\n      { lat: 13.0067, lng: 80.2206 },\n      { lat: 12.9141, lng: 80.2270 }\n    ],\n    density: \"medium\",\n    avgSpeed: 40,\n    congestionLevel: 55,\n    estimatedTime: \"20 mins\",\n    color: \"#3b82f6\"\n  },\n  {\n    id: 5,\n    name: \"Inner Ring Road\",\n    path: [\n      { lat: 13.0827, lng: 80.2707 },\n      { lat: 13.0732, lng: 80.2609 },\n      { lat: 13.0569, lng: 80.2378 },\n      { lat: 13.0418, lng: 80.2341 }\n    ],\n    density: \"high\",\n    avgSpeed: 25,\n    congestionLevel: 75,\n    estimatedTime: \"30 mins\",\n    color: \"#f59e0b\"\n  }\n];\n\nconst App = () => {\n  const [currentPage, setCurrentPage] = useState('dashboard');\n  const [darkMode, setDarkMode] = useState(false);\n  const [map, setMap] = useState(null);\n  const [selectedRoute, setSelectedRoute] = useState(null);\n  const [trafficData, setTrafficData] = useState({\n    congestion: 65,\n    incidents: 3,\n    avgSpeed: 28,\n    totalVehicles: 45230,\n    activeRoutes: 5\n  });\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [systemStatus, setSystemStatus] = useState(\"operational\");\n  const mapRef = useRef(null);\n  const [trafficIncidents, setTrafficIncidents] = useState([\n    {\n      id: \"INC001\",\n      type: \"road accident\",\n      severity: \"high\",\n      location: \"Anna Salai - Thousand Lights\",\n      coords: { lat: 13.0569, lng: 80.2378 },\n      reportedAt: new Date(Date.now() - 300000), // 5 minutes ago\n      status: \"active\",\n      affectedRoutes: [\"Anna Salai - Main Corridor\"]\n    },\n    {\n      id: \"INC002\",\n      type: \"vehicle breakdown\",\n      severity: \"medium\",\n      location: \"OMR - Thoraipakkam\",\n      coords: { lat: 12.9716, lng: 80.2341 },\n      reportedAt: new Date(Date.now() - 180000), // 3 minutes ago\n      status: \"clearing\",\n      affectedRoutes: [\"OMR Tech Corridor\"]\n    }\n  ]);\n\n  // Real-time clock update\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Simulate real-time data updates\n  useEffect(() => {\n    const interval = setInterval(() => {\n      // Update traffic data\n      setTrafficData(prev => ({\n        congestion: Math.max(20, Math.min(95, prev.congestion + (Math.random() - 0.5) * 10)),\n        incidents: Math.max(0, prev.incidents + (Math.random() > 0.7 ? 1 : Math.random() < 0.3 ? -1 : 0)),\n        avgSpeed: Math.max(15, Math.min(45, prev.avgSpeed + (Math.random() - 0.5) * 5))\n      }));\n\n      // Simulate ambulance position updates\n      setAmbulances(prev => prev.map(amb => ({\n        ...amb,\n        location: {\n          lat: amb.location.lat + (Math.random() - 0.5) * 0.001,\n          lng: amb.location.lng + (Math.random() - 0.5) * 0.001\n        }\n      })));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  useEffect(() => {\n    const initMap = () => {\n      const newMap = new window.google.maps.Map(document.getElementById(\"emergency-map\"), {\n        zoom: 12,\n        center: { lat: 13.0827, lng: 80.2707 },\n        styles: [\n          { elementType: \"geometry\", stylers: [{ color: \"#0a0f1e\" }] },\n          { elementType: \"labels.text.stroke\", stylers: [{ color: \"#0a0f1e\" }] },\n          { elementType: \"labels.text.fill\", stylers: [{ color: \"#00e5ff\" }] },\n          { featureType: \"road\", elementType: \"geometry\", stylers: [{ color: \"#1a2332\" }] },\n          { featureType: \"road\", elementType: \"geometry.stroke\", stylers: [{ color: \"#00e5ff\" }] },\n          { featureType: \"water\", elementType: \"geometry\", stylers: [{ color: \"#001122\" }] }\n        ]\n      });\n\n      const trafficLayer = new window.google.maps.TrafficLayer();\n      trafficLayer.setMap(newMap);\n\n      setMap(newMap);\n      mapRef.current = newMap;\n    };\n\n    const loadGoogleMapsScript = () => {\n      if (!window.google || !window.google.maps) {\n        const script = document.createElement(\"script\");\n        script.src = `https://maps.googleapis.com/maps/api/js?key=AIzaSyB5-Vbpc3qz3PteK43YFcZOSe3q99gcNX0&libraries=places`;\n        script.async = true;\n        script.defer = true;\n        document.body.appendChild(script);\n        script.onload = initMap;\n      } else {\n        initMap();\n      }\n    };\n\n    loadGoogleMapsScript();\n  }, []);\n\n  // Utility functions for ambulance management\n  const getStatusColor = (status) => {\n    switch (status) {\n      case 'available': return '#00ff88';\n      case 'dispatched': return '#ffaa00';\n      case 'en-route': return '#ff4444';\n      case 'maintenance': return '#666666';\n      default: return '#00e5ff';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'critical': return '#ff0044';\n      case 'high': return '#ff6600';\n      case 'medium': return '#ffaa00';\n      case 'low': return '#00ff88';\n      default: return '#00e5ff';\n    }\n  };\n\n  const formatTime = (date) => {\n    return date.toLocaleTimeString('en-US', {\n      hour12: false,\n      hour: '2-digit',\n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const calculateDistance = (lat1, lng1, lat2, lng2) => {\n    const R = 6371; // Earth's radius in km\n    const dLat = (lat2 - lat1) * Math.PI / 180;\n    const dLng = (lng2 - lng1) * Math.PI / 180;\n    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +\n              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *\n              Math.sin(dLng/2) * Math.sin(dLng/2);\n    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));\n    return R * c;\n  };\n\n  const findNearestAmbulance = (emergencyCoords) => {\n    const availableAmbulances = ambulances.filter(amb => amb.status === 'available');\n    if (availableAmbulances.length === 0) return null;\n\n    return availableAmbulances.reduce((nearest, current) => {\n      const currentDistance = calculateDistance(\n        emergencyCoords.lat, emergencyCoords.lng,\n        current.location.lat, current.location.lng\n      );\n      const nearestDistance = calculateDistance(\n        emergencyCoords.lat, emergencyCoords.lng,\n        nearest.location.lat, nearest.location.lng\n      );\n      return currentDistance < nearestDistance ? current : nearest;\n    });\n  };\n\n  const dispatchAmbulance = (emergencyId, ambulanceId) => {\n    setAmbulances(prev => prev.map(amb =>\n      amb.id === ambulanceId\n        ? { ...amb, status: 'dispatched' }\n        : amb\n    ));\n\n    setActiveEmergencies(prev => prev.map(emergency =>\n      emergency.id === emergencyId\n        ? { ...emergency, assignedAmbulance: ambulanceId, status: 'dispatched' }\n        : emergency\n    ));\n  };\n\n\n  const renderCurrentPage = () => {\n    switch (currentPage) {\n      case 'dashboard':\n        return <TrafficDashboard />;\n      case 'transport':\n        return <PatientTransportOptimizer />;\n      case 'traffic-signals':\n        return <TrafficSignalHijacking />;\n      case 'mobile':\n        return <MobileApp />;\n      case 'storyboard':\n        return <Storyboard />;\n      case 'ar-navigation':\n        return <ARNavigation />;\n      default:\n        return <TrafficDashboard />;\n    }\n  };\n\n  return (\n    <div className=\"app-container\">\n      {/* Navigation Header */}\n      <header className=\"app-header\">\n        <div className=\"header-left\">\n          <h1 className=\"app-title\">\n            🚦 Chennai Traffic Management System\n          </h1>\n        </div>\n        <nav className=\"main-navigation\">\n          <button\n            className={`nav-btn ${currentPage === 'dashboard' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('dashboard')}\n          >\n            🗺️ Traffic Dashboard\n          </button>\n          <button\n            className={`nav-btn ${currentPage === 'transport' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('transport')}\n          >\n            🚑 Transport Optimizer\n          </button>\n          <button\n            className={`nav-btn ${currentPage === 'traffic-signals' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('traffic-signals')}\n          >\n            🚦 Traffic Signals\n          </button>\n          <button\n            className={`nav-btn ${currentPage === 'mobile' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('mobile')}\n          >\n            📱 Mobile App\n          </button>\n          <button\n            className={`nav-btn ${currentPage === 'ar-navigation' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('ar-navigation')}\n          >\n            🥽 AR Navigation\n          </button>\n          <button\n            className={`nav-btn ${currentPage === 'storyboard' ? 'active' : ''}`}\n            onClick={() => setCurrentPage('storyboard')}\n          >\n            📋 System Overview\n          </button>\n        </nav>\n      </header>\n\n      {/* Main dashboard grid */}\n      <div className=\"dashboard-grid\">\n\n        {/* Emergency Alerts Panel */}\n        <div className=\"panel emergency-panel\">\n          <div className=\"panel-header\">\n            <h3>🚨 ACTIVE EMERGENCIES</h3>\n            <span className=\"emergency-count\">{activeEmergencies.length}</span>\n          </div>\n          <div className=\"emergency-list\">\n            {activeEmergencies.map(emergency => (\n              <div key={emergency.id} className={`emergency-card priority-${emergency.priority}`}>\n                <div className=\"emergency-header\">\n                  <span className=\"emergency-id\">{emergency.id}</span>\n                  <span className={`priority-badge ${emergency.priority}`}>\n                    {emergency.priority.toUpperCase()}\n                  </span>\n                </div>\n                <div className=\"emergency-details\">\n                  <div className=\"emergency-type\">{emergency.type}</div>\n                  <div className=\"emergency-location\">📍 {emergency.location}</div>\n                  <div className=\"emergency-time\">\n                    ⏱️ {Math.floor((currentTime - emergency.reportedAt) / 60000)} min ago\n                  </div>\n                  {emergency.assignedAmbulance && (\n                    <div className=\"assigned-ambulance\">\n                      🚑 {emergency.assignedAmbulance} - {emergency.status}\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Traffic Analytics Panel */}\n        <div className=\"panel analytics-panel\">\n          <div className=\"panel-header\">\n            <h3>📊 TRAFFIC ANALYTICS</h3>\n          </div>\n          <div className=\"analytics-grid\">\n            <div className=\"metric-card\">\n              <div className=\"metric-value\">{trafficData.congestion}%</div>\n              <div className=\"metric-label\">Traffic Congestion</div>\n              <div className={`metric-trend ${trafficData.congestion > 70 ? 'high' : 'normal'}`}>\n                {trafficData.congestion > 70 ? '⚠️ HIGH' : '✅ NORMAL'}\n              </div>\n            </div>\n            <div className=\"metric-card\">\n              <div className=\"metric-value\">{trafficData.incidents}</div>\n              <div className=\"metric-label\">Active Incidents</div>\n              <div className={`metric-trend ${trafficData.incidents > 5 ? 'high' : 'normal'}`}>\n                {trafficData.incidents > 5 ? '⚠️ HIGH' : '✅ NORMAL'}\n              </div>\n            </div>\n            <div className=\"metric-card\">\n              <div className=\"metric-value\">{trafficData.avgSpeed} km/h</div>\n              <div className=\"metric-label\">Avg Speed</div>\n              <div className={`metric-trend ${trafficData.avgSpeed < 25 ? 'low' : 'normal'}`}>\n                {trafficData.avgSpeed < 25 ? '🐌 SLOW' : '🚗 NORMAL'}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Ambulance Fleet Panel */}\n        <div className=\"panel fleet-panel\">\n          <div className=\"panel-header\">\n            <h3>🚑 AMBULANCE FLEET</h3>\n            <div className=\"fleet-summary\">\n              <span className=\"available-count\">\n                {ambulances.filter(a => a.status === 'available').length} Available\n              </span>\n              <span className=\"active-count\">\n                {ambulances.filter(a => a.status === 'dispatched' || a.status === 'en-route').length} Active\n              </span>\n            </div>\n          </div>\n          <div className=\"ambulance-grid\">\n            {ambulances.map(ambulance => (\n              <div\n                key={ambulance.id}\n                className={`ambulance-card ${ambulance.status} ${selectedAmbulance?.id === ambulance.id ? 'selected' : ''}`}\n                onClick={() => setSelectedAmbulance(ambulance)}\n              >\n                <div className=\"ambulance-header\">\n                  <span className=\"ambulance-id\">{ambulance.id}</span>\n                  <span\n                    className=\"status-dot\"\n                    style={{ backgroundColor: getStatusColor(ambulance.status) }}\n                  ></span>\n                </div>\n                <div className=\"ambulance-info\">\n                  <div className=\"crew-info\">\n                    <div>👨‍⚕️ {ambulance.driver}</div>\n                    <div>🩺 {ambulance.medic}</div>\n                  </div>\n                  <div className=\"vehicle-stats\">\n                    <div className=\"fuel-level\">\n                      ⛽ {ambulance.fuel}%\n                      <div className=\"fuel-bar\">\n                        <div\n                          className=\"fuel-fill\"\n                          style={{\n                            width: `${ambulance.fuel}%`,\n                            backgroundColor: ambulance.fuel < 30 ? '#ff4444' : '#00ff88'\n                          }}\n                        ></div>\n                      </div>\n                    </div>\n                  </div>\n                  {ambulance.emergency && (\n                    <div className=\"emergency-assignment\">\n                      <div className=\"assignment-type\">{ambulance.emergency.type}</div>\n                      <div className=\"assignment-eta\">ETA: {ambulance.emergency.eta}</div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Map Panel */}\n        <div className=\"panel map-panel\">\n          <div className=\"panel-header\">\n            <h3>🗺️ REAL-TIME MAP</h3>\n            <div className=\"map-controls\">\n              <button className=\"map-btn\">Traffic Layer</button>\n              <button className=\"map-btn\">Hospitals</button>\n              <button className=\"map-btn\">Routes</button>\n            </div>\n          </div>\n          <div id=\"emergency-map\" className=\"emergency-map\"></div>\n        </div>\n\n      </div>\n    </div>\n  );\n};\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,yBAAyB,MAAM,wCAAwC;AAC9E,OAAOC,sBAAsB,MAAM,qCAAqC;AACxE,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,YAAY,MAAM,2BAA2B;;AAEpD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAG,CACrB;EAAEC,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,iCAAiC;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,WAAW;EAAEC,OAAO,EAAE;AAAO,CAAC,EAC9H;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,cAAc;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,SAAS;EAAEC,OAAO,EAAE;AAAS,CAAC,EAC3G;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,4BAA4B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,YAAY;EAAEC,OAAO,EAAE;AAAY,CAAC,EAC/H;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,uBAAuB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,UAAU;EAAEC,OAAO,EAAE;AAAO,CAAC,EACnH;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,wBAAwB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,aAAa;EAAEC,OAAO,EAAE;AAAM,CAAC,EACtH;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,0BAA0B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,WAAW;EAAEC,OAAO,EAAE;AAAS,CAAC,EACzH;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,0BAA0B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,YAAY;EAAEC,OAAO,EAAE;AAAS,CAAC,EAC1H;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,8BAA8B;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,UAAU;EAAEC,OAAO,EAAE;AAAY,CAAC,EAC/H;EAAEN,EAAE,EAAE,CAAC;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,MAAM;EAAEC,OAAO,EAAE;AAAO,CAAC,EACzG;EAAEN,EAAE,EAAE,EAAE;EAAEC,IAAI,EAAE,iBAAiB;EAAEC,MAAM,EAAE;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC;EAAEC,IAAI,EAAE,WAAW;EAAEC,OAAO,EAAE;AAAO,CAAC,CAChH;;AAED;AACA,MAAMC,aAAa,GAAG,CACpB;EACEP,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,4BAA4B;EAClCO,IAAI,EAAE,CACJ;IAAEL,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,CAC/B;EACDK,OAAO,EAAE,WAAW;EACpBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,mBAAmB;EACzBO,IAAI,EAAE,CACJ;IAAEL,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,CAC/B;EACDK,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,mBAAmB;EACzBO,IAAI,EAAE,CACJ;IAAEL,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,CAC/B;EACDK,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,qBAAqB;EAC3BO,IAAI,EAAE,CACJ;IAAEL,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,CAC/B;EACDK,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,KAAK,EAAE;AACT,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBO,IAAI,EAAE,CACJ;IAAEL,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,EAC9B;IAAED,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE;EAAQ,CAAC,CAC/B;EACDK,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,KAAK,EAAE;AACT,CAAC,CACF;AAED,MAAMC,GAAG,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,WAAW,CAAC;EAC3D,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAAC+B,GAAG,EAAEC,MAAM,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACpC,MAAM,CAACiC,aAAa,EAAEC,gBAAgB,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC;IAC7CqC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,CAAC;IACZjB,QAAQ,EAAE,EAAE;IACZkB,aAAa,EAAE,KAAK;IACpBC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,IAAI2C,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG7C,QAAQ,CAAC,aAAa,CAAC;EAC/D,MAAM8C,MAAM,GAAG7C,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAAC8C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhD,QAAQ,CAAC,CACvD;IACEW,EAAE,EAAE,QAAQ;IACZK,IAAI,EAAE,eAAe;IACrBiC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,8BAA8B;IACxCrC,MAAM,EAAE;MAAEC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IACtCoC,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;IAAE;IAC3CC,MAAM,EAAE,QAAQ;IAChBC,cAAc,EAAE,CAAC,4BAA4B;EAC/C,CAAC,EACD;IACE3C,EAAE,EAAE,QAAQ;IACZK,IAAI,EAAE,mBAAmB;IACzBiC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,oBAAoB;IAC9BrC,MAAM,EAAE;MAAEC,GAAG,EAAE,OAAO;MAAEC,GAAG,EAAE;IAAQ,CAAC;IACtCoC,UAAU,EAAE,IAAIR,IAAI,CAACA,IAAI,CAACS,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC;IAAE;IAC3CC,MAAM,EAAE,UAAU;IAClBC,cAAc,EAAE,CAAC,mBAAmB;EACtC,CAAC,CACF,CAAC;;EAEF;EACAvD,SAAS,CAAC,MAAM;IACd,MAAMwD,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9Bd,cAAc,CAAC,IAAIC,IAAI,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMc,aAAa,CAACF,KAAK,CAAC;EACnC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxD,SAAS,CAAC,MAAM;IACd,MAAM2D,QAAQ,GAAGF,WAAW,CAAC,MAAM;MACjC;MACApB,cAAc,CAACuB,IAAI,KAAK;QACtBtB,UAAU,EAAEuB,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACtB,UAAU,GAAG,CAACuB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC;QACpFzB,SAAS,EAAEsB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,IAAI,CAACrB,SAAS,IAAIsB,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGH,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;QACjG1C,QAAQ,EAAEuC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,IAAI,CAACtC,QAAQ,GAAG,CAACuC,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC;MAChF,CAAC,CAAC,CAAC;;MAEH;MACAC,aAAa,CAACL,IAAI,IAAIA,IAAI,CAAC5B,GAAG,CAACkC,GAAG,KAAK;QACrC,GAAGA,GAAG;QACNf,QAAQ,EAAE;UACRpC,GAAG,EAAEmD,GAAG,CAACf,QAAQ,CAACpC,GAAG,GAAG,CAAC8C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK;UACrDhD,GAAG,EAAEkD,GAAG,CAACf,QAAQ,CAACnC,GAAG,GAAG,CAAC6C,IAAI,CAACG,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI;QAClD;MACF,CAAC,CAAC,CAAC,CAAC;IACN,CAAC,EAAE,IAAI,CAAC;IAER,OAAO,MAAMN,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN3D,SAAS,CAAC,MAAM;IACd,MAAMmE,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAMC,MAAM,GAAG,IAAIC,MAAM,CAACC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,QAAQ,CAACC,cAAc,CAAC,eAAe,CAAC,EAAE;QAClFC,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE;UAAE7D,GAAG,EAAE,OAAO;UAAEC,GAAG,EAAE;QAAQ,CAAC;QACtC6D,MAAM,EAAE,CACN;UAAEC,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EAC5D;UAAEqD,WAAW,EAAE,oBAAoB;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACtE;UAAEqD,WAAW,EAAE,kBAAkB;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACpE;UAAEuD,WAAW,EAAE,MAAM;UAAEF,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACjF;UAAEuD,WAAW,EAAE,MAAM;UAAEF,WAAW,EAAE,iBAAiB;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC,EACxF;UAAEuD,WAAW,EAAE,OAAO;UAAEF,WAAW,EAAE,UAAU;UAAEC,OAAO,EAAE,CAAC;YAAEtD,KAAK,EAAE;UAAU,CAAC;QAAE,CAAC;MAEtF,CAAC,CAAC;MAEF,MAAMwD,YAAY,GAAG,IAAIZ,MAAM,CAACC,MAAM,CAACC,IAAI,CAACW,YAAY,CAAC,CAAC;MAC1DD,YAAY,CAAChD,MAAM,CAACmC,MAAM,CAAC;MAE3BnC,MAAM,CAACmC,MAAM,CAAC;MACdrB,MAAM,CAACoC,OAAO,GAAGf,MAAM;IACzB,CAAC;IAED,MAAMgB,oBAAoB,GAAGA,CAAA,KAAM;MACjC,IAAI,CAACf,MAAM,CAACC,MAAM,IAAI,CAACD,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE;QACzC,MAAMc,MAAM,GAAGZ,QAAQ,CAACa,aAAa,CAAC,QAAQ,CAAC;QAC/CD,MAAM,CAACE,GAAG,GAAG,sGAAsG;QACnHF,MAAM,CAACG,KAAK,GAAG,IAAI;QACnBH,MAAM,CAACI,KAAK,GAAG,IAAI;QACnBhB,QAAQ,CAACiB,IAAI,CAACC,WAAW,CAACN,MAAM,CAAC;QACjCA,MAAM,CAACO,MAAM,GAAGzB,OAAO;MACzB,CAAC,MAAM;QACLA,OAAO,CAAC,CAAC;MACX;IACF,CAAC;IAEDiB,oBAAoB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,cAAc,GAAIvC,MAAM,IAAK;IACjC,QAAQA,MAAM;MACZ,KAAK,WAAW;QAAE,OAAO,SAAS;MAClC,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMwC,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,UAAU;QAAE,OAAO,SAAS;MACjC,KAAK,MAAM;QAAE,OAAO,SAAS;MAC7B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,KAAK;QAAE,OAAO,SAAS;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,UAAU,GAAIC,IAAI,IAAK;IAC3B,OAAOA,IAAI,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACtCC,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI,KAAK;IACpD,MAAMC,CAAC,GAAG,IAAI,CAAC,CAAC;IAChB,MAAMC,IAAI,GAAG,CAACH,IAAI,GAAGF,IAAI,IAAI3C,IAAI,CAACiD,EAAE,GAAG,GAAG;IAC1C,MAAMC,IAAI,GAAG,CAACJ,IAAI,GAAGF,IAAI,IAAI5C,IAAI,CAACiD,EAAE,GAAG,GAAG;IAC1C,MAAME,CAAC,GAAGnD,IAAI,CAACoD,GAAG,CAACJ,IAAI,GAAC,CAAC,CAAC,GAAGhD,IAAI,CAACoD,GAAG,CAACJ,IAAI,GAAC,CAAC,CAAC,GACnChD,IAAI,CAACqD,GAAG,CAACV,IAAI,GAAG3C,IAAI,CAACiD,EAAE,GAAG,GAAG,CAAC,GAAGjD,IAAI,CAACqD,GAAG,CAACR,IAAI,GAAG7C,IAAI,CAACiD,EAAE,GAAG,GAAG,CAAC,GAC/DjD,IAAI,CAACoD,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC,GAAGlD,IAAI,CAACoD,GAAG,CAACF,IAAI,GAAC,CAAC,CAAC;IAC7C,MAAMI,CAAC,GAAG,CAAC,GAAGtD,IAAI,CAACuD,KAAK,CAACvD,IAAI,CAACwD,IAAI,CAACL,CAAC,CAAC,EAAEnD,IAAI,CAACwD,IAAI,CAAC,CAAC,GAACL,CAAC,CAAC,CAAC;IACtD,OAAOJ,CAAC,GAAGO,CAAC;EACd,CAAC;EAED,MAAMG,oBAAoB,GAAIC,eAAe,IAAK;IAChD,MAAMC,mBAAmB,GAAGC,UAAU,CAACC,MAAM,CAACxD,GAAG,IAAIA,GAAG,CAACZ,MAAM,KAAK,WAAW,CAAC;IAChF,IAAIkE,mBAAmB,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEjD,OAAOH,mBAAmB,CAACI,MAAM,CAAC,CAACC,OAAO,EAAE1C,OAAO,KAAK;MACtD,MAAM2C,eAAe,GAAGvB,iBAAiB,CACvCgB,eAAe,CAACxG,GAAG,EAAEwG,eAAe,CAACvG,GAAG,EACxCmE,OAAO,CAAChC,QAAQ,CAACpC,GAAG,EAAEoE,OAAO,CAAChC,QAAQ,CAACnC,GACzC,CAAC;MACD,MAAM+G,eAAe,GAAGxB,iBAAiB,CACvCgB,eAAe,CAACxG,GAAG,EAAEwG,eAAe,CAACvG,GAAG,EACxC6G,OAAO,CAAC1E,QAAQ,CAACpC,GAAG,EAAE8G,OAAO,CAAC1E,QAAQ,CAACnC,GACzC,CAAC;MACD,OAAO8G,eAAe,GAAGC,eAAe,GAAG5C,OAAO,GAAG0C,OAAO;IAC9D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAACC,WAAW,EAAEC,WAAW,KAAK;IACtDjE,aAAa,CAACL,IAAI,IAAIA,IAAI,CAAC5B,GAAG,CAACkC,GAAG,IAChCA,GAAG,CAACtD,EAAE,KAAKsH,WAAW,GAClB;MAAE,GAAGhE,GAAG;MAAEZ,MAAM,EAAE;IAAa,CAAC,GAChCY,GACN,CAAC,CAAC;IAEFiE,oBAAoB,CAACvE,IAAI,IAAIA,IAAI,CAAC5B,GAAG,CAACoG,SAAS,IAC7CA,SAAS,CAACxH,EAAE,KAAKqH,WAAW,GACxB;MAAE,GAAGG,SAAS;MAAEC,iBAAiB,EAAEH,WAAW;MAAE5E,MAAM,EAAE;IAAa,CAAC,GACtE8E,SACN,CAAC,CAAC;EACJ,CAAC;EAGD,MAAME,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQ1G,WAAW;MACjB,KAAK,WAAW;QACd,oBAAOlB,OAAA,CAACP,gBAAgB;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC7B,KAAK,WAAW;QACd,oBAAOhI,OAAA,CAACN,yBAAyB;UAAAmI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtC,KAAK,iBAAiB;QACpB,oBAAOhI,OAAA,CAACL,sBAAsB;UAAAkI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACnC,KAAK,QAAQ;QACX,oBAAOhI,OAAA,CAACJ,SAAS;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACtB,KAAK,YAAY;QACf,oBAAOhI,OAAA,CAACH,UAAU;UAAAgI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACvB,KAAK,eAAe;QAClB,oBAAOhI,OAAA,CAACF,YAAY;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MACzB;QACE,oBAAOhI,OAAA,CAACP,gBAAgB;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;IAC/B;EACF,CAAC;EAED,oBACEhI,OAAA;IAAKiI,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAE5BlI,OAAA;MAAQiI,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC5BlI,OAAA;QAAKiI,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BlI,OAAA;UAAIiI,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAE1B;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACNhI,OAAA;QAAKiI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACpEiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,WAAW,CAAE;UAAA+G,QAAA,EAC5C;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EAAG;UACpEiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,WAAW,CAAE;UAAA+G,QAAA,EAC5C;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,iBAAiB,GAAG,QAAQ,GAAG,EAAE,EAAG;UAC1EiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,iBAAiB,CAAE;UAAA+G,QAAA,EAClD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;UACjEiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,QAAQ,CAAE;UAAA+G,QAAA,EACzC;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAE,EAAG;UACxEiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,eAAe,CAAE;UAAA+G,QAAA,EAChD;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACThI,OAAA;UACEiI,SAAS,EAAE,WAAW/G,WAAW,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAAG;UACrEiH,OAAO,EAAEA,CAAA,KAAMhH,cAAc,CAAC,YAAY,CAAE;UAAA+G,QAAA,EAC7C;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGThI,OAAA;MAAKiI,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAG7BlI,OAAA;QAAKiI,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClI,OAAA;UAAKiI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlI,OAAA;YAAAkI,QAAA,EAAI;UAAqB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9BhI,OAAA;YAAMiI,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAEE,iBAAiB,CAACnB;UAAM;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC,eACNhI,OAAA;UAAKiI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BE,iBAAiB,CAAC9G,GAAG,CAACoG,SAAS,iBAC9B1H,OAAA;YAAwBiI,SAAS,EAAE,2BAA2BP,SAAS,CAACrC,QAAQ,EAAG;YAAA6C,QAAA,gBACjFlI,OAAA;cAAKiI,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BlI,OAAA;gBAAMiI,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAER,SAAS,CAACxH;cAAE;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACpDhI,OAAA;gBAAMiI,SAAS,EAAE,kBAAkBP,SAAS,CAACrC,QAAQ,EAAG;gBAAA6C,QAAA,EACrDR,SAAS,CAACrC,QAAQ,CAACgD,WAAW,CAAC;cAAC;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNhI,OAAA;cAAKiI,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChClI,OAAA;gBAAKiI,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAAER,SAAS,CAACnH;cAAI;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACtDhI,OAAA;gBAAKiI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,eAAG,EAACR,SAAS,CAACjF,QAAQ;cAAA;gBAAAoF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjEhI,OAAA;gBAAKiI,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,GAAC,eAC3B,EAAC/E,IAAI,CAACmF,KAAK,CAAC,CAACtG,WAAW,GAAG0F,SAAS,CAAChF,UAAU,IAAI,KAAK,CAAC,EAAC,UAC/D;cAAA;gBAAAmF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EACLN,SAAS,CAACC,iBAAiB,iBAC1B3H,OAAA;gBAAKiI,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,GAAC,eAC/B,EAACR,SAAS,CAACC,iBAAiB,EAAC,KAAG,EAACD,SAAS,CAAC9E,MAAM;cAAA;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAlBEN,SAAS,CAACxH,EAAE;YAAA2H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAmBjB,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhI,OAAA;QAAKiI,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpClI,OAAA;UAAKiI,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BlI,OAAA;YAAAkI,QAAA,EAAI;UAAoB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC,eACNhI,OAAA;UAAKiI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BlI,OAAA;YAAKiI,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAExG,WAAW,CAACE,UAAU,EAAC,GAAC;YAAA;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7DhI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAkB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACtDhI,OAAA;cAAKiI,SAAS,EAAE,gBAAgBvG,WAAW,CAACE,UAAU,GAAG,EAAE,GAAG,MAAM,GAAG,QAAQ,EAAG;cAAAsG,QAAA,EAC/ExG,WAAW,CAACE,UAAU,GAAG,EAAE,GAAG,SAAS,GAAG;YAAU;cAAAiG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhI,OAAA;YAAKiI,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAExG,WAAW,CAACG;YAAS;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3DhI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACpDhI,OAAA;cAAKiI,SAAS,EAAE,gBAAgBvG,WAAW,CAACG,SAAS,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ,EAAG;cAAAqG,QAAA,EAC7ExG,WAAW,CAACG,SAAS,GAAG,CAAC,GAAG,SAAS,GAAG;YAAU;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhI,OAAA;YAAKiI,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,GAAExG,WAAW,CAACd,QAAQ,EAAC,OAAK;YAAA;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC/DhI,OAAA;cAAKiI,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7ChI,OAAA;cAAKiI,SAAS,EAAE,gBAAgBvG,WAAW,CAACd,QAAQ,GAAG,EAAE,GAAG,KAAK,GAAG,QAAQ,EAAG;cAAAsH,QAAA,EAC5ExG,WAAW,CAACd,QAAQ,GAAG,EAAE,GAAG,SAAS,GAAG;YAAW;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhI,OAAA;QAAKiI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChClI,OAAA;UAAKiI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlI,OAAA;YAAAkI,QAAA,EAAI;UAAkB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3BhI,OAAA;YAAKiI,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BlI,OAAA;cAAMiI,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAC9BnB,UAAU,CAACC,MAAM,CAACV,CAAC,IAAIA,CAAC,CAAC1D,MAAM,KAAK,WAAW,CAAC,CAACqE,MAAM,EAAC,YAC3D;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACPhI,OAAA;cAAMiI,SAAS,EAAC,cAAc;cAAAC,QAAA,GAC3BnB,UAAU,CAACC,MAAM,CAACV,CAAC,IAAIA,CAAC,CAAC1D,MAAM,KAAK,YAAY,IAAI0D,CAAC,CAAC1D,MAAM,KAAK,UAAU,CAAC,CAACqE,MAAM,EAAC,SACvF;YAAA;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhI,OAAA;UAAKiI,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAC5BnB,UAAU,CAACzF,GAAG,CAACiH,SAAS;YAAA,IAAAC,kBAAA;YAAA,oBACvBxI,OAAA;cAEEiI,SAAS,EAAE,kBAAkBM,SAAS,CAAC3F,MAAM,IAAI,EAAA4F,kBAAA,GAAAC,iBAAiB,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBtI,EAAE,MAAKqI,SAAS,CAACrI,EAAE,GAAG,UAAU,GAAG,EAAE,EAAG;cAC5GiI,OAAO,EAAEA,CAAA,KAAMO,oBAAoB,CAACH,SAAS,CAAE;cAAAL,QAAA,gBAE/ClI,OAAA;gBAAKiI,SAAS,EAAC,kBAAkB;gBAAAC,QAAA,gBAC/BlI,OAAA;kBAAMiI,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEK,SAAS,CAACrI;gBAAE;kBAAA2H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACpDhI,OAAA;kBACEiI,SAAS,EAAC,YAAY;kBACtBU,KAAK,EAAE;oBAAEC,eAAe,EAAEzD,cAAc,CAACoD,SAAS,CAAC3F,MAAM;kBAAE;gBAAE;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACNhI,OAAA;gBAAKiI,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BlI,OAAA;kBAAKiI,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBlI,OAAA;oBAAAkI,QAAA,GAAK,iCAAM,EAACK,SAAS,CAACM,MAAM;kBAAA;oBAAAhB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACnChI,OAAA;oBAAAkI,QAAA,GAAK,eAAG,EAACK,SAAS,CAACO,KAAK;kBAAA;oBAAAjB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B,CAAC,eACNhI,OAAA;kBAAKiI,SAAS,EAAC,eAAe;kBAAAC,QAAA,eAC5BlI,OAAA;oBAAKiI,SAAS,EAAC,YAAY;oBAAAC,QAAA,GAAC,SACxB,EAACK,SAAS,CAACQ,IAAI,EAAC,GAClB,eAAA/I,OAAA;sBAAKiI,SAAS,EAAC,UAAU;sBAAAC,QAAA,eACvBlI,OAAA;wBACEiI,SAAS,EAAC,WAAW;wBACrBU,KAAK,EAAE;0BACLK,KAAK,EAAE,GAAGT,SAAS,CAACQ,IAAI,GAAG;0BAC3BH,eAAe,EAAEL,SAAS,CAACQ,IAAI,GAAG,EAAE,GAAG,SAAS,GAAG;wBACrD;sBAAE;wBAAAlB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EACLO,SAAS,CAACb,SAAS,iBAClB1H,OAAA;kBAAKiI,SAAS,EAAC,sBAAsB;kBAAAC,QAAA,gBACnClI,OAAA;oBAAKiI,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,EAAEK,SAAS,CAACb,SAAS,CAACnH;kBAAI;oBAAAsH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjEhI,OAAA;oBAAKiI,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,GAAC,OAAK,EAACK,SAAS,CAACb,SAAS,CAACuB,GAAG;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA,GApCDO,SAAS,CAACrI,EAAE;cAAA2H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqCd,CAAC;UAAA,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhI,OAAA;QAAKiI,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlI,OAAA;UAAKiI,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BlI,OAAA;YAAAkI,QAAA,EAAI;UAAiB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BhI,OAAA;YAAKiI,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BlI,OAAA;cAAQiI,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAa;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClDhI,OAAA;cAAQiI,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC9ChI,OAAA;cAAQiI,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAC;YAAM;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNhI,OAAA;UAAKE,EAAE,EAAC,eAAe;UAAC+H,SAAS,EAAC;QAAe;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC/G,EAAA,CAnYID,GAAG;AAAAkI,EAAA,GAAHlI,GAAG;AAqYT,eAAeA,GAAG;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}