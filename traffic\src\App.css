@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* CSS Variables for Theme System */
:root {
  /* Light Theme Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-accent: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --accent-primary: #3b82f6;
  --accent-secondary: #1e40af;
  --accent-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #06b6d4;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Glass Effect */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --backdrop-blur: blur(16px);
}

[data-theme="dark"] {
  /* Dark Theme Colors */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-accent: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
  --accent-primary: #60a5fa;
  --accent-secondary: #3b82f6;
  --accent-gradient: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  --success: #34d399;
  --warning: #fbbf24;
  --error: #f87171;
  --info: #22d3ee;

  /* Dark Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.5);

  /* Dark Glass Effect */
  --glass-bg: rgba(15, 23, 42, 0.25);
  --glass-border: rgba(148, 163, 184, 0.18);
  --backdrop-blur: blur(16px);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  margin: 0;
  padding: 0;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Stunning Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--accent-primary);
  }
  50% {
    box-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-primary);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0,-1px,0);
  }
}

/* Main dispatch center layout */
.dispatch-center {
  min-height: 100vh;
  background: var(--bg-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Stunning Header Design */
.dispatch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 32px;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--shadow-lg);
  position: sticky;
  top: 0;
  z-index: 1000;
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* Stunning Navigation */
.main-navigation {
  display: flex;
  gap: 6px;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  padding: 6px;
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-md);
}

.nav-btn {
  background: transparent;
  border: none;
  color: var(--text-muted);
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.nav-btn:hover::before {
  left: 100%;
}

.nav-btn:hover {
  background: var(--glass-bg);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.nav-btn.active {
  background: var(--accent-gradient);
  color: white;
  box-shadow: var(--shadow-xl);
  transform: translateY(-1px);
}

.nav-btn.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: white;
  border-radius: 2px;
  animation: glow 2s infinite;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.system-title {
  font-size: 28px;
  font-weight: 800;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: var(--success);
  border-radius: 50%;
  animation: pulse 2s infinite;
  box-shadow: 0 0 8px var(--success);
}

.system-status {
  font-size: 16px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--success);
  box-shadow: 0 0 4px var(--success);
}

.status-indicator.operational {
  background: var(--success);
  box-shadow: 0 0 4px var(--success);
}
.status-indicator.warning {
  background: var(--warning);
  box-shadow: 0 0 4px var(--warning);
}
.status-indicator.critical {
  background: var(--error);
  box-shadow: 0 0 4px var(--error);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.time-display {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  font-variant-numeric: tabular-nums;
  transition: color 0.3s ease;
}

.date-display {
  font-size: 14px;
  color: var(--text-muted);
  margin-top: 2px;
  transition: color 0.3s ease;
}

/* Dark Mode Toggle */
.theme-toggle {
  position: relative;
  width: 60px;
  height: 30px;
  background: var(--border-secondary);
  border-radius: 15px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.theme-toggle:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 3px;
  left: 3px;
  width: 24px;
  height: 24px;
  background: var(--bg-primary);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .theme-toggle {
  background: var(--accent-primary);
}

[data-theme="dark"] .theme-toggle::before {
  transform: translateX(30px);
  background: var(--bg-primary);
}

.theme-toggle-icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.theme-toggle-icon.sun {
  left: 8px;
  opacity: 1;
  color: #f59e0b;
}

.theme-toggle-icon.moon {
  right: 8px;
  opacity: 0.5;
  color: #64748b;
}

[data-theme="dark"] .theme-toggle-icon.sun {
  opacity: 0.5;
  color: #64748b;
}

[data-theme="dark"] .theme-toggle-icon.moon {
  opacity: 1;
  color: #fbbf24;
}

/* Dashboard grid layout - Map-focused */
.dashboard-grid {
  display: grid;
  grid-template-columns: 3fr 1fr;
  grid-template-rows: 1fr;
  gap: 24px;
  padding: 24px;
  height: calc(100vh - 120px);
  max-width: 1600px;
  margin: 0 auto;
}

.main-map-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.analytics-sidebar {
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto;
}

/* Stunning Panel Design */
.panel {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 28px;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--accent-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.panel:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
}

.panel:hover::before {
  opacity: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-primary);
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 12px;
  transition: color 0.3s ease;
}

/* Emergency panel styling */
.emergency-panel {
  grid-row: span 2;
}

.emergency-count {
  background: #dc2626;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.emergency-list {
  max-height: calc(100vh - 280px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.emergency-list::-webkit-scrollbar {
  width: 6px;
}

.emergency-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.emergency-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.emergency-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.emergency-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-left: 4px solid;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.emergency-card:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.emergency-card.priority-critical {
  border-left-color: var(--error);
  background: var(--glass-bg);
}

.emergency-card.priority-high {
  border-left-color: #ea580c;
  background: var(--glass-bg);
}

.emergency-card.priority-medium {
  border-left-color: var(--warning);
  background: var(--glass-bg);
}

.emergency-card.priority-low {
  border-left-color: var(--success);
  background: var(--glass-bg);
}

.emergency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.emergency-id {
  font-weight: 600;
  color: var(--text-secondary);
  font-size: 14px;
  font-family: 'Inter', monospace;
  transition: color 0.3s ease;
}

.priority-badge {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.priority-badge.critical {
  background: var(--error);
  color: white;
}

.priority-badge.high {
  background: #ea580c;
  color: white;
}

.priority-badge.medium {
  background: var(--warning);
  color: white;
}

.priority-badge.low {
  background: var(--success);
  color: white;
}

.emergency-details {
  font-size: 14px;
  line-height: 1.5;
}

.emergency-type {
  font-weight: 700;
  color: var(--text-primary);
  text-transform: capitalize;
  margin-bottom: 8px;
  font-size: 16px;
  transition: color 0.3s ease;
}

.emergency-location,
.emergency-time,
.assigned-ambulance {
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-size: 14px;
  transition: color 0.3s ease;
}

.assigned-ambulance {
  color: var(--success);
  font-weight: 600;
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--success);
  padding: 6px 12px;
  border-radius: 8px;
  display: inline-block;
  margin-top: 4px;
  box-shadow: var(--shadow-sm);
}

/* Analytics panel styling */
.analytics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.metric-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.metric-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.metric-value {
  font-size: 32px;
  font-weight: 800;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-variant-numeric: tabular-nums;
  background: var(--accent-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.metric-label {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 8px;
  transition: color 0.3s ease;
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.metric-trend.high {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.metric-trend.normal {
  background: #f0fdf4;
  color: #059669;
  border: 1px solid #bbf7d0;
}

.metric-trend.low {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

/* Fleet panel styling */
.fleet-summary {
  display: flex;
  gap: 12px;
  font-size: 12px;
}

.available-count {
  background: #f0fdf4;
  color: #059669;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #bbf7d0;
  font-weight: 600;
}

.active-count {
  background: #fffbeb;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #fed7aa;
  font-weight: 600;
}

.ambulance-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
  max-height: calc(100vh - 320px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.ambulance-grid::-webkit-scrollbar {
  width: 6px;
}

.ambulance-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.ambulance-grid::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.ambulance-grid::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.ambulance-card {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: fadeIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-md);
}

.ambulance-card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-xl);
  transform: translateY(-2px);
}

.ambulance-card.selected {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.ambulance-card.available {
  border-left: 4px solid #10b981;
}

.ambulance-card.dispatched {
  border-left: 4px solid #f59e0b;
}

.ambulance-card.en-route {
  border-left: 4px solid #ef4444;
}

.ambulance-card.maintenance {
  border-left: 4px solid #6b7280;
  opacity: 0.8;
}

.ambulance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ambulance-id {
  font-weight: 700;
  color: var(--text-primary);
  font-size: 16px;
  font-family: 'Inter', monospace;
  transition: color 0.3s ease;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.ambulance-info {
  font-size: 13px;
  line-height: 1.5;
}

.crew-info {
  margin-bottom: 12px;
  color: var(--text-secondary);
  transition: color 0.3s ease;
}

.crew-info div {
  margin-bottom: 2px;
}

.vehicle-stats {
  margin-bottom: 12px;
}

.fuel-level {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 12px;
}

.fuel-bar {
  flex: 1;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  overflow: hidden;
}

.fuel-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.emergency-assignment {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
}

.assignment-type {
  color: #dc2626;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 4px;
  font-size: 13px;
}

.assignment-eta {
  color: #d97706;
  font-size: 12px;
  font-weight: 500;
}

/* Main map panel styling */
.main-map-panel {
  height: 100%;
  min-height: 600px;
}

.map-controls {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.map-btn {
  background: white;
  border: 1px solid #d1d5db;
  color: #374151;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.map-btn:hover {
  background: #f9fafb;
  border-color: #9ca3af;
}

.map-btn.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.emergency-map {
  width: 100%;
  height: calc(100% - 120px);
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
  background: #f8fafc;
  min-height: 500px;
}

/* Fleet status overlay */
.fleet-status-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.fleet-summary-overlay {
  display: flex;
  gap: 12px;
  font-size: 13px;
  font-weight: 600;
}

/* Stunning Map Legend */
.map-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px 0 0 0;
  border-top: 1px solid var(--border-primary);
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1px solid transparent;
}

.legend-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  color: var(--text-primary);
  box-shadow: var(--shadow-md);
}

.legend-icon {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  color: white;
  font-weight: bold;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.legend-item:hover .legend-icon {
  transform: scale(1.2);
  box-shadow: var(--shadow-lg);
}

/* Stunning AI Panel Styling */
.ai-panel {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 2px solid var(--accent-primary);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  animation: glow 3s infinite;
}

.ai-status {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.ai-status.active {
  background: var(--success);
  color: white;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.4);
  animation: pulse 2s infinite;
}

.ai-metrics {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.ai-metric {
  text-align: center;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.ai-metric .metric-value {
  font-size: 20px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.ai-metric .metric-label {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ai-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.feature-icon {
  font-size: 16px;
}

.feature-title {
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.feature-desc {
  font-size: 11px;
  color: #64748b;
}

/* Compact Emergency Panel */
.emergency-panel.compact {
  max-height: 400px;
}

.emergency-panel.compact .emergency-list {
  max-height: 300px;
}

.emergency-panel.compact .emergency-card {
  padding: 12px;
  margin-bottom: 8px;
}

/* AI Classification styling */
.ai-classification {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  padding: 8px;
  margin: 8px 0;
  font-size: 11px;
}

.ai-confidence, .risk-score, .data-source {
  margin-bottom: 2px;
}

.patient-info {
  background: #f8fafc;
  border-radius: 4px;
  padding: 6px;
  margin-top: 8px;
}

/* Advanced Features Section */
.advanced-features-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 24px;
}

/* Voice Interface Panel */
.voice-interface-panel {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2px solid #0ea5e9;
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
}

.status-indicator.listening {
  background: #ef4444;
  animation: pulse 1s infinite;
}

.status-indicator.ready {
  background: #10b981;
}

.voice-interface-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.voice-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.voice-stat {
  text-align: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.voice-stat .stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #0ea5e9;
  margin-bottom: 4px;
}

.voice-stat .stat-label {
  font-size: 12px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.voice-feature {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.voice-feature .feature-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.voice-feature .feature-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
}

.voice-feature .feature-content p {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: #64748b;
  line-height: 1.5;
}

.tech-badge, .accessibility-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
}

.tech-badge {
  background: #dbeafe;
  color: #1d4ed8;
}

.accessibility-badge {
  background: #dcfce7;
  color: #166534;
}

.language-pills {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.language-pill {
  padding: 2px 6px;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 12px;
  font-size: 10px;
  color: #475569;
}

.voice-demo {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 2px dashed #0ea5e9;
}

.voice-demo-btn {
  padding: 12px 24px;
  background: #0ea5e9;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.voice-demo-btn:hover {
  background: #0284c7;
  transform: translateY(-1px);
}

.voice-demo-text {
  font-size: 13px;
  color: #64748b;
  text-align: center;
  font-style: italic;
}

/* Heatmap Panel */
.heatmap-panel {
  background: linear-gradient(135deg, #fef7ed 0%, #fed7aa 100%);
  border: 2px solid #f97316;
}

.heatmap-accuracy {
  font-size: 12px;
  color: #64748b;
}

.accuracy-value {
  font-weight: 600;
  color: #f97316;
}

.heatmap-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.heatmap-overview {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.heatmap-description h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.heatmap-description p {
  margin: 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

.hotspots-grid h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.hotspots-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 12px;
}

.hotspot-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.hotspot-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.hotspot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.hotspot-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 14px;
}

.risk-level {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.risk-level.high {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.risk-level.medium {
  background: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

.risk-level.low {
  background: #f0fdf4;
  color: #059669;
  border: 1px solid #bbf7d0;
}

.hotspot-stats {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.hotspot-stat {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.hotspot-stat .stat-label {
  color: #64748b;
}

.hotspot-stat .stat-value {
  font-weight: 600;
  color: #1e293b;
}

.heatmap-features {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.heatmap-feature-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.heatmap-feature {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.heatmap-feature .feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.heatmap-feature h5 {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
}

.heatmap-feature p {
  margin: 0;
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

/* Architecture Panel */
.architecture-panel {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 2px solid #22c55e;
}

.architecture-status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  color: #059669;
}

.architecture-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.architecture-overview {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.architecture-description h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.architecture-description p {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
}

.architecture-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.arch-stat {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.arch-stat .stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #22c55e;
  margin-bottom: 4px;
}

.arch-stat .stat-label {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.microservices-grid h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.microservices-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 12px;
}

.microservice-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.microservice-card:hover {
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.service-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 13px;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.service-status.active {
  color: #059669;
}

.service-status .status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #059669;
}

.service-uptime {
  font-size: 12px;
  color: #64748b;
}

.uptime-value {
  font-weight: 600;
  color: #059669;
}

.architecture-features {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.arch-feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.arch-feature {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.arch-feature .feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.arch-feature h5 {
  margin: 0 0 4px 0;
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
}

.arch-feature p {
  margin: 0;
  font-size: 12px;
  color: #64748b;
  line-height: 1.4;
}

.integration-showcase {
  background: white;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.integration-showcase h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.integration-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.integration-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 500;
  color: #475569;
}

.integration-icon {
  font-size: 16px;
}

/* Responsive design */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 2fr 1fr;
  }

  .main-map-panel {
    min-height: 500px;
  }
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }

  .main-map-section {
    order: 1;
  }

  .analytics-sidebar {
    order: 2;
  }

  .panel {
    padding: 20px;
  }

  .dispatch-header {
    padding: 16px 20px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .header-left, .header-right, .header-center {
    align-items: center;
  }

  .main-navigation {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .nav-btn {
    flex: 1;
    min-width: 120px;
  }

  .system-title {
    font-size: 22px;
  }

  .ambulance-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .analytics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }

  .metric-card {
    padding: 16px;
  }

  .metric-value {
    font-size: 24px;
  }

  .emergency-map {
    min-height: 400px;
  }
}

@media (max-width: 768px) {
  .dispatch-header {
    padding: 12px 16px;
  }

  .system-title {
    font-size: 20px;
  }

  .time-display {
    font-size: 18px;
  }

  .dashboard-grid {
    padding: 12px 16px;
    gap: 12px;
  }

  .panel {
    padding: 16px;
  }

  .panel-header h3 {
    font-size: 16px;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .ambulance-grid {
    grid-template-columns: 1fr;
  }

  .emergency-card {
    padding: 14px;
  }

  .ambulance-card {
    padding: 14px;
  }

  .emergency-list,
  .ambulance-grid {
    max-height: 300px;
  }

  .emergency-map {
    min-height: 350px;
  }

  .map-controls {
    flex-wrap: wrap;
    gap: 6px;
  }

  .map-btn {
    padding: 6px 10px;
    font-size: 11px;
  }

  /* Advanced Features Responsive */
  .feature-grid {
    grid-template-columns: 1fr;
  }

  .voice-stats {
    grid-template-columns: 1fr;
  }

  .heatmap-feature-grid {
    grid-template-columns: 1fr;
  }

  .arch-feature-grid {
    grid-template-columns: 1fr;
  }

  .integration-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .hotspots-list {
    grid-template-columns: 1fr;
  }

  .microservices-list {
    grid-template-columns: 1fr;
  }

  .architecture-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .integration-grid {
    grid-template-columns: 1fr;
  }

  .voice-demo-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .advanced-features-section {
    gap: 16px;
    margin-top: 16px;
  }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-sm { font-size: 14px; }
.text-xs { font-size: 12px; }

.mb-2 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 16px; }

.p-2 { padding: 8px; }
.p-4 { padding: 16px; }

/* Status colors */
.text-green-600 { color: #059669; }
.text-red-600 { color: #dc2626; }
.text-yellow-600 { color: #d97706; }
.text-blue-600 { color: #2563eb; }
.text-gray-600 { color: #4b5563; }

.bg-green-50 { background-color: #f0fdf4; }
.bg-red-50 { background-color: #fef2f2; }
.bg-yellow-50 { background-color: #fffbeb; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-gray-50 { background-color: #f9fafb; }

/* Focus states for accessibility */
.map-btn:focus,
.ambulance-card:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .dispatch-header {
    position: static;
  }

  .panel {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }

  .emergency-map {
    height: 400px;
  }
}
