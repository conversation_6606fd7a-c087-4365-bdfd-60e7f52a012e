@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  background: linear-gradient(135deg, #0a0f1e 0%, #1a1f2e 50%, #0f1419 100%);
  color: #e0e6ed;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  min-height: 100vh;
}

/* Futuristic animations */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px #00e5ff; }
  50% { box-shadow: 0 0 20px #00e5ff, 0 0 30px #00e5ff; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Main dispatch center layout */
.dispatch-center {
  min-height: 100vh;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 229, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 68, 68, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0f1e 0%, #1a1f2e 100%);
  animation: fadeIn 1s ease-out;
}

/* Header styling */
.dispatch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: rgba(10, 15, 30, 0.9);
  border-bottom: 2px solid #00e5ff;
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.system-title {
  font-family: 'Orbitron', monospace;
  font-size: 28px;
  font-weight: 900;
  color: #00e5ff;
  text-shadow: 0 0 20px rgba(0, 229, 255, 0.5);
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 5px;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: #00ff88;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.system-status {
  font-size: 14px;
  color: #7f8fa6;
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00ff88;
  animation: pulse 2s infinite;
}

.status-indicator.operational { background: #00ff88; }
.status-indicator.warning { background: #ffaa00; }
.status-indicator.critical { background: #ff4444; }

.header-right {
  text-align: right;
}

.time-display {
  font-family: 'Orbitron', monospace;
  font-size: 24px;
  font-weight: 700;
  color: #00e5ff;
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.5);
}

.date-display {
  font-size: 14px;
  color: #7f8fa6;
  margin-top: 2px;
}

/* Dashboard grid layout */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr;
  grid-template-rows: auto auto;
  gap: 20px;
  padding: 20px 30px;
  height: calc(100vh - 120px);
}

.panel {
  background: rgba(20, 25, 40, 0.9);
  border: 1px solid rgba(0, 229, 255, 0.3);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  animation: fadeIn 0.8s ease-out;
}

.panel:hover {
  border-color: rgba(0, 229, 255, 0.6);
  box-shadow: 0 0 30px rgba(0, 229, 255, 0.2);
  transform: translateY(-2px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 229, 255, 0.2);
}

.panel-header h3 {
  font-family: 'Orbitron', monospace;
  font-size: 16px;
  font-weight: 700;
  color: #00e5ff;
  text-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
}

/* Emergency panel styling */
.emergency-panel {
  grid-row: span 2;
}

.emergency-count {
  background: #ff4444;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  animation: pulse 2s infinite;
}

.emergency-list {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #00e5ff rgba(20, 25, 40, 0.5);
}

.emergency-list::-webkit-scrollbar {
  width: 6px;
}

.emergency-list::-webkit-scrollbar-track {
  background: rgba(20, 25, 40, 0.5);
  border-radius: 3px;
}

.emergency-list::-webkit-scrollbar-thumb {
  background: #00e5ff;
  border-radius: 3px;
}

.emergency-card {
  background: rgba(30, 35, 50, 0.8);
  border-left: 4px solid;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  animation: slideIn 0.5s ease-out;
}

.emergency-card:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.emergency-card.priority-critical {
  border-left-color: #ff0044;
  background: rgba(255, 0, 68, 0.1);
}

.emergency-card.priority-high {
  border-left-color: #ff6600;
  background: rgba(255, 102, 0, 0.1);
}

.emergency-card.priority-medium {
  border-left-color: #ffaa00;
  background: rgba(255, 170, 0, 0.1);
}

.emergency-card.priority-low {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.1);
}

.emergency-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.emergency-id {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  color: #00e5ff;
  font-size: 14px;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 700;
  text-transform: uppercase;
}

.priority-badge.critical {
  background: #ff0044;
  color: white;
  animation: pulse 1.5s infinite;
}

.priority-badge.high {
  background: #ff6600;
  color: white;
}

.priority-badge.medium {
  background: #ffaa00;
  color: black;
}

.priority-badge.low {
  background: #00ff88;
  color: black;
}

.emergency-details {
  font-size: 13px;
  line-height: 1.6;
}

.emergency-type {
  font-weight: 600;
  color: #ffffff;
  text-transform: capitalize;
  margin-bottom: 5px;
}

.emergency-location,
.emergency-time,
.assigned-ambulance {
  color: #b0b8c1;
  margin-bottom: 3px;
}

.assigned-ambulance {
  color: #00ff88;
  font-weight: 500;
}

/* Analytics panel styling */
.analytics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
}

.metric-card {
  background: rgba(40, 45, 60, 0.6);
  border: 1px solid rgba(0, 229, 255, 0.2);
  border-radius: 10px;
  padding: 20px;
  text-align: center;
  transition: all 0.3s ease;
}

.metric-card:hover {
  border-color: rgba(0, 229, 255, 0.5);
  transform: scale(1.02);
}

.metric-value {
  font-family: 'Orbitron', monospace;
  font-size: 32px;
  font-weight: 900;
  color: #00e5ff;
  text-shadow: 0 0 15px rgba(0, 229, 255, 0.5);
  margin-bottom: 8px;
}

.metric-label {
  font-size: 14px;
  color: #7f8fa6;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
}

.metric-trend.high {
  background: rgba(255, 68, 68, 0.2);
  color: #ff4444;
  border: 1px solid #ff4444;
}

.metric-trend.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid #00ff88;
}

.metric-trend.low {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
  border: 1px solid #ffaa00;
}

/* Fleet panel styling */
.fleet-summary {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.available-count {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #00ff88;
}

.active-count {
  background: rgba(255, 170, 0, 0.2);
  color: #ffaa00;
  padding: 4px 8px;
  border-radius: 12px;
  border: 1px solid #ffaa00;
}

.ambulance-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #00e5ff rgba(20, 25, 40, 0.5);
}

.ambulance-grid::-webkit-scrollbar {
  width: 6px;
}

.ambulance-grid::-webkit-scrollbar-track {
  background: rgba(20, 25, 40, 0.5);
  border-radius: 3px;
}

.ambulance-grid::-webkit-scrollbar-thumb {
  background: #00e5ff;
  border-radius: 3px;
}

.ambulance-card {
  background: rgba(30, 35, 50, 0.8);
  border: 1px solid rgba(0, 229, 255, 0.2);
  border-radius: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  animation: fadeIn 0.6s ease-out;
}

.ambulance-card:hover {
  border-color: rgba(0, 229, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.ambulance-card.selected {
  border-color: #00e5ff;
  box-shadow: 0 0 20px rgba(0, 229, 255, 0.3);
}

.ambulance-card.available {
  border-left: 4px solid #00ff88;
}

.ambulance-card.dispatched {
  border-left: 4px solid #ffaa00;
}

.ambulance-card.en-route {
  border-left: 4px solid #ff4444;
}

.ambulance-card.maintenance {
  border-left: 4px solid #666666;
  opacity: 0.7;
}

.ambulance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.ambulance-id {
  font-family: 'Orbitron', monospace;
  font-weight: 700;
  color: #00e5ff;
  font-size: 14px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.ambulance-info {
  font-size: 12px;
  line-height: 1.5;
}

.crew-info {
  margin-bottom: 10px;
  color: #b0b8c1;
}

.vehicle-stats {
  margin-bottom: 10px;
}

.fuel-level {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #b0b8c1;
}

.fuel-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
}

.fuel-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.emergency-assignment {
  background: rgba(255, 68, 68, 0.1);
  border: 1px solid #ff4444;
  border-radius: 6px;
  padding: 8px;
  margin-top: 8px;
}

.assignment-type {
  color: #ff4444;
  font-weight: 600;
  text-transform: capitalize;
  margin-bottom: 2px;
}

.assignment-eta {
  color: #ffaa00;
  font-size: 11px;
}

/* Map panel styling */
.map-panel {
  grid-column: span 1;
  grid-row: span 2;
}

.map-controls {
  display: flex;
  gap: 10px;
}

.map-btn {
  background: rgba(0, 229, 255, 0.1);
  border: 1px solid rgba(0, 229, 255, 0.3);
  color: #00e5ff;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.map-btn:hover {
  background: rgba(0, 229, 255, 0.2);
  border-color: #00e5ff;
  box-shadow: 0 0 10px rgba(0, 229, 255, 0.3);
}

.emergency-map {
  width: 100%;
  height: calc(100% - 80px);
  border-radius: 10px;
  border: 1px solid rgba(0, 229, 255, 0.2);
  overflow: hidden;
  background: #0a0f1e;
}

/* Responsive design */
@media (max-width: 1400px) {
  .dashboard-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: auto auto auto;
  }

  .map-panel {
    grid-column: span 2;
    grid-row: span 1;
  }

  .emergency-panel {
    grid-row: span 1;
  }
}

@media (max-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    padding: 15px 20px;
  }

  .panel {
    padding: 15px;
  }

  .dispatch-header {
    padding: 15px 20px;
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .system-title {
    font-size: 24px;
  }

  .ambulance-grid {
    grid-template-columns: 1fr;
  }

  .analytics-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .metric-card {
    padding: 15px;
  }

  .metric-value {
    font-size: 24px;
  }
}

@media (max-width: 768px) {
  .dispatch-header {
    padding: 10px 15px;
  }

  .system-title {
    font-size: 20px;
  }

  .time-display {
    font-size: 18px;
  }

  .dashboard-grid {
    padding: 10px 15px;
    gap: 10px;
  }

  .panel {
    padding: 12px;
  }

  .panel-header h3 {
    font-size: 14px;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .emergency-card {
    padding: 12px;
  }

  .ambulance-card {
    padding: 12px;
  }
}

/* Additional futuristic effects */
.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00e5ff, transparent);
  opacity: 0.5;
}

.emergency-card::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, transparent, rgba(0, 229, 255, 0.5), transparent);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.emergency-card:hover::after {
  opacity: 1;
}

/* Scrollbar styling for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(20, 25, 40, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #00e5ff, #0099cc);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #00ffff, #00ccff);
}

/* Loading animations */
@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

.loading {
  background: linear-gradient(90deg, transparent, rgba(0, 229, 255, 0.1), transparent);
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}
