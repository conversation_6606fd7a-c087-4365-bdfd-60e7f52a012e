{"ast": null, "code": "// Traffic routes and navigation data for Chennai\nexport const trafficRoutes = [{\n  id: 1,\n  name: \"Anna Salai - Main Corridor\",\n  description: \"Primary business district route connecting Central to T Nagar\",\n  path: [{\n    lat: 13.0827,\n    lng: 80.2707,\n    name: \"Chennai Central\"\n  }, {\n    lat: 13.0732,\n    lng: 80.2609,\n    name: \"<PERSON><PERSON><PERSON>\"\n  }, {\n    lat: 13.0569,\n    lng: 80.2378,\n    name: \"Thousand Lights\"\n  }, {\n    lat: 13.0418,\n    lng: 80.2341,\n    name: \"T Nagar\"\n  }],\n  density: \"very_high\",\n  avgSpeed: 15,\n  congestionLevel: 85,\n  estimatedTime: \"25 mins\",\n  distance: \"8.2 km\",\n  color: \"#dc2626\",\n  peakHours: [\"8:00-10:00\", \"17:00-20:00\"],\n  alternativeRoutes: [2, 5]\n}, {\n  id: 2,\n  name: \"OMR Tech Corridor\",\n  description: \"IT corridor connecting city center to tech parks\",\n  path: [{\n    lat: 13.0732,\n    lng: 80.2609,\n    name: \"Egmore\"\n  }, {\n    lat: 13.0418,\n    lng: 80.2341,\n    name: \"T Nagar\"\n  }, {\n    lat: 12.9716,\n    lng: 80.2341,\n    name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  }, {\n    lat: 12.9141,\n    lng: 80.2270,\n    name: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\"\n  }],\n  density: \"high\",\n  avgSpeed: 35,\n  congestionLevel: 70,\n  estimatedTime: \"18 mins\",\n  distance: \"12.5 km\",\n  color: \"#f59e0b\",\n  peakHours: [\"8:30-10:30\", \"18:00-20:30\"],\n  alternativeRoutes: [3, 4]\n}, {\n  id: 3,\n  name: \"ECR Coastal Route\",\n  description: \"Scenic coastal highway with moderate traffic\",\n  path: [{\n    lat: 13.0500,\n    lng: 80.2824,\n    name: \"Marina Beach\"\n  }, {\n    lat: 12.9716,\n    lng: 80.2341,\n    name: \"Besant Nagar\"\n  }, {\n    lat: 12.8956,\n    lng: 80.2267,\n    name: \"Thiruvanmiyur\"\n  }, {\n    lat: 12.8200,\n    lng: 80.2267,\n    name: \"Mahabalipuram Road\"\n  }],\n  density: \"medium\",\n  avgSpeed: 45,\n  congestionLevel: 45,\n  estimatedTime: \"22 mins\",\n  distance: \"15.8 km\",\n  color: \"#10b981\",\n  peakHours: [\"9:00-11:00\", \"16:00-18:00\"],\n  alternativeRoutes: [2, 4]\n}, {\n  id: 4,\n  name: \"GST Road Industrial\",\n  description: \"Industrial corridor connecting airport to city\",\n  path: [{\n    lat: 13.1986,\n    lng: 80.1811,\n    name: \"Chennai Airport\"\n  }, {\n    lat: 13.1200,\n    lng: 80.2000,\n    name: \"Meenambakkam\"\n  }, {\n    lat: 13.0067,\n    lng: 80.2206,\n    name: \"Guindy\"\n  }, {\n    lat: 12.9141,\n    lng: 80.2270,\n    name: \"Chrompet\"\n  }],\n  density: \"medium\",\n  avgSpeed: 40,\n  congestionLevel: 55,\n  estimatedTime: \"20 mins\",\n  distance: \"18.3 km\",\n  color: \"#3b82f6\",\n  peakHours: [\"7:00-9:00\", \"17:30-19:30\"],\n  alternativeRoutes: [1, 5]\n}, {\n  id: 5,\n  name: \"Inner Ring Road\",\n  description: \"Circular route connecting major city areas\",\n  path: [{\n    lat: 13.0827,\n    lng: 80.2707,\n    name: \"Chennai Central\"\n  }, {\n    lat: 13.0850,\n    lng: 80.2101,\n    name: \"Anna Nagar\"\n  }, {\n    lat: 13.0732,\n    lng: 80.2609,\n    name: \"Egmore\"\n  }, {\n    lat: 13.0569,\n    lng: 80.2378,\n    name: \"Thousand Lights\"\n  }, {\n    lat: 13.0418,\n    lng: 80.2341,\n    name: \"T Nagar\"\n  }],\n  density: \"high\",\n  avgSpeed: 25,\n  congestionLevel: 75,\n  estimatedTime: \"30 mins\",\n  distance: \"14.2 km\",\n  color: \"#f59e0b\",\n  peakHours: [\"8:00-10:30\", \"17:00-20:00\"],\n  alternativeRoutes: [1, 2]\n}, {\n  id: 6,\n  name: \"Outer Ring Road\",\n  description: \"Bypass route avoiding city center congestion\",\n  path: [{\n    lat: 13.1986,\n    lng: 80.1811,\n    name: \"Airport\"\n  }, {\n    lat: 13.1500,\n    lng: 80.2200,\n    name: \"Porur\"\n  }, {\n    lat: 13.1200,\n    lng: 80.2800,\n    name: \"Madhavaram\"\n  }, {\n    lat: 13.0850,\n    lng: 80.2101,\n    name: \"Anna Nagar\"\n  }],\n  density: \"low\",\n  avgSpeed: 55,\n  congestionLevel: 30,\n  estimatedTime: \"15 mins\",\n  distance: \"22.1 km\",\n  color: \"#059669\",\n  peakHours: [\"7:30-9:30\", \"18:00-19:30\"],\n  alternativeRoutes: [4, 5]\n}];\n\n// Traffic density zones\nexport const trafficZones = [{\n  id: 1,\n  name: \"Central Business District\",\n  bounds: {\n    north: 13.0900,\n    south: 13.0300,\n    east: 80.2800,\n    west: 80.2200\n  },\n  density: \"very_high\",\n  avgSpeed: 12,\n  congestionLevel: 90,\n  color: \"#dc2626\"\n}, {\n  id: 2,\n  name: \"T Nagar Commercial Zone\",\n  bounds: {\n    north: 13.0500,\n    south: 13.0300,\n    east: 80.2500,\n    west: 80.2200\n  },\n  density: \"very_high\",\n  avgSpeed: 10,\n  congestionLevel: 95,\n  color: \"#dc2626\"\n}, {\n  id: 3,\n  name: \"OMR IT Corridor\",\n  bounds: {\n    north: 13.0000,\n    south: 12.8500,\n    east: 80.2500,\n    west: 80.2000\n  },\n  density: \"high\",\n  avgSpeed: 30,\n  congestionLevel: 70,\n  color: \"#f59e0b\"\n}];", "map": {"version": 3, "names": ["trafficRoutes", "id", "name", "description", "path", "lat", "lng", "density", "avgSpeed", "congestionLevel", "estimatedTime", "distance", "color", "peakHours", "alternativeRoutes", "trafficZones", "bounds", "north", "south", "east", "west"], "sources": ["D:/EMBEDDED/Project/traffic/src/routes.js"], "sourcesContent": ["// Traffic routes and navigation data for Chennai\r\nexport const trafficRoutes = [\r\n  {\r\n    id: 1,\r\n    name: \"Anna Salai - Main Corridor\",\r\n    description: \"Primary business district route connecting Central to T Nagar\",\r\n    path: [\r\n      { lat: 13.0827, lng: 80.2707, name: \"Chennai Central\" },\r\n      { lat: 13.0732, lng: 80.2609, name: \"<PERSON><PERSON><PERSON>\" },\r\n      { lat: 13.0569, lng: 80.2378, name: \"Thousand Lights\" },\r\n      { lat: 13.0418, lng: 80.2341, name: \"T Nagar\" }\r\n    ],\r\n    density: \"very_high\",\r\n    avgSpeed: 15,\r\n    congestionLevel: 85,\r\n    estimatedTime: \"25 mins\",\r\n    distance: \"8.2 km\",\r\n    color: \"#dc2626\",\r\n    peakHours: [\"8:00-10:00\", \"17:00-20:00\"],\r\n    alternativeRoutes: [2, 5]\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"OMR Tech Corridor\",\r\n    description: \"IT corridor connecting city center to tech parks\",\r\n    path: [\r\n      { lat: 13.0732, lng: 80.2609, name: \"Egmore\" },\r\n      { lat: 13.0418, lng: 80.2341, name: \"T Nagar\" },\r\n      { lat: 12.9716, lng: 80.2341, name: \"<PERSON><PERSON><PERSON><PERSON><PERSON>\" },\r\n      { lat: 12.9141, lng: 80.2270, name: \"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\" }\r\n    ],\r\n    density: \"high\",\r\n    avgSpeed: 35,\r\n    congestionLevel: 70,\r\n    estimatedTime: \"18 mins\",\r\n    distance: \"12.5 km\",\r\n    color: \"#f59e0b\",\r\n    peakHours: [\"8:30-10:30\", \"18:00-20:30\"],\r\n    alternativeRoutes: [3, 4]\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"ECR Coastal Route\",\r\n    description: \"Scenic coastal highway with moderate traffic\",\r\n    path: [\r\n      { lat: 13.0500, lng: 80.2824, name: \"Marina Beach\" },\r\n      { lat: 12.9716, lng: 80.2341, name: \"Besant Nagar\" },\r\n      { lat: 12.8956, lng: 80.2267, name: \"Thiruvanmiyur\" },\r\n      { lat: 12.8200, lng: 80.2267, name: \"Mahabalipuram Road\" }\r\n    ],\r\n    density: \"medium\",\r\n    avgSpeed: 45,\r\n    congestionLevel: 45,\r\n    estimatedTime: \"22 mins\",\r\n    distance: \"15.8 km\",\r\n    color: \"#10b981\",\r\n    peakHours: [\"9:00-11:00\", \"16:00-18:00\"],\r\n    alternativeRoutes: [2, 4]\r\n  },\r\n  {\r\n    id: 4,\r\n    name: \"GST Road Industrial\",\r\n    description: \"Industrial corridor connecting airport to city\",\r\n    path: [\r\n      { lat: 13.1986, lng: 80.1811, name: \"Chennai Airport\" },\r\n      { lat: 13.1200, lng: 80.2000, name: \"Meenambakkam\" },\r\n      { lat: 13.0067, lng: 80.2206, name: \"Guindy\" },\r\n      { lat: 12.9141, lng: 80.2270, name: \"Chrompet\" }\r\n    ],\r\n    density: \"medium\",\r\n    avgSpeed: 40,\r\n    congestionLevel: 55,\r\n    estimatedTime: \"20 mins\",\r\n    distance: \"18.3 km\",\r\n    color: \"#3b82f6\",\r\n    peakHours: [\"7:00-9:00\", \"17:30-19:30\"],\r\n    alternativeRoutes: [1, 5]\r\n  },\r\n  {\r\n    id: 5,\r\n    name: \"Inner Ring Road\",\r\n    description: \"Circular route connecting major city areas\",\r\n    path: [\r\n      { lat: 13.0827, lng: 80.2707, name: \"Chennai Central\" },\r\n      { lat: 13.0850, lng: 80.2101, name: \"Anna Nagar\" },\r\n      { lat: 13.0732, lng: 80.2609, name: \"Egmore\" },\r\n      { lat: 13.0569, lng: 80.2378, name: \"Thousand Lights\" },\r\n      { lat: 13.0418, lng: 80.2341, name: \"T Nagar\" }\r\n    ],\r\n    density: \"high\",\r\n    avgSpeed: 25,\r\n    congestionLevel: 75,\r\n    estimatedTime: \"30 mins\",\r\n    distance: \"14.2 km\",\r\n    color: \"#f59e0b\",\r\n    peakHours: [\"8:00-10:30\", \"17:00-20:00\"],\r\n    alternativeRoutes: [1, 2]\r\n  },\r\n  {\r\n    id: 6,\r\n    name: \"Outer Ring Road\",\r\n    description: \"Bypass route avoiding city center congestion\",\r\n    path: [\r\n      { lat: 13.1986, lng: 80.1811, name: \"Airport\" },\r\n      { lat: 13.1500, lng: 80.2200, name: \"Porur\" },\r\n      { lat: 13.1200, lng: 80.2800, name: \"Madhavaram\" },\r\n      { lat: 13.0850, lng: 80.2101, name: \"Anna Nagar\" }\r\n    ],\r\n    density: \"low\",\r\n    avgSpeed: 55,\r\n    congestionLevel: 30,\r\n    estimatedTime: \"15 mins\",\r\n    distance: \"22.1 km\",\r\n    color: \"#059669\",\r\n    peakHours: [\"7:30-9:30\", \"18:00-19:30\"],\r\n    alternativeRoutes: [4, 5]\r\n  }\r\n];\r\n\r\n// Traffic density zones\r\nexport const trafficZones = [\r\n  {\r\n    id: 1,\r\n    name: \"Central Business District\",\r\n    bounds: {\r\n      north: 13.0900,\r\n      south: 13.0300,\r\n      east: 80.2800,\r\n      west: 80.2200\r\n    },\r\n    density: \"very_high\",\r\n    avgSpeed: 12,\r\n    congestionLevel: 90,\r\n    color: \"#dc2626\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"T Nagar Commercial Zone\",\r\n    bounds: {\r\n      north: 13.0500,\r\n      south: 13.0300,\r\n      east: 80.2500,\r\n      west: 80.2200\r\n    },\r\n    density: \"very_high\",\r\n    avgSpeed: 10,\r\n    congestionLevel: 95,\r\n    color: \"#dc2626\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"OMR IT Corridor\",\r\n    bounds: {\r\n      north: 13.0000,\r\n      south: 12.8500,\r\n      east: 80.2500,\r\n      west: 80.2000\r\n    },\r\n    density: \"high\",\r\n    avgSpeed: 30,\r\n    congestionLevel: 70,\r\n    color: \"#f59e0b\"\r\n  }\r\n];\r\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,aAAa,GAAG,CAC3B;EACEC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,4BAA4B;EAClCC,WAAW,EAAE,+DAA+D;EAC5EC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAkB,CAAC,EACvD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAS,CAAC,EAC9C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAkB,CAAC,EACvD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAU,CAAC,CAChD;EACDK,OAAO,EAAE,WAAW;EACpBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,QAAQ;EAClBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;EACxCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,kDAAkD;EAC/DC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAS,CAAC,EAC9C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAU,CAAC,EAC/C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAe,CAAC,EACpD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAiB,CAAC,CACvD;EACDK,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;EACxCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,mBAAmB;EACzBC,WAAW,EAAE,8CAA8C;EAC3DC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAe,CAAC,EACpD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAe,CAAC,EACpD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAgB,CAAC,EACrD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAqB,CAAC,CAC3D;EACDK,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;EACxCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,qBAAqB;EAC3BC,WAAW,EAAE,gDAAgD;EAC7DC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAkB,CAAC,EACvD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAe,CAAC,EACpD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAS,CAAC,EAC9C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAW,CAAC,CACjD;EACDK,OAAO,EAAE,QAAQ;EACjBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACvCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,4CAA4C;EACzDC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAkB,CAAC,EACvD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAa,CAAC,EAClD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAS,CAAC,EAC9C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAkB,CAAC,EACvD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAU,CAAC,CAChD;EACDK,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;EACxCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,EACD;EACEb,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBC,WAAW,EAAE,8CAA8C;EAC3DC,IAAI,EAAE,CACJ;IAAEC,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAU,CAAC,EAC/C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAQ,CAAC,EAC7C;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAa,CAAC,EAClD;IAAEG,GAAG,EAAE,OAAO;IAAEC,GAAG,EAAE,OAAO;IAAEJ,IAAI,EAAE;EAAa,CAAC,CACnD;EACDK,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;EACvCC,iBAAiB,EAAE,CAAC,CAAC,EAAE,CAAC;AAC1B,CAAC,CACF;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG,CAC1B;EACEd,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,2BAA2B;EACjCc,MAAM,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;EACR,CAAC;EACDb,OAAO,EAAE,WAAW;EACpBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBG,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,yBAAyB;EAC/Bc,MAAM,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;EACR,CAAC;EACDb,OAAO,EAAE,WAAW;EACpBC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBG,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE,iBAAiB;EACvBc,MAAM,EAAE;IACNC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,OAAO;IACbC,IAAI,EAAE;EACR,CAAC;EACDb,OAAO,EAAE,MAAM;EACfC,QAAQ,EAAE,EAAE;EACZC,eAAe,EAAE,EAAE;EACnBG,KAAK,EAAE;AACT,CAAC,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}