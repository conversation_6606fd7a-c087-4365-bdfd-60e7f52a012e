// Traffic routes and navigation data for Chennai
export const trafficRoutes = [
  {
    id: 1,
    name: "Anna Salai - Main Corridor",
    description: "Primary business district route connecting Central to T Nagar",
    path: [
      { lat: 13.0827, lng: 80.2707, name: "Chennai Central" },
      { lat: 13.0732, lng: 80.2609, name: "<PERSON><PERSON><PERSON>" },
      { lat: 13.0569, lng: 80.2378, name: "Thousand Lights" },
      { lat: 13.0418, lng: 80.2341, name: "T Nagar" }
    ],
    density: "very_high",
    avgSpeed: 15,
    congestionLevel: 85,
    estimatedTime: "25 mins",
    distance: "8.2 km",
    color: "#dc2626",
    peakHours: ["8:00-10:00", "17:00-20:00"],
    alternativeRoutes: [2, 5]
  },
  {
    id: 2,
    name: "OMR Tech Corridor",
    description: "IT corridor connecting city center to tech parks",
    path: [
      { lat: 13.0732, lng: 80.2609, name: "Egmore" },
      { lat: 13.0418, lng: 80.2341, name: "T Nagar" },
      { lat: 12.9716, lng: 80.2341, name: "<PERSON><PERSON><PERSON><PERSON><PERSON>" },
      { lat: 12.9141, lng: 80.2270, name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" }
    ],
    density: "high",
    avgSpeed: 35,
    congestionLevel: 70,
    estimatedTime: "18 mins",
    distance: "12.5 km",
    color: "#f59e0b",
    peakHours: ["8:30-10:30", "18:00-20:30"],
    alternativeRoutes: [3, 4]
  },
  {
    id: 3,
    name: "ECR Coastal Route",
    description: "Scenic coastal highway with moderate traffic",
    path: [
      { lat: 13.0500, lng: 80.2824, name: "Marina Beach" },
      { lat: 12.9716, lng: 80.2341, name: "Besant Nagar" },
      { lat: 12.8956, lng: 80.2267, name: "Thiruvanmiyur" },
      { lat: 12.8200, lng: 80.2267, name: "Mahabalipuram Road" }
    ],
    density: "medium",
    avgSpeed: 45,
    congestionLevel: 45,
    estimatedTime: "22 mins",
    distance: "15.8 km",
    color: "#10b981",
    peakHours: ["9:00-11:00", "16:00-18:00"],
    alternativeRoutes: [2, 4]
  },
  {
    id: 4,
    name: "GST Road Industrial",
    description: "Industrial corridor connecting airport to city",
    path: [
      { lat: 13.1986, lng: 80.1811, name: "Chennai Airport" },
      { lat: 13.1200, lng: 80.2000, name: "Meenambakkam" },
      { lat: 13.0067, lng: 80.2206, name: "Guindy" },
      { lat: 12.9141, lng: 80.2270, name: "Chrompet" }
    ],
    density: "medium",
    avgSpeed: 40,
    congestionLevel: 55,
    estimatedTime: "20 mins",
    distance: "18.3 km",
    color: "#3b82f6",
    peakHours: ["7:00-9:00", "17:30-19:30"],
    alternativeRoutes: [1, 5]
  },
  {
    id: 5,
    name: "Inner Ring Road",
    description: "Circular route connecting major city areas",
    path: [
      { lat: 13.0827, lng: 80.2707, name: "Chennai Central" },
      { lat: 13.0850, lng: 80.2101, name: "Anna Nagar" },
      { lat: 13.0732, lng: 80.2609, name: "Egmore" },
      { lat: 13.0569, lng: 80.2378, name: "Thousand Lights" },
      { lat: 13.0418, lng: 80.2341, name: "T Nagar" }
    ],
    density: "high",
    avgSpeed: 25,
    congestionLevel: 75,
    estimatedTime: "30 mins",
    distance: "14.2 km",
    color: "#f59e0b",
    peakHours: ["8:00-10:30", "17:00-20:00"],
    alternativeRoutes: [1, 2]
  },
  {
    id: 6,
    name: "Outer Ring Road",
    description: "Bypass route avoiding city center congestion",
    path: [
      { lat: 13.1986, lng: 80.1811, name: "Airport" },
      { lat: 13.1500, lng: 80.2200, name: "Porur" },
      { lat: 13.1200, lng: 80.2800, name: "Madhavaram" },
      { lat: 13.0850, lng: 80.2101, name: "Anna Nagar" }
    ],
    density: "low",
    avgSpeed: 55,
    congestionLevel: 30,
    estimatedTime: "15 mins",
    distance: "22.1 km",
    color: "#059669",
    peakHours: ["7:30-9:30", "18:00-19:30"],
    alternativeRoutes: [4, 5]
  }
];

// Traffic density zones
export const trafficZones = [
  {
    id: 1,
    name: "Central Business District",
    bounds: {
      north: 13.0900,
      south: 13.0300,
      east: 80.2800,
      west: 80.2200
    },
    density: "very_high",
    avgSpeed: 12,
    congestionLevel: 90,
    color: "#dc2626"
  },
  {
    id: 2,
    name: "T Nagar Commercial Zone",
    bounds: {
      north: 13.0500,
      south: 13.0300,
      east: 80.2500,
      west: 80.2200
    },
    density: "very_high",
    avgSpeed: 10,
    congestionLevel: 95,
    color: "#dc2626"
  },
  {
    id: 3,
    name: "OMR IT Corridor",
    bounds: {
      north: 13.0000,
      south: 12.8500,
      east: 80.2500,
      west: 80.2000
    },
    density: "high",
    avgSpeed: 30,
    congestionLevel: 70,
    color: "#f59e0b"
  }
];
