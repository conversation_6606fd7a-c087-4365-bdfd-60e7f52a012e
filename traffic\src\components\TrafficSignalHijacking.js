import React, { useState, useEffect, useRef } from 'react';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import './TrafficSignalHijacking.css';

const TrafficSignalHijacking = () => {
  const mapRef = useRef(null);
  const leafletMapRef = useRef(null);
  const [isSimulationRunning, setIsSimulationRunning] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [emergencyLog, setEmergencyLog] = useState([
    { time: new Date(), message: 'AI Traffic Control System initialized', type: 'info' }
  ]);

  // Chennai road network with real coordinates
  const roadNetwork = {
    intersections: [
      { id: 'INT001', name: 'Anna Nagar Junction', lat: 13.0850, lng: 80.2101, state: 'red', timer: 30, hijacked: false },
      { id: 'INT002', name: 'T Nagar Signal', lat: 13.0418, lng: 80.2341, state: 'green', timer: 25, hijacked: false },
      { id: 'INT003', name: 'Vela<PERSON><PERSON> Cross', lat: 12.9756, lng: 80.2207, state: 'yellow', timer: 5, hijacked: false },
      { id: 'INT004', name: 'Adyar Signal', lat: 13.0067, lng: 80.2206, state: 'green', timer: 20, hijacked: false },
      { id: 'INT005', name: 'Guindy Junction', lat: 13.0103, lng: 80.2209, state: 'red', timer: 35, hijacked: false },
      { id: 'INT006', name: 'Mylapore Signal', lat: 13.0339, lng: 80.2619, state: 'green', timer: 15, hijacked: false },
    ],
    roads: [
      { from: 'INT001', to: 'INT002', distance: 5.2, congestion: 0.3 },
      { from: 'INT002', to: 'INT003', distance: 8.1, congestion: 0.7 },
      { from: 'INT003', to: 'INT004', distance: 3.4, congestion: 0.2 },
      { from: 'INT004', to: 'INT005', distance: 2.1, congestion: 0.5 },
      { from: 'INT005', to: 'INT006', distance: 4.8, congestion: 0.4 },
      { from: 'INT001', to: 'INT006', distance: 6.7, congestion: 0.6 },
    ]
  };

  // Traffic light states using FSM
  const [trafficLights, setTrafficLights] = useState(roadNetwork.intersections);

  // Ambulance data with FSM states
  const [ambulances, setAmbulances] = useState([
    {
      id: 'AMB001',
      currentPosition: { lat: 13.0900, lng: 80.2050 },
      destination: { lat: 13.0067, lng: 80.2206, name: 'Apollo Hospital' },
      speed: 60, // km/h
      active: false,
      state: 'IDLE', // FSM states: IDLE, DISPATCHED, EN_ROUTE, WAITING_AT_SIGNAL, ARRIVED
      route: ['INT001', 'INT002', 'INT004'], // Intersection IDs
      currentRouteIndex: 0,
      nextIntersection: null,
      estimatedArrival: null,
      priority: 'CRITICAL'
    },
    {
      id: 'AMB002',
      currentPosition: { lat: 13.0400, lng: 80.2300 },
      destination: { lat: 12.9756, lng: 80.2207, name: 'MIOT Hospital' },
      speed: 55, // km/h
      active: false,
      state: 'IDLE',
      route: ['INT002', 'INT003'],
      currentRouteIndex: 0,
      nextIntersection: null,
      estimatedArrival: null,
      priority: 'HIGH'
    }
  ]);



  // System metrics
  const [systemMetrics, setSystemMetrics] = useState({
    totalLights: 8,
    hijackedLights: 0,
    activeCorridors: 0,
    averageDelay: 0,
    swarmEfficiency: 95.2,
    energySavings: 23.7,
    trafficFlow: 87.3
  });

  // AI Control System with FSM and DRL
  const [aiControlSystem, setAiControlSystem] = useState({
    mode: 'RULE_BASED_FSM', // RULE_BASED_FSM, DEEP_RL, HYBRID
    activeAgents: 24,
    processingTime: 0.3,
    decisionAccuracy: 98.7,
    networkLatency: 12,
    coordinationScore: 94.8,
    learningRate: 0.001,
    episodeCount: 1247,
    rewardScore: 0.89
  });

  // FSM States for Traffic Control
  const trafficControlFSM = {
    states: {
      NORMAL_OPERATION: 'normal',
      EMERGENCY_DETECTED: 'emergency_detected',
      CORRIDOR_PLANNING: 'planning',
      SIGNAL_HIJACKING: 'hijacking',
      CORRIDOR_ACTIVE: 'active',
      CORRIDOR_CLEARING: 'clearing'
    },
    currentState: 'NORMAL_OPERATION'
  };

  const [fsmState, setFsmState] = useState(trafficControlFSM.currentState);

  // Real-time clock
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Initialize Leaflet map
  useEffect(() => {
    if (mapRef.current && !leafletMapRef.current) {
      // Initialize map centered on Chennai
      leafletMapRef.current = L.map(mapRef.current).setView([13.0827, 80.2707], 12);

      // Add OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(leafletMapRef.current);

      // Add intersection markers
      trafficLights.forEach(intersection => {
        const color = getTrafficLightColor(intersection.state, intersection.hijacked);
        const marker = L.circleMarker([intersection.lat, intersection.lng], {
          radius: 12,
          fillColor: color,
          color: intersection.hijacked ? '#3b82f6' : '#000000',
          weight: intersection.hijacked ? 3 : 1,
          opacity: 1,
          fillOpacity: 0.8
        }).addTo(leafletMapRef.current);

        marker.bindPopup(`
          <div style="font-family: Inter, sans-serif;">
            <h4 style="margin: 0 0 8px 0;">${intersection.name}</h4>
            <p style="margin: 0; color: ${color}; font-weight: 600;">
              Status: ${intersection.state.toUpperCase()}
            </p>
            <p style="margin: 4px 0 0 0; font-size: 12px;">
              Timer: ${intersection.timer}s
            </p>
            ${intersection.hijacked ?
              '<p style="margin: 4px 0 0 0; color: #3b82f6; font-weight: 600; font-size: 12px;">🤖 AI CONTROLLED</p>' :
              ''
            }
          </div>
        `);
      });

      // Add road network
      roadNetwork.roads.forEach(road => {
        const fromIntersection = trafficLights.find(i => i.id === road.from);
        const toIntersection = trafficLights.find(i => i.id === road.to);

        if (fromIntersection && toIntersection) {
          const congestionColor = road.congestion > 0.6 ? '#ef4444' :
                                 road.congestion > 0.3 ? '#f59e0b' : '#10b981';

          L.polyline([
            [fromIntersection.lat, fromIntersection.lng],
            [toIntersection.lat, toIntersection.lng]
          ], {
            color: congestionColor,
            weight: 4,
            opacity: 0.7
          }).addTo(leafletMapRef.current);
        }
      });
    }

    return () => {
      if (leafletMapRef.current) {
        leafletMapRef.current.remove();
        leafletMapRef.current = null;
      }
    };
  }, []);

  // Helper function to get traffic light color
  const getTrafficLightColor = (state, hijacked) => {
    if (hijacked) {
      return state === 'green' ? '#10b981' : state === 'yellow' ? '#f59e0b' : '#ef4444';
    }
    return state === 'green' ? '#22c55e' : state === 'yellow' ? '#eab308' : '#dc2626';
  };

  // AI Control Algorithms
  const ruleBasedFSM = (ambulanceRoute, currentTrafficState) => {
    // Simple rule-based finite state machine
    const decisions = [];

    ambulanceRoute.forEach(intersectionId => {
      const intersection = trafficLights.find(light => light.id === intersectionId);
      if (intersection && !intersection.hijacked) {
        decisions.push({
          intersectionId,
          action: 'HIJACK',
          newState: 'green',
          priority: 1,
          reason: 'Emergency vehicle approaching'
        });
      }
    });

    return decisions;
  };

  const deepRLAgent = (state, action, reward) => {
    // Simulated Deep Reinforcement Learning agent
    // In real implementation, this would use TensorFlow.js or similar

    const stateVector = [
      ...trafficLights.map(light => light.state === 'green' ? 1 : 0),
      ...ambulances.map(amb => amb.active ? 1 : 0),
      systemMetrics.trafficFlow / 100,
      systemMetrics.averageDelay / 60
    ];

    // Simulate Q-learning decision
    const qValues = {
      'HIJACK_SIGNAL': 0.8 + Math.random() * 0.2,
      'MAINTAIN_NORMAL': 0.3 + Math.random() * 0.2,
      'COORDINATE_TIMING': 0.6 + Math.random() * 0.2
    };

    const bestAction = Object.keys(qValues).reduce((a, b) =>
      qValues[a] > qValues[b] ? a : b
    );

    return {
      action: bestAction,
      confidence: qValues[bestAction],
      stateValue: stateVector.reduce((sum, val) => sum + val, 0) / stateVector.length
    };
  };

  const hybridAIControl = (ambulanceData, trafficData) => {
    // Combine rule-based FSM with DRL for optimal decisions
    const fsmDecisions = ruleBasedFSM(ambulanceData.route, trafficData);
    const rlDecision = deepRLAgent(trafficData, 'EVALUATE', 0);

    // Weight decisions based on confidence and situation complexity
    const complexity = ambulances.filter(amb => amb.active).length;
    const fsmWeight = complexity <= 1 ? 0.8 : 0.4;
    const rlWeight = 1 - fsmWeight;

    return {
      decisions: fsmDecisions,
      rlGuidance: rlDecision,
      weights: { fsm: fsmWeight, rl: rlWeight },
      finalAction: rlDecision.confidence > 0.7 ? rlDecision.action : 'RULE_BASED'
    };
  };

  // Ambulance movement along road network
  const moveAmbulanceAlongRoute = (ambulance) => {
    if (!ambulance.active || ambulance.state === 'ARRIVED') return ambulance;

    const currentIntersection = trafficLights.find(light =>
      light.id === ambulance.route[ambulance.currentRouteIndex]
    );

    if (!currentIntersection) return ambulance;

    // Calculate distance to next intersection
    const distance = calculateDistance(
      ambulance.currentPosition.lat,
      ambulance.currentPosition.lng,
      currentIntersection.lat,
      currentIntersection.lng
    );

    // Check if ambulance has reached the intersection
    if (distance < 0.001) { // ~100 meters
      // Check traffic light state
      if (currentIntersection.state === 'red' && !currentIntersection.hijacked) {
        return { ...ambulance, state: 'WAITING_AT_SIGNAL' };
      }

      // Move to next intersection
      const nextIndex = ambulance.currentRouteIndex + 1;
      if (nextIndex >= ambulance.route.length) {
        return {
          ...ambulance,
          state: 'ARRIVED',
          currentPosition: ambulance.destination
        };
      }

      return {
        ...ambulance,
        currentRouteIndex: nextIndex,
        state: 'EN_ROUTE'
      };
    }

    // Move towards current target intersection
    const speed = ambulance.speed / 3600; // Convert km/h to degrees per second (approximate)
    const bearing = calculateBearing(
      ambulance.currentPosition.lat,
      ambulance.currentPosition.lng,
      currentIntersection.lat,
      currentIntersection.lng
    );

    const newPosition = moveTowards(
      ambulance.currentPosition,
      currentIntersection,
      speed * 0.1 // Simulation time step
    );

    return {
      ...ambulance,
      currentPosition: newPosition,
      state: 'EN_ROUTE'
    };
  };

  // Helper functions for geographic calculations
  const calculateDistance = (lat1, lng1, lat2, lng2) => {
    const R = 6371; // Earth's radius in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const calculateBearing = (lat1, lng1, lat2, lng2) => {
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    const y = Math.sin(dLng) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) -
              Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLng);
    return Math.atan2(y, x);
  };

  const moveTowards = (current, target, speed) => {
    const distance = calculateDistance(current.lat, current.lng, target.lat, target.lng);
    if (distance <= speed) return target;

    const ratio = speed / distance;
    return {
      lat: current.lat + (target.lat - current.lat) * ratio,
      lng: current.lng + (target.lng - current.lng) * ratio
    };
  };

  // Start emergency simulation
  const startSimulation = () => {
    setIsSimulationRunning(true);
    setFsmState('EMERGENCY_DETECTED');

    // Add log entry
    setEmergencyLog(prev => [...prev,
      { time: new Date(), message: 'Emergency detected - Activating AI control system', type: 'emergency' }
    ]);

    // Activate first ambulance
    setAmbulances(prev => prev.map(amb =>
      amb.id === 'AMB001' ? {
        ...amb,
        active: true,
        state: 'DISPATCHED',
        currentRouteIndex: 0
      } : amb
    ));

    // Start AI decision making process
    setTimeout(() => {
      setFsmState('CORRIDOR_PLANNING');

      const activeAmb = ambulances.find(amb => amb.id === 'AMB001');
      if (activeAmb) {
        const aiDecision = hybridAIControl(activeAmb, trafficLights);

        // Apply AI decisions to hijack traffic lights
        setTrafficLights(prev => prev.map(light => {
          const shouldHijack = activeAmb.route.includes(light.id);
          return shouldHijack ? {
            ...light,
            hijacked: true,
            state: 'green',
            timer: 60
          } : light;
        }));

        setSystemMetrics(prev => ({
          ...prev,
          hijackedLights: activeAmb.route.length,
          activeCorridors: 1
        }));

        setEmergencyLog(prev => [...prev,
          { time: new Date(), message: `AI Decision: ${aiDecision.finalAction} - Green corridor activated`, type: 'success' }
        ]);
      }

      setFsmState('SIGNAL_HIJACKING');
    }, 1000);

    // Activate second ambulance after delay
    setTimeout(() => {
      setAmbulances(prev => prev.map(amb =>
        amb.id === 'AMB002' ? {
          ...amb,
          active: true,
          state: 'DISPATCHED',
          currentRouteIndex: 0
        } : amb
      ));

      setEmergencyLog(prev => [...prev,
        { time: new Date(), message: 'Second emergency detected - AMB002 dispatched', type: 'emergency' }
      ]);

      setFsmState('CORRIDOR_ACTIVE');
    }, 5000);
  };

  // Stop simulation
  const stopSimulation = () => {
    setIsSimulationRunning(false);
    setFsmState('NORMAL_OPERATION');

    // Reset all states
    setTrafficLights(prev => prev.map(light => ({
      ...light,
      hijacked: false,
      state: Math.random() > 0.5 ? 'red' : 'green',
      timer: Math.floor(Math.random() * 40) + 10
    })));

    setAmbulances(prev => prev.map(amb => ({
      ...amb,
      active: false,
      state: 'IDLE',
      currentRouteIndex: 0,
      currentPosition: amb.id === 'AMB001' ?
        { lat: 13.0900, lng: 80.2050 } :
        { lat: 13.0400, lng: 80.2300 }
    })));

    setSystemMetrics(prev => ({
      ...prev,
      hijackedLights: 0,
      activeCorridors: 0
    }));

    setEmergencyLog(prev => [...prev,
      { time: new Date(), message: 'Simulation stopped - System returned to normal operation', type: 'info' }
    ]);
  };

  // Simulation loop
  useEffect(() => {
    if (!isSimulationRunning) return;

    const simulationInterval = setInterval(() => {
      // Update ambulance positions
      setAmbulances(prev => prev.map(moveAmbulanceAlongRoute));

      // Update traffic light timers
      setTrafficLights(prev => prev.map(light => {
        if (light.hijacked) return light;

        let newTimer = light.timer - 1;
        let newState = light.state;

        if (newTimer <= 0) {
          switch (light.state) {
            case 'red':
              newState = 'green';
              newTimer = 25 + Math.floor(Math.random() * 15);
              break;
            case 'green':
              newState = 'yellow';
              newTimer = 3;
              break;
            case 'yellow':
              newState = 'red';
              newTimer = 30 + Math.floor(Math.random() * 20);
              break;
            default:
              newState = 'red';
              newTimer = 30;
          }
        }

        return { ...light, state: newState, timer: newTimer };
      }));

      // Update AI system metrics
      setAiControlSystem(prev => ({
        ...prev,
        episodeCount: prev.episodeCount + 1,
        rewardScore: Math.min(1, prev.rewardScore + 0.001),
        processingTime: 0.2 + Math.random() * 0.2
      }));

    }, 1000);

    return () => clearInterval(simulationInterval);
  }, [isSimulationRunning]);

  // Store ambulance markers for smooth movement
  const ambulanceMarkersRef = useRef({});

  // Update map markers when traffic lights change
  useEffect(() => {
    if (!leafletMapRef.current) return;

    // Clear existing intersection markers
    leafletMapRef.current.eachLayer(layer => {
      if (layer instanceof L.CircleMarker) {
        leafletMapRef.current.removeLayer(layer);
      }
    });

    // Add updated intersection markers
    trafficLights.forEach(intersection => {
      const color = getTrafficLightColor(intersection.state, intersection.hijacked);
      const marker = L.circleMarker([intersection.lat, intersection.lng], {
        radius: intersection.hijacked ? 15 : 12,
        fillColor: color,
        color: intersection.hijacked ? '#2563eb' : '#374151',
        weight: intersection.hijacked ? 3 : 2,
        opacity: 1,
        fillOpacity: intersection.hijacked ? 0.9 : 0.8
      }).addTo(leafletMapRef.current);

      marker.bindPopup(`
        <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 8px;">
          <h4 style="margin: 0 0 8px 0; color: #1f2937; font-size: 14px;">${intersection.name}</h4>
          <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
            <div style="width: 8px; height: 8px; border-radius: 50%; background: ${color};"></div>
            <span style="font-size: 12px; font-weight: 600; color: #374151;">
              ${intersection.state.toUpperCase()}
            </span>
          </div>
          <p style="margin: 0; font-size: 11px; color: #6b7280;">
            Timer: ${intersection.timer}s
          </p>
          ${intersection.hijacked ?
            '<div style="margin-top: 6px; padding: 4px 8px; background: #dbeafe; border-radius: 4px; font-size: 10px; color: #1d4ed8; font-weight: 600;">AI CONTROLLED</div>' :
            ''
          }
        </div>
      `);
    });
  }, [trafficLights]);

  // Update ambulance positions smoothly
  useEffect(() => {
    if (!leafletMapRef.current) return;

    ambulances.forEach(ambulance => {
      if (ambulance.active) {
        if (!ambulanceMarkersRef.current[ambulance.id]) {
          // Create new ambulance marker
          const ambulanceIcon = L.divIcon({
            html: `
              <div class="ambulance-icon" style="
                background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
                border: 3px solid white;
                border-radius: 8px;
                width: 32px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                font-weight: bold;
                box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
                position: relative;
              ">
                🚑
                <div style="
                  position: absolute;
                  top: -2px;
                  right: -2px;
                  width: 8px;
                  height: 8px;
                  background: #10b981;
                  border-radius: 50%;
                  border: 1px solid white;
                  animation: pulse 1s infinite;
                "></div>
              </div>
            `,
            className: 'ambulance-marker-enterprise',
            iconSize: [32, 24],
            iconAnchor: [16, 12]
          });

          const marker = L.marker([
            ambulance.currentPosition.lat,
            ambulance.currentPosition.lng
          ], { icon: ambulanceIcon }).addTo(leafletMapRef.current);

          marker.bindPopup(`
            <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; padding: 12px; min-width: 200px;">
              <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                <div style="font-size: 16px;">🚑</div>
                <h4 style="margin: 0; color: #1f2937; font-size: 14px; font-weight: 600;">${ambulance.id}</h4>
              </div>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; font-size: 11px;">
                <div>
                  <span style="color: #6b7280;">Status:</span>
                  <div style="font-weight: 600; color: #dc2626;">${ambulance.state.replace('_', ' ')}</div>
                </div>
                <div>
                  <span style="color: #6b7280;">Priority:</span>
                  <div style="font-weight: 600; color: ${ambulance.priority === 'CRITICAL' ? '#dc2626' : '#d97706'};">${ambulance.priority}</div>
                </div>
                <div style="grid-column: 1 / -1;">
                  <span style="color: #6b7280;">Destination:</span>
                  <div style="font-weight: 600; color: #374151;">${ambulance.destination.name}</div>
                </div>
                <div>
                  <span style="color: #6b7280;">Speed:</span>
                  <div style="font-weight: 600; color: #059669;">${ambulance.speed} km/h</div>
                </div>
                <div>
                  <span style="color: #6b7280;">ETA:</span>
                  <div style="font-weight: 600; color: #7c3aed;">3.2 min</div>
                </div>
              </div>
            </div>
          `);

          ambulanceMarkersRef.current[ambulance.id] = marker;
        } else {
          // Update existing marker position
          const marker = ambulanceMarkersRef.current[ambulance.id];
          marker.setLatLng([ambulance.currentPosition.lat, ambulance.currentPosition.lng]);
        }
      } else {
        // Remove inactive ambulance markers
        if (ambulanceMarkersRef.current[ambulance.id]) {
          leafletMapRef.current.removeLayer(ambulanceMarkersRef.current[ambulance.id]);
          delete ambulanceMarkersRef.current[ambulance.id];
        }
      }
    });
  }, [ambulances]);





  return (
    <div className="traffic-hijacking">
      <div className="enterprise-header">
        <div className="header-content">
          <div className="header-info">
            <h1>Emergency Traffic Management System</h1>
            <p>AI-powered traffic signal control for emergency vehicle prioritization</p>
          </div>
          <div className="header-status">
            <div className="system-status">
              <div className="status-indicator">
                <div className={`status-dot ${isSimulationRunning ? 'active' : 'standby'}`}></div>
                <span className="status-text">
                  {isSimulationRunning ? 'Emergency Active' : 'System Standby'}
                </span>
              </div>
              <div className="fsm-status">
                <span className="fsm-label">Current State:</span>
                <span className={`fsm-value ${fsmState.toLowerCase().replace('_', '-')}`}>
                  {fsmState.replace('_', ' ')}
                </span>
              </div>
            </div>
            <div className="control-panel">
              <button
                className={`enterprise-btn ${isSimulationRunning ? 'danger' : 'primary'}`}
                onClick={isSimulationRunning ? stopSimulation : startSimulation}
              >
                {isSimulationRunning ? 'Stop Emergency' : 'Activate Emergency'}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="enterprise-content">
        {/* Left Panel - System Overview */}
        <div className="left-panel">
          <div className="enterprise-panel">
            <div className="panel-header">
              <h3>System Overview</h3>
            </div>
            <div className="metrics-container">
              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">🚦</span>
                  <span className="metric-title">Signal Control</span>
                </div>
                <div className="metric-value">{systemMetrics.hijackedLights}/{systemMetrics.totalLights}</div>
                <div className="metric-label">Controlled Intersections</div>
              </div>
              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">🛣️</span>
                  <span className="metric-title">Green Corridors</span>
                </div>
                <div className="metric-value">{systemMetrics.activeCorridors}</div>
                <div className="metric-label">Active Routes</div>
              </div>
              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">⚡</span>
                  <span className="metric-title">System Efficiency</span>
                </div>
                <div className="metric-value">{systemMetrics.swarmEfficiency}%</div>
                <div className="metric-label">Performance Rating</div>
              </div>
              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">💡</span>
                  <span className="metric-title">Energy Optimization</span>
                </div>
                <div className="metric-value">{systemMetrics.energySavings}%</div>
                <div className="metric-label">Power Reduction</div>
              </div>
            </div>
          </div>

          <div className="enterprise-panel">
            <div className="panel-header">
              <h3>AI Control Engine</h3>
              <div className="control-mode-selector">
                <select
                  value={aiControlSystem.mode}
                  onChange={(e) => setAiControlSystem(prev => ({...prev, mode: e.target.value}))}
                  className="enterprise-select"
                >
                  <option value="RULE_BASED_FSM">Rule-Based FSM</option>
                  <option value="DEEP_RL">Deep Learning</option>
                  <option value="HYBRID">Hybrid AI</option>
                </select>
              </div>
            </div>
            <div className="ai-performance-grid">
              <div className="performance-metric">
                <div className="metric-label">Active Agents</div>
                <div className="metric-value">{aiControlSystem.activeAgents}</div>
              </div>
              <div className="performance-metric">
                <div className="metric-label">Response Time</div>
                <div className="metric-value">{aiControlSystem.processingTime.toFixed(2)}s</div>
              </div>
              <div className="performance-metric">
                <div className="metric-label">Accuracy</div>
                <div className="metric-value">{aiControlSystem.decisionAccuracy}%</div>
              </div>
              <div className="performance-metric">
                <div className="metric-label">Learning Episodes</div>
                <div className="metric-value">{aiControlSystem.episodeCount.toLocaleString()}</div>
              </div>
            </div>
            <div className="capabilities-list">
              <div className="capability-item">
                <div className="capability-icon">⚡</div>
                <div className="capability-content">
                  <div className="capability-title">Real-time Processing</div>
                  <div className="capability-desc">Sub-second decision making</div>
                </div>
              </div>
              <div className="capability-item">
                <div className="capability-icon">🔗</div>
                <div className="capability-content">
                  <div className="capability-title">Distributed Architecture</div>
                  <div className="capability-desc">Fault-tolerant system design</div>
                </div>
              </div>
              <div className="capability-item">
                <div className="capability-icon">🎯</div>
                <div className="capability-content">
                  <div className="capability-title">Predictive Analytics</div>
                  <div className="capability-desc">Traffic pattern optimization</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Center Panel - Traffic Control Map */}
        <div className="center-panel">
          <div className="enterprise-panel map-panel">
            <div className="panel-header">
              <h3>Traffic Control Center</h3>
              <div className="map-controls">
                <div className="control-group">
                  <span className="control-label">View:</span>
                  <button className="map-control-btn active">Traffic</button>
                  <button className="map-control-btn">Satellite</button>
                </div>
                <div className="control-group">
                  <span className="control-label">Layers:</span>
                  <button className="map-control-btn active">Signals</button>
                  <button className="map-control-btn active">Routes</button>
                </div>
              </div>
            </div>
            <div className="map-container">
              <div
                ref={mapRef}
                className="enterprise-map"
                style={{ height: '100%', width: '100%' }}
              />
              <div className="map-overlay">
                <div className="map-legend">
                  <div className="legend-header">
                    <h4>Map Legend</h4>
                  </div>
                  <div className="legend-items">
                    <div className="legend-item">
                      <div className="legend-indicator red-signal"></div>
                      <span>Red Signal</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-indicator yellow-signal"></div>
                      <span>Yellow Signal</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-indicator green-signal"></div>
                      <span>Green Signal</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-indicator ai-controlled"></div>
                      <span>AI Controlled</span>
                    </div>
                    <div className="legend-item">
                      <div className="legend-indicator emergency-vehicle"></div>
                      <span>Emergency Vehicle</span>
                    </div>
                  </div>
                </div>
                <div className="system-state-display">
                  <div className="state-header">System State</div>
                  <div className={`state-value ${fsmState.toLowerCase().replace('_', '-')}`}>
                    {fsmState.replace('_', ' ')}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Analytics & Operations */}
        <div className="right-panel">
          <div className="enterprise-panel">
            <div className="panel-header">
              <h3>Performance Analytics</h3>
            </div>
            <div className="analytics-grid">
              <div className="analytics-card">
                <div className="analytics-header">
                  <span className="analytics-title">Traffic Flow</span>
                  <span className="analytics-trend positive">↗ +5.2%</span>
                </div>
                <div className="analytics-value">{systemMetrics.trafficFlow}%</div>
                <div className="analytics-chart">
                  <div className="chart-bar" style={{ height: `${systemMetrics.trafficFlow}%` }}></div>
                </div>
              </div>
              <div className="analytics-card">
                <div className="analytics-header">
                  <span className="analytics-title">Average Delay</span>
                  <span className="analytics-trend negative">↘ -12.8%</span>
                </div>
                <div className="analytics-value">{systemMetrics.averageDelay}s</div>
                <div className="analytics-chart">
                  <div className="chart-bar" style={{ height: `${Math.max(20, 100 - systemMetrics.averageDelay)}%` }}></div>
                </div>
              </div>
            </div>
          </div>

          <div className="enterprise-panel">
            <div className="panel-header">
              <h3>System Capabilities</h3>
            </div>
            <div className="capabilities-grid">
              <div className="capability-card">
                <div className="capability-icon">⚡</div>
                <div className="capability-content">
                  <div className="capability-title">Autonomous Operation</div>
                  <div className="capability-metric">99.8% Uptime</div>
                </div>
              </div>
              <div className="capability-card">
                <div className="capability-icon">🚀</div>
                <div className="capability-content">
                  <div className="capability-title">Response Time</div>
                  <div className="capability-metric">&lt; 300ms</div>
                </div>
              </div>
              <div className="capability-card">
                <div className="capability-icon">🌐</div>
                <div className="capability-content">
                  <div className="capability-title">Network Coverage</div>
                  <div className="capability-metric">City-wide</div>
                </div>
              </div>
              <div className="capability-card">
                <div className="capability-icon">💡</div>
                <div className="capability-content">
                  <div className="capability-title">Energy Savings</div>
                  <div className="capability-metric">23.7%</div>
                </div>
              </div>
            </div>
          </div>

          <div className="enterprise-panel">
            <div className="panel-header">
              <h3>System Activity Log</h3>
            </div>
            <div className="activity-log">
              {emergencyLog.slice(-6).reverse().map((entry, index) => (
                <div key={index} className={`activity-entry ${entry.type}`}>
                  <div className="activity-time">{entry.time.toLocaleTimeString()}</div>
                  <div className="activity-content">
                    <div className={`activity-indicator ${entry.type}`}></div>
                    <span className="activity-message">{entry.message}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrafficSignalHijacking;
