.ar-navigation-container {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ar-navigation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 25px;
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ar-navigation-header h1 {
  color: var(--text-primary);
  margin: 0;
  font-size: 32px;
  font-weight: 700;
}

.header-time {
  color: var(--text-secondary);
  font-size: 18px;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.ar-setup-section {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
  margin-bottom: 30px;
}

.ar-intro h2 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 26px;
  font-weight: 700;
}

.ar-intro p {
  color: var(--text-secondary);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 30px;
}

.ar-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.ar-feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
}

.ar-feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.ar-feature-card h3 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 18px;
  font-weight: 600;
}

.ar-feature-card p {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

.emergency-info {
  background: rgba(220, 38, 38, 0.1);
  border: 2px solid rgba(220, 38, 38, 0.2);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
}

.emergency-info h3 {
  color: var(--text-primary);
  margin: 0 0 20px 0;
  font-size: 20px;
  font-weight: 700;
}

.emergency-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.emergency-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid rgba(220, 38, 38, 0.2);
}

.emergency-item:last-child {
  border-bottom: none;
}

.emergency-item .label {
  font-weight: 600;
  color: #dc2626;
}

.emergency-item .value {
  color: var(--text-primary);
  font-weight: 500;
}

.emergency-item .value.critical {
  color: #dc2626;
  font-weight: 700;
  text-transform: uppercase;
}

.start-ar-btn {
  width: 100%;
  padding: 20px;
  background: linear-gradient(135deg, #7c3aed, #a855f7);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.start-ar-btn:hover {
  background: linear-gradient(135deg, #6d28d9, #9333ea);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
}

.ar-active-section {
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 30px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
}

.ar-camera-container {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
}

.ar-camera-feed {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ar-overlay-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ar-ui-overlays {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.ar-ui-overlays > * {
  pointer-events: auto;
}

.ar-status-bar {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 12px 20px;
  color: white;
}

.emergency-status {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.critical {
  background: #dc2626;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.ar-time {
  font-family: 'Courier New', monospace;
  font-weight: 700;
}

.ar-navigation-info {
  position: absolute;
  top: 80px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px;
  color: white;
}

.nav-instruction {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.instruction-icon {
  font-size: 24px;
}

.instruction-text {
  font-size: 18px;
  font-weight: 600;
}

.nav-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 15px;
}

.stat {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #ccc;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 700;
  color: #00ff88;
}

.ar-controls {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.ar-control-btn {
  padding: 12px 16px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.ar-control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.ar-control-btn.active {
  background: rgba(59, 130, 246, 0.8);
  border-color: #3b82f6;
}

.ar-control-btn.stop-btn {
  background: rgba(220, 38, 38, 0.8);
  border-color: #dc2626;
}

.ar-control-btn.stop-btn:hover {
  background: rgba(220, 38, 38, 1);
}

.ar-tech-specs {
  background: var(--panel-bg);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 30px;
}

.ar-tech-specs h3 {
  color: var(--text-primary);
  margin: 0 0 25px 0;
  font-size: 24px;
  font-weight: 700;
}

.tech-specs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.spec-item {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
}

.spec-item h4 {
  color: var(--text-primary);
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  padding-bottom: 10px;
  border-bottom: 2px solid #7c3aed;
}

.spec-item ul {
  margin: 0;
  padding-left: 20px;
  color: var(--text-secondary);
}

.spec-item li {
  margin-bottom: 8px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .ar-navigation-container {
    padding: 15px;
  }
  
  .ar-navigation-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .ar-camera-container {
    height: 400px;
  }
  
  .ar-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .ar-control-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .emergency-details {
    grid-template-columns: 1fr;
  }
  
  .nav-stats {
    grid-template-columns: repeat(3, 1fr);
  }
}
