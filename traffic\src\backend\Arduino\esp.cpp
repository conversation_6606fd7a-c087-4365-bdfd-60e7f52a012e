#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>

const char* ssid = "YourWiFiSSID";
const char* password = "YourWiFiPassword";

ESP8266WebServer server(80);

// Relay connected to GPIO0 or GPIO2 depending on module wiring
const int RELAY_PIN = 2;

void setup() {
  Serial.begin(115200);
  WiFi.begin(ssid, password);

  pinMode(RELAY_PIN, OUTPUT);
  digitalWrite(RELAY_PIN, HIGH); // Relay OFF (LOW = ON for active-low relay)

  Serial.println("Connecting to WiFi...");
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }

  Serial.println("");
  Serial.print("Connected to WiFi. IP: ");
  Serial.println(WiFi.localIP());

  server.on("/on", []() {
    digitalWrite(RELAY_PIN, LOW); // Turn ON relay
    server.send(200, "text/plain", "Relay ON");
  });

  server.on("/off", []() {
    digitalWrite(RELAY_PIN, HIGH); // Turn OFF relay
    server.send(200, "text/plain", "Relay OFF");
  });

  server.begin();
}

void loop() {
  server.handleClient();
}
